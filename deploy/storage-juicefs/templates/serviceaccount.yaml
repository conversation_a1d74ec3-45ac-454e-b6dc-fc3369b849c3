{{- if .Values.serviceAccount.create -}}
{{- if .Values.s3gateway.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "storage-juicefs.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "storage-juicefs.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
{{- end }}
{{- end }}
