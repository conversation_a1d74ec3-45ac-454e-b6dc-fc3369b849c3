{{- if .Values.secret.create -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "storage-juicefs.secretName" . }}-secret
  namespace: {{ .Release.Namespace }}
  {{- with .Values.secret.annotations }}
  annotations: {{- . | toYaml | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "storage-juicefs.name" . }}
    chart: {{ include "storage-juicefs.chart" . }}
    release: {{ .Release.Name }}
type: Opaque
data:
  name: {{ .Values.secret.fsname }}
  metaurl: {{ .Values.secret.metaurl }}
  storage: {{ .Values.secret.storage }}
  bucket: {{ .Values.secret.bucket }}
  access-key: {{ .Values.secret.accessKey }}
  secret-key: {{ .Values.secret.secretKey }}
  trash-days: {{ .Values.secret.trashDays }}
  format-options: {{ .Values.secret.formatOptions }}
{{- end -}}
