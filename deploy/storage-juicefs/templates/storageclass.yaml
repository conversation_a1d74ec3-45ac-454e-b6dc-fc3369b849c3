{{- if .Values.storageClass.create -}}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ .Release.Name }}
{{- if .Values.storageClass.annotations }}
  annotations:
{{ toYaml .Values.storageClass.annotations | indent 4 }}
{{- end }}
  labels:
    app: {{ include "storage-juicefs.name" . }}
    chart: {{ include "storage-juicefs.chart" . }}
    release: {{ .Release.Name }}
provisioner: {{ .Values.driverName }}
parameters:
  csi.storage.k8s.io/provisioner-secret-name: {{ include "storage-juicefs.secretName" . }}-secret
  csi.storage.k8s.io/provisioner-secret-namespace: {{ .Release.Namespace }}
  csi.storage.k8s.io/controller-expand-secret-name: {{ include "storage-juicefs.secretName" . }}-secret
  csi.storage.k8s.io/controller-expand-secret-namespace: {{ .Release.Namespace }}
  csi.storage.k8s.io/node-stage-secret-name: {{ include "storage-juicefs.secretName" . }}-secret
  csi.storage.k8s.io/node-stage-secret-namespace: {{ .Release.Namespace }}
{{- if .Values.storageClass.pathPattern }}
  pathPattern: {{ .Values.storageClass.pathPattern }}
{{- end }}
{{- with .Values.storageClass.mountPod }}
{{- if .resources.limits }}
{{- if .resources.limits.cpu }}
  juicefs/mount-cpu-limit: {{ .resources.limits.cpu | quote }}
{{- end }}
{{- if .resources.limits.memory }}
  juicefs/mount-memory-limit: {{ .resources.limits.memory | quote }}
{{- end }}
{{- end }}
{{- if .resources.requests }}
{{- if .resources.requests.cpu }}
  juicefs/mount-cpu-request: {{ .resources.requests.cpu | quote }}
{{- end }}
 {{- if .resources.requests.memory }}
  juicefs/mount-memory-request: {{ .resources.requests.memory | quote }}
{{- end }}
{{- end }}
{{- if .image }}
  juicefs/mount-image: {{ .image }}
{{- end }}
{{- if .annotations }}
  juicefs/mount-annotations: {{ toJson .annotations | quote }}
{{- end }}
{{- end }}

reclaimPolicy: {{ .Values.storageClass.reclaimPolicy }}
volumeBindingMode: Immediate
allowVolumeExpansion: {{ .Values.storageClass.allowVolumeExpansion }}
{{- if .Values.storageClass.mountOptions }}
mountOptions:
  {{- range .Values.storageClass.mountOptions }}
  - {{ . }}
  {{- end }}
{{- end }}
{{- end -}}
