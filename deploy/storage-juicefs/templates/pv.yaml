apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ include "storage-juicefs.fullname" . }}-static-pv
spec:
  capacity:
    storage: 1Pi
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  csi:
    driver: {{ .Values.driverName }}
    volumeHandle: {{ include "storage-juicefs.fullname" . }}-static-pv
    fsType: juicefs
    nodeStageSecretRef:
      name: {{ include "storage-juicefs.secretName" . }}-secret
      namespace: {{ .Release.Namespace }}
  persistentVolumeReclaimPolicy: Retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "storage-juicefs.fullname" . }}-static-pvc
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Pi
  volumeName: {{ include "storage-juicefs.fullname" . }}-static-pv
  storageClassName: ""