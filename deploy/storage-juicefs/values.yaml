imagePullSecrets: []
image:
  repository: nginx
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
podSecurityContext: {}
  # fsGroup: 2000
driverName: "csi.juicefs.com"

nodeSelector: {}
tolerations: []
affinity: {}
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

storageClass:
  create: true
  annotations: {}
  reclaimPolicy: Retain
  allowVolumeExpansion: true
  mountOptions: []
  pathPattern: ""
  mountPod:
    # Mount pod resource requests & limits
    resources:
      limits:
        cpu: 5000m
        memory: 5Gi
      requests:
        cpu: 1000m
        memory: 1Gi
    # Override mount pod image, ref: https://juicefs.com/docs/csi/guide/custom-image
    image: ""
    # Set annotations for the mount pod
    annotations: {}
  

secret:
  # Specifies whether the secret should be created
  create: true
  annotations: {}
  # The JuiceFS file system name
  fsname: ""
  # Connection URL for metadata engine (e.g. Redis), for community edition use only, ref: https://juicefs.com/docs/community/databases_for_metadata
  metaurl: ""
  # Object storage type, such as s3, gs, oss, for community edition use only, ref: https://juicefs.com/docs/community/how_to_setup_object_storage
  storage: ""
  # Bucket URL, for community edition use only, ref: https://juicefs.com/docs/community/how_to_setup_object_storage
  bucket: ""
  # Token for JuiceFS Enterprise Edition token, ref: https://juicefs.com/docs/cloud/acl
  token: ""
  # Access key for object storage
  accessKey: ""
  # Secret key for object storage
  secretKey: ""
  # The number of days which files are kept in the trash, for community edition use only, ref: https://juicefs.com/docs/community/security/trash
  trashDays: ""
  # Options passed to the "juicefs format" or "juicefs auth" command, depending on which edition you're using
  # Example: block-size=4096,capacity=10
  # Ref: https://juicefs.com/docs/community/command_reference#format and https://juicefs.com/docs/cloud/reference/commands_reference#auth
  formatOptions: ""

s3gateway:
  create: true
  replicaCount: 1


service:
  type: ClusterIP
  port: 80

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

livenessProbe:
  httpGet:
    path: /
    port: http
readinessProbe:
  httpGet:
    path: /
    port: http

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

ingress:
  enabled: true
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local







