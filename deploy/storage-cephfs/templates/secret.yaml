{{- if .Values.secret.create -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "storage-cephfs.secretName" . }}-secret
  namespace: {{ .Release.Namespace }}
  {{- with .Values.secret.annotations }}
  annotations: {{- . | toYaml | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "storage-cephfs.name" . }}
    chart: {{ include "storage-cephfs.chart" . }}
    release: {{ .Release.Name }}
stringData:
  userID: {{ .Values.secret.userID }}
  userKey: {{ .Values.secret.userKey }}
{{- end -}}
