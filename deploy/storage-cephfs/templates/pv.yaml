apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ include "storage-cephfs.fullname" . }}-static-pv
spec:
  capacity:
    storage: 1Pi
  accessModes:
    - ReadWriteMany
  csi:
    driver: {{ .Values.driverName }}
    volumeHandle: {{ include "storage-cephfs.fullname" . }}-static-pv
    volumeAttributes:
      monitors: {{ .Values.storageClass.monitors }}
      clusterID: {{ .Values.storageClass.clusterID }}
      fsName: {{ .Values.storageClass.fsName }}
      staticVolume: "true"
      rootPath: /
      mounter: {{ .Values.storageClass.mounter }}
    nodeStageSecretRef:
      name: {{ include "storage-cephfs.secretName" . }}-secret
      namespace: {{ .Release.Namespace }}
  persistentVolumeReclaimPolicy: Retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "storage-cephfs.fullname" . }}-static-pvc
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Pi
  volumeName: {{ include "storage-cephfs.fullname" . }}-static-pv
  storageClassName: ""