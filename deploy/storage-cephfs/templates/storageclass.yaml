{{- if .Values.storageClass.create -}}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ .Release.Name }}
{{- if .Values.storageClass.annotations }}
  annotations:
{{ toYaml .Values.storageClass.annotations | indent 4 }}
{{- end }}
  labels:
    app: {{ include "storage-cephfs.name" . }}
    chart: {{ include "storage-cephfs.chart" . }}
    release: {{ .Release.Name }}
provisioner: {{ .Values.driverName }}
parameters:
  clusterID: {{ .Values.storageClass.clusterID }}
  fsName: {{ .Values.storageClass.fsName }}
{{- if .Values.storageClass.pool }}
  pool: {{ .Values.storageClass.pool }}
{{- end }}
{{- if .Values.storageClass.encrypted }}
  encrypted: "{{ .Values.storageClass.encrypted }}"
{{- end }}
{{- if .Values.storageClass.encryptionKMSID }}
  encryptionKMSID: {{ .Values.storageClass.encryptionKMSID }}
{{- end }}
{{- if .Values.storageClass.fuseMountOptions }}
  fuseMountOptions: "{{ .Values.storageClass.fuseMountOptions }}"
{{- end }}
{{- if .Values.storageClass.kernelMountOptions }}
  kernelMountOptions: "{{ .Values.storageClass.kernelMountOptions }}"
{{- end }}
{{- if .Values.storageClass.mounter }}
  mounter: "{{ .Values.storageClass.mounter }}"
{{- end }}
{{- if .Values.storageClass.volumeNamePrefix }}
  volumeNamePrefix: "{{ .Values.storageClass.volumeNamePrefix }}"
{{- end }}
  csi.storage.k8s.io/provisioner-secret-name: {{ include "storage-cephfs.secretName" . }}-secret
{{- if .Values.storageClass.provisionerSecretNamespace }}
  csi.storage.k8s.io/provisioner-secret-namespace: {{ .Values.storageClass.provisionerSecretNamespace }}
{{ else }}
  csi.storage.k8s.io/provisioner-secret-namespace: {{ .Release.Namespace }}
{{- end }}
  csi.storage.k8s.io/controller-expand-secret-name: {{ include "storage-cephfs.secretName" . }}-secret
{{- if .Values.storageClass.controllerExpandSecretNamespace }}
  csi.storage.k8s.io/controller-expand-secret-namespace: {{ .Values.storageClass.controllerExpandSecretNamespace }}
{{ else }}
  csi.storage.k8s.io/controller-expand-secret-namespace: {{ .Release.Namespace }}
{{- end }}
  csi.storage.k8s.io/node-stage-secret-name: {{ include "storage-cephfs.secretName" . }}-secret
{{- if .Values.storageClass.nodeStageSecretNamespace }}
  csi.storage.k8s.io/node-stage-secret-namespace: {{ .Values.storageClass.nodeStageSecretNamespace }}
{{ else }}
  csi.storage.k8s.io/node-stage-secret-namespace: {{ .Release.Namespace }}
{{- end }}
reclaimPolicy: {{ .Values.storageClass.reclaimPolicy }}
volumeBindingMode: Immediate
allowVolumeExpansion: {{ .Values.storageClass.allowVolumeExpansion }}
{{- if .Values.storageClass.mountOptions }}
mountOptions:
  {{- range .Values.storageClass.mountOptions }}
  - {{ . }}
  {{- end }}
{{- end }}
{{- end -}}
