---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: jobs.pai.xiaoshiai.cn
spec:
  group: pai.xiaoshiai.cn
  names:
    kind: Job
    listKind: JobList
    plural: jobs
    singular: job
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Job phase
      jsonPath: .status.state.phase
      name: Phase
      type: string
    - description: Job replicas
      jsonPath: .status.replicas
      name: Replicas
      type: integer
    name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              extensions:
                items:
                  properties:
                    config:
                      additionalProperties:
                        type: string
                      type: object
                    kind:
                      type: string
                  type: object
                type: array
              historyLimit:
                type: integer
              paused:
                type: boolean
              priorityName:
                type: string
              queueName:
                type: string
              replicas:
                description: |-
                  replicas is the number of desired replicas of the last role if it is specified.
                  the design proposal is to support HPA on the last role.
                format: int32
                type: integer
              roles:
                items:
                  properties:
                    affinity:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0Affinity'
                    annotations:
                      additionalProperties:
                        type: string
                      type: object
                    args:
                      items:
                        type: string
                      type: array
                    command:
                      items:
                        type: string
                      type: array
                    configs:
                      items:
                        properties:
                          path:
                            type: string
                          value:
                            type: string
                        type: object
                      type: array
                    env:
                      items:
                        $ref: '#/definitions/k8s.io~1api~1core~1v1~0EnvVar'
                      type: array
                    hostNetwork:
                      type: boolean
                    image:
                      type: string
                    imagePullPolicy:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0PullPolicy'
                    imagePullSecrets:
                      items:
                        $ref: '#/definitions/k8s.io~1api~1core~1v1~0LocalObjectReference'
                      type: array
                    labels:
                      additionalProperties:
                        type: string
                      type: object
                    lifecycle:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0Lifecycle'
                    mounts:
                      items:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          name:
                            description: Name is the name of pvc
                            type: string
                          path:
                            type: string
                          readonly:
                            type: boolean
                        type: object
                      type: array
                    name:
                      type: string
                    nodeSelector:
                      additionalProperties:
                        type: string
                      type: object
                    podSpec:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0PodSpec'
                    ports:
                      items:
                        description: |-
                          JobPort defines a port for a job role.
                          It a limited version of [corev1.ContainerPort]
                        properties:
                          name:
                            type: string
                          port:
                            format: int32
                            type: integer
                          protocol:
                            default: TCP
                            type: string
                        type: object
                      type: array
                    privileged:
                      type: boolean
                    replicas:
                      format: int32
                      type: integer
                    resourceFlavor:
                      type: string
                    resources:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceRequirements'
                    restartPolicy:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0RestartPolicy'
                    shmSize:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    workingDir:
                      type: string
                  type: object
                type: array
              suspend:
                type: boolean
              ttl:
                type: string
            type: object
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0ConditionStatus'
                    type:
                      type: string
                  type: object
                type: array
              history:
                items:
                  properties:
                    finishTimestamp:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    phase:
                      type: string
                    reason:
                      type: string
                    runningTimestamp:
                      format: date-time
                      type: string
                    startTimestamp:
                      format: date-time
                      type: string
                  type: object
                type: array
              message:
                type: string
              observedGeneration:
                format: int64
                type: integer
              replicas:
                format: int32
                type: integer
              roles:
                items:
                  properties:
                    name:
                      type: string
                    ports:
                      items:
                        properties:
                          access:
                            properties:
                              host:
                                type: string
                              password:
                                type: string
                              type:
                                type: string
                              username:
                                type: string
                            type: object
                          name:
                            type: string
                          port:
                            format: int32
                            type: integer
                        type: object
                      type: array
                    replicas:
                      properties:
                        current:
                          format: int32
                          type: integer
                        desired:
                          format: int32
                          type: integer
                        running:
                          format: int32
                          type: integer
                      type: object
                    resources:
                      $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceRequirements'
                  type: object
                type: array
              selector:
                type: string
              state:
                properties:
                  finishTimestamp:
                    format: date-time
                    type: string
                  lastTransitionTime:
                    format: date-time
                    type: string
                  phase:
                    type: string
                  reason:
                    type: string
                  runningTimestamp:
                    format: date-time
                    type: string
                  startTimestamp:
                    format: date-time
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.replicas
      status: {}
