---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: queues.cloud.xiaoshiai.cn
spec:
  group: cloud.xiaoshiai.cn
  names:
    kind: Queue
    listKind: QueueList
    plural: queues
    singular: queue
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              guaranteed:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceList'
              limit:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceList'
              reclaimPolicy:
                type: string
              request:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceList'
            type: object
          status:
            properties:
              allocated:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ResourceList'
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
