---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: storageclusters.pai.xiaoshiai.cn
spec:
  group: pai.xiaoshiai.cn
  names:
    kind: StorageCluster
    listKind: StorageClusterList
    plural: storageclusters
    singular: storagecluster
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              chart:
                type: string
              provider:
                description: Provisioner StorageClusterProvisioner `json:"provisioner"`
                type: string
              repository:
                type: string
              values:
                type: object
                x-kubernetes-preserve-unknown-fields: true
              version:
                type: string
            type: object
          status:
            properties:
              clusterReference:
                description: ObjectReference contains enough information to let you
                  inspect or modify the referred object.
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              message:
                description: Message is the status message of the storage cluster
                type: string
              objects:
                items:
                  description: ObjectReference contains enough information to let
                    you inspect or modify the referred object.
                  properties:
                    apiVersion:
                      description: API version of the referent.
                      type: string
                    fieldPath:
                      description: |-
                        If referring to a piece of an object instead of an entire object, this string
                        should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                        For example, if the object reference is to a container within a pod, this would take on a value like:
                        "spec.containers{name}" (where "name" refers to the name of the container that triggered
                        the event) or if no container name is specified "spec.containers[2]" (container with
                        index 2 in this pod). This syntax is chosen only to have some well-defined way of
                        referencing a part of an object.
                      type: string
                    kind:
                      description: |-
                        Kind of the referent.
                        More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                      type: string
                    name:
                      description: |-
                        Name of the referent.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    namespace:
                      description: |-
                        Namespace of the referent.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                      type: string
                    resourceVersion:
                      description: |-
                        Specific resourceVersion to which this reference is made, if any.
                        More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                      type: string
                    uid:
                      description: |-
                        UID of the referent.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                      type: string
                  type: object
                  x-kubernetes-map-type: atomic
                type: array
                x-kubernetes-list-type: atomic
              phase:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
