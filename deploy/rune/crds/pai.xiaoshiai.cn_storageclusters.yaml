---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: storageclusters.pai.xiaoshiai.cn
spec:
  group: pai.xiaoshiai.cn
  names:
    kind: StorageCluster
    listKind: StorageClusterList
    plural: storageclusters
    singular: storagecluster
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              chart:
                type: string
              provider:
                description: Provisioner StorageClusterProvisioner `json:"provisioner"`
                type: string
              repository:
                type: string
              values:
                type: object
                x-kubernetes-preserve-unknown-fields: true
              version:
                type: string
            type: object
          status:
            properties:
              clusterReference:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ObjectReference'
              message:
                description: Message is the status message of the storage cluster
                type: string
              objects:
                items:
                  $ref: '#/definitions/k8s.io~1api~1core~1v1~0ObjectReference'
                type: array
                x-kubernetes-list-type: atomic
              phase:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
