---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: clusterresourcequotas.cloud.xiaoshiai.cn
spec:
  group: cloud.xiaoshiai.cn
  names:
    kind: ClusterResourceQuota
    listKind: ClusterResourceQuotaList
    plural: clusterresourcequotas
    singular: clusterresourcequota
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Resource Request
      jsonPath: .status.used
      name: Request
      type: string
    - description: Resource Limit
      jsonPath: .status.hard
      name: Lmit
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the behavior of the License.
            properties:
              hard:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: |-
                  hard is the set of desired hard limits for each named resource.
                  More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/
                type: object
              scopeSelector:
                description: |-
                  scopeSelector is also a collection of filters like scopes that must match each object tracked by a quota
                  but expressed using ScopeSelectorOperator in combination with possible values.
                  For a resource to match, both scopes AND scopeSelector (if specified in spec), must be matched.
                properties:
                  matchExpressions:
                    description: A list of scope selector requirements by scope of
                      the resources.
                    items:
                      description: |-
                        A scoped-resource selector requirement is a selector that contains values, a scope name, and an operator
                        that relates the scope name and values.
                      properties:
                        operator:
                          description: |-
                            Represents a scope's relationship to a set of values.
                            Valid operators are In, NotIn, Exists, DoesNotExist.
                          type: string
                        scopeName:
                          description: The name of the scope that the selector applies
                            to.
                          type: string
                        values:
                          description: |-
                            An array of string values. If the operator is In or NotIn,
                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                            the values array must be empty.
                            This array is replaced during a strategic merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - operator
                      - scopeName
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
                x-kubernetes-map-type: atomic
              scopes:
                description: |-
                  A collection of filters that must match each object tracked by a quota.
                  If not specified, the quota matches all objects.
                items:
                  description: A ResourceQuotaScope defines a filter that must match
                    each object tracked by a quota
                  type: string
                type: array
                x-kubernetes-list-type: atomic
              selector:
                description: selector is the selector that is used to select namespaces
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: |-
                        A label selector requirement is a selector that contains values, a key, and an operator that
                        relates the key and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: |-
                            operator represents a key's relationship to a set of values.
                            Valid operators are In, NotIn, Exists and DoesNotExist.
                          type: string
                        values:
                          description: |-
                            values is an array of string values. If the operator is In or NotIn,
                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                            the values array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: |-
                      matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                      map is equivalent to an element of matchExpressions, whose key field is "key", the
                      operator is "In", and the values array contains only "value". The requirements are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
            type: object
          status:
            description: Status describes the current status of a License.
            properties:
              hard:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: |-
                  Hard is the set of enforced hard limits for each named resource.
                  More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/
                type: object
              namespaces:
                description: Namespaces is the list of namespaces on which the resource
                  quota is applied
                items:
                  properties:
                    name:
                      description: Name is the name of the namespace
                      type: string
                    used:
                      additionalProperties:
                        anyOf:
                        - type: integer
                        - type: string
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      description: ResourceList is a set of (resource name, quantity)
                        pairs.
                      type: object
                  required:
                  - name
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - name
                x-kubernetes-list-type: map
              used:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: Used is the current observed total usage of the resource
                  in the namespace.
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
