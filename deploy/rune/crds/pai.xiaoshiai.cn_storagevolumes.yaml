---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: storagevolumes.pai.xiaoshiai.cn
spec:
  group: pai.xiaoshiai.cn
  names:
    kind: StorageVolume
    listKind: StorageVolumeList
    plural: storagevolumes
    singular: storagevolume
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              capability:
                anyOf:
                - type: integer
                - type: string
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              clusterReference:
                $ref: '#/definitions/k8s.io~1api~1core~1v1~0ObjectReference'
            type: object
          status:
            properties:
              accounts:
                items:
                  properties:
                    accessKey:
                      type: string
                    role:
                      type: string
                    secretKey:
                      type: string
                  type: object
                type: array
              storagePath:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
