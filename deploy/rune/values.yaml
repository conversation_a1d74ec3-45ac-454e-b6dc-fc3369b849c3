global:
  imageRegistry: "registry.cn-hangzhou.aliyuncs.com"
  imageRepository: "xiaoshiai"
  imagePullSecrets: []
  storageClass:
  security:
    # If you want to use insecure images, set this to true.
    # This is not recommended for production environments.
    # If you set this to true, you must also set the global.imagePullSecrets to an empty list.
    # Otherwise, the image pull will fail.
    allowInsecureImages: true
api:
  image:
    registry: registry.cn-hangzhou.aliyuncs.com
    repository: xiaoshiai/rune
    tag: latest
    pullPolicy: ""
    pullSecrets: []
  replicaCount: 1
  logLevel: 1
  extraArgs: []
  nodeAffinityPreset: {}
  podSecurityContext: {}
  containerSecurityContext: {}
  containerPorts:
    http: 8080
  livenessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 30
  readinessProbe:
    enabled: true
    initialDelaySeconds: 10
    timeoutSeconds: 1
    periodSeconds: 30
    failureThreshold: 3
    successThreshold: 1
  startupProbe: {}
  resources:
    limits: {}
    requests: {}
  persistence: {}
  service:
    ports:
      http: 80
    nodePorts: {}
  serviceAccount:
    create: false

iam:
  image:
    registry: registry.cn-beijing.aliyuncs.com
    repository: xiaoshiai/rune
    tag: latest
    pullPolicy: ""
    pullSecrets: []
  replicaCount: 1
  logLevel: 1
  leaderElect: true
  containerPorts:
    http: 8080
  extraArgs: []
  nodeAffinityPreset: {}
  podSecurityContext: {}
  containerSecurityContext: {}
  livenessProbe:
    enabled: true
    initialDelaySeconds: 10
  readinessProbe:
    initialDelaySeconds: 10
    enabled: true
  startupProbe: {}
  resources:
    limits: {}
    requests: {}
  persistence: {}
  service:
    ports:
      http: 80
    nodePorts: {}
  serviceAccount:
    create: false

console:
  image:
    registry: registry.cn-hangzhou.aliyuncs.com
    repository: xiaoshiai/rune-console
    tag: latest
    pullPolicy: ""
    pullSecrets: []
  replicaCount: 1
  logLevel: 1
  extraArgs: []
  nodeAffinityPreset: {}
  podSecurityContext: {}
  containerSecurityContext: {}
  livenessProbe:
    enabled: true
    initialDelaySeconds: 10
  readinessProbe:
    enabled: true
    initialDelaySeconds: 10
    timeoutSeconds: 1
    periodSeconds: 5
    failureThreshold: 6
    successThreshold: 1
  startupProbe: {}
  resources:
    limits: {}
    requests: {}
  persistence: {}
  containerPorts:
    http: 3000
  service:
    ports:
      http: 80
    nodePorts: {}
  serviceAccount:
    create: false
  metrics:
    enabled: false

cloud:
  api:
    image:
      registry: registry.cn-hangzhou.aliyuncs.com
      repository: xiaoshiai/rune
      tag: latest
      pullPolicy: ""
      pullSecrets: []
    replicaCount: 1
    logLevel: 1
    extraArgs: []
    nodeAffinityPreset: {}
    podSecurityContext: {}
    containerSecurityContext: {}
    containerPorts:
      http: 8080
    livenessProbe:
      enabled: true
      initialDelaySeconds: 5
      periodSeconds: 30
    readinessProbe:
      enabled: true
      initialDelaySeconds: 10
      timeoutSeconds: 1
      periodSeconds: 30
      failureThreshold: 3
      successThreshold: 1
    startupProbe: {}
    resources:
      limits: {}
      requests: {}
    persistence: {}
    service:
      ports:
        http: 80
      nodePorts: {}
    serviceAccount:
      create: false
  controller:
    image:
      registry: registry.cn-hangzhou.aliyuncs.com
      repository: xiaoshiai/rune
      tag: latest
      pullPolicy: ""
      pullSecrets: []
    replicaCount: 1
    logLevel: 1
    extraArgs: []
    nodeAffinityPreset: {}
    podSecurityContext: {}
    containerSecurityContext: {}
    containerPorts:
      http: 8080
    livenessProbe:
      enabled: true
      initialDelaySeconds: 5
      periodSeconds: 30
    readinessProbe:
      enabled: true
      initialDelaySeconds: 10
      timeoutSeconds: 1
      periodSeconds: 30
      failureThreshold: 3
      successThreshold: 1
    startupProbe: {}
    resources:
      limits: {}
      requests: {}
    persistence: {}
    service:
      ports:
        http: 80
      nodePorts: {}
    serviceAccount:
      create: false

xpai:
  api:
    image:
      registry: registry.cn-hangzhou.aliyuncs.com
      repository: xiaoshiai/rune
      tag: latest
      pullPolicy: ""
      pullSecrets: []
    replicaCount: 1
    logLevel: 1
    extraArgs: []
    nodeAffinityPreset: {}
    podSecurityContext: {}
    containerSecurityContext: {}
    containerPorts:
      http: 8080
    livenessProbe:
      enabled: true
      initialDelaySeconds: 5
      periodSeconds: 30
    readinessProbe:
      enabled: true
      initialDelaySeconds: 10
      timeoutSeconds: 1
      periodSeconds: 30
      failureThreshold: 3
      successThreshold: 1
    startupProbe: {}
    resources:
      limits: {}
      requests: {}
    persistence: {}
    service:
      ports:
        http: 80
      nodePorts: {}
    serviceAccount:
      create: false
  controller:
    image:
      registry: registry.cn-hangzhou.aliyuncs.com
      repository: xiaoshiai/rune
      tag: latest
      pullPolicy: ""
      pullSecrets: []
    replicaCount: 1
    logLevel: 1
    extraArgs: []
    nodeAffinityPreset: {}
    podSecurityContext: {}
    containerSecurityContext: {}
    containerPorts:
      http: 8080
    livenessProbe:
      enabled: true
      initialDelaySeconds: 5
      periodSeconds: 30
    readinessProbe:
      enabled: true
      initialDelaySeconds: 10
      timeoutSeconds: 1
      periodSeconds: 30
      failureThreshold: 3
      successThreshold: 1
    startupProbe: {}
    resources:
      limits: {}
      requests: {}
    persistence: {}
    service:
      ports:
        http: 80
      nodePorts: {}
    serviceAccount:
      create: false

ingress:
  enabled: true
  pathType: ImplementationSpecific
  hostname: rune.xiaoshiai.cn
  ingressClassName: default-gateway
  path: /
  annotations: {}
  tls: false
  selfSigned: false
  existingSecretName: ""
  extraHosts: []
  extraPaths: []
  extraTls: []
  secrets: []

# mongodb arm support is not available in bitnami helm chart
# see issue: https://github.com/bitnami/charts/issues/3635
mongodb:
  enabled: true
  resources:
    limits:
      cpu: "4"
      memory: 8Gi
    requests:
      cpu: 500m
      memory: 512Mi
  image:
    repository: xiaoshiai/mongodb
  architecture: replicaset
  replicaCount: 3
  persistence:
    enabled: true
    size: 10Gi
  backup:
    enabled: false

externalMongodb:
  enabled: false
  host: ""
  port: 27017
  username: ""
  password: ""

etcd:
  enabled: true
  replicaCount: 3
  resources:
    limits:
      cpu: "2"
      memory: 4Gi
    requests:
      cpu: 500m
      memory: 512Mi
  image:
    registry: registry.cn-hangzhou.aliyuncs.com
    repository: xiaoshiai/etcd

  autoCompactionMode: periodic
  autoCompactionRetention: "10m"

  auth:
    rbac:
      # etcd client watch api do not support token auth rotation
      create: false
    token:
      enabled: false
    client:
      secureTransport: false

casdoor-helm-charts:
  enabled: true
  serviceAccount:
    create: false
  clientId: ""
  clientSecret: ""
  image:
    repository: registry.cn-hangzhou.aliyuncs.com/xiaoshiai
  database:
    driver: postgres
    user: "casdoor"
    password: 3d48c7b9d715
    host: "rune-postgresql-primary"
    port: "5432"
    databaseName: casdoor
    sslMode: disable
  adminPassword: "123"

postgresql:
  enabled: true
  serviceAccount:
    create: false
  image:
    repository: xiaoshiai/postgresql
  architecture: replication
  auth:
    username: "casdoor"
    password: "3d48c7b9d715"
    database: "casdoor"
  primary:
    resources:
      limits:
        cpu: "2"
        memory: 4Gi
      requests:
        cpu: 500m
        memory: 512Mi
  readReplicas:
    resources:
      limits:
        cpu: "1"
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 512Mi

externalEtcd:
  enabled: true
  servers: "http://rune-etcd:2379"
  username: ""
  password: ""
