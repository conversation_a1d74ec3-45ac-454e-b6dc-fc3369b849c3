apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.cloud.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-api
  {{- if or .Values.cloud.api.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.api.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.cloud.api.replicaCount }}
  {{- if .Values.cloud.api.updateStrategy }}
  strategy: {{- toYaml .Values.cloud.api.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.api.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: cloud-api
  template:
    metadata:
      {{- if .Values.cloud.api.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: cloud-api
    spec:
      {{- include "rune.cloud.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.cloud.api.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.cloud.api.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.cloud.api.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.api.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.api.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.cloud.api.podAffinityPreset "component" "cloud" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.cloud.api.podAntiAffinityPreset "component" "cloud" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.cloud.api.nodeAffinityPreset.type "key" .Values.cloud.api.nodeAffinityPreset.key "values" .Values.cloud.api.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.cloud.api.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.api.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.api.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.api.priorityClassName }}
      priorityClassName: {{ .Values.cloud.api.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.cloud.api.schedulerName }}
      schedulerName: {{ .Values.cloud.api.schedulerName | quote }}
      {{- end }}
      {{- if .Values.cloud.api.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.api.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.cloud.api.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.api.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.cloud.api.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.cloud.api.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: cloud
          image: {{ template "rune.cloud.api.image" . }}
          imagePullPolicy: {{ .Values.cloud.api.image.pullPolicy }}
          {{- if .Values.cloud.api.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.cloud.api.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.cloud.api.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - rune-cloud
            - api
            - --listen=:{{ .Values.cloud.api.containerPorts.http }}
            - --v={{ .Values.cloud.api.logLevel }}
            {{- if .Values.cloud.api.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.cloud.api.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.cloud.api.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.cloud.api.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.cloud.api.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.cloud.api.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.cloud.api.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.cloud.api.resources }}
          resources: {{- toYaml .Values.cloud.api.resources | nindent 12 }}
          {{- else if ne .Values.cloud.api.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.cloud.api.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.iam.containerPorts.http }}
            {{- if .Values.iam.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.cloud.api.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.api.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.api.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.api.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.api.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.api.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.api.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.api.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.api.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.api.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.cloud.api.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.api.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.cloud.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-api
  {{- if or .Values.cloud.api.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.api.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.cloud.api.service.type }}
  {{- if and .Values.cloud.api.service.clusterIP (eq .Values.cloud.api.service.type "ClusterIP") }}
  clusterIP: {{ .Values.cloud.api.service.clusterIP }}
  {{- end }}
  {{- if .Values.cloud.api.service.sessionAffinity }}
  sessionAffinity: {{ .Values.cloud.api.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.cloud.api.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.cloud.api.service.type "LoadBalancer") (eq .Values.cloud.api.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.cloud.api.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.cloud.api.service.type "LoadBalancer") (not (empty .Values.cloud.api.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.cloud.api.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.cloud.api.service.type "LoadBalancer") (not (empty .Values.cloud.api.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.cloud.api.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      targetPort: http
      port: {{ .Values.cloud.api.service.ports.http }}
      {{- if and (or (eq .Values.cloud.api.service.type "NodePort") (eq .Values.cloud.api.service.type "LoadBalancer")) (not (empty .Values.cloud.api.service.nodePorts.http)) }}
      nodePort: {{ .Values.cloud.api.service.nodePorts.http }}
      {{- else if eq .Values.cloud.api.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.cloud.api.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.cloud.api.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.api.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-api