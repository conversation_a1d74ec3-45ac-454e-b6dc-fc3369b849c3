apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.cloud.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-controller
  {{- if or .Values.cloud.controller.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.controller.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.cloud.controller.replicaCount }}
  {{- if .Values.cloud.controller.updateStrategy }}
  strategy: {{- toYaml .Values.cloud.controller.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.controller.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: cloud-controller
  template:
    metadata:
      {{- if .Values.cloud.controller.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: cloud-controller
    spec:
      {{- include "rune.cloud.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.cloud.controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.cloud.controller.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.cloud.controller.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.controller.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.controller.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.cloud.controller.podAffinityPreset "component" "controller" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.cloud.controller.podAntiAffinityPreset "component" "controller" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.cloud.controller.nodeAffinityPreset.type "key" .Values.cloud.controller.nodeAffinityPreset.key "values" .Values.cloud.controller.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.cloud.controller.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.controller.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.controller.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.controller.priorityClassName }}
      priorityClassName: {{ .Values.cloud.controller.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.cloud.controller.schedulerName }}
      schedulerName: {{ .Values.cloud.controller.schedulerName | quote }}
      {{- end }}
      {{- if .Values.cloud.controller.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.controller.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.cloud.controller.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.cloud.controller.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.cloud.controller.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.cloud.controller.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: controller
          image: {{ template "rune.cloud.controller.image" . }}
          imagePullPolicy: {{ .Values.cloud.controller.image.pullPolicy }}
          {{- if .Values.cloud.controller.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.cloud.controller.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.cloud.controller.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - rune-cloud
            - controller
            - --listen=:{{ .Values.cloud.controller.containerPorts.http }}
            - --v={{ .Values.cloud.controller.logLevel }}
            {{- if .Values.cloud.controller.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.cloud.controller.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- if .Values.cloud.controller.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.cloud.controller.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.cloud.controller.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.cloud.controller.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.cloud.controller.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.cloud.controller.resources }}
          resources: {{- toYaml .Values.cloud.controller.resources | nindent 12 }}
          {{- else if ne .Values.cloud.controller.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.cloud.controller.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.cloud.controller.containerPorts.http }}
            {{- if .Values.cloud.controller.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.cloud.controller.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.controller.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.controller.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.controller.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.controller.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.controller.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.controller.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.cloud.controller.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.cloud.controller.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.cloud.controller.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.cloud.controller.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.cloud.controller.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.cloud.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-controller
  {{- if or .Values.cloud.controller.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.controller.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.cloud.controller.service.type }}
  {{- if and .Values.cloud.controller.service.clusterIP (eq .Values.cloud.controller.service.type "ClusterIP") }}
  clusterIP: {{ .Values.cloud.controller.service.clusterIP }}
  {{- end }}
  {{- if .Values.cloud.controller.service.sessionAffinity }}
  sessionAffinity: {{ .Values.cloud.controller.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.cloud.controller.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.cloud.controller.service.type "LoadBalancer") (eq .Values.cloud.controller.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.cloud.controller.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.cloud.controller.service.type "LoadBalancer") (not (empty .Values.cloud.controller.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.cloud.controller.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.cloud.controller.service.type "LoadBalancer") (not (empty .Values.cloud.controller.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.cloud.controller.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      targetPort: http
      port: {{ .Values.cloud.controller.service.ports.http }}
      {{- if and (or (eq .Values.cloud.controller.service.type "NodePort") (eq .Values.cloud.controller.service.type "LoadBalancer")) (not (empty .Values.cloud.controller.service.nodePorts.http)) }}
      nodePort: {{ .Values.cloud.controller.service.nodePorts.http }}
      {{- else if eq .Values.cloud.controller.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.cloud.controller.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.cloud.controller.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.cloud.controller.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: cloud-controller
