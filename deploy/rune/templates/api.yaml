apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: api
  {{- if or .Values.api.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.api.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.api.replicaCount }}
  {{- if .Values.api.updateStrategy }}
  strategy: {{- toYaml .Values.api.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.api.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: api
  template:
    metadata:
      {{- if .Values.api.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.api.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: api
    spec:
      {{- include "rune.api.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.api.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.api.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.api.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.api.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.api.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.api.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.api.podAffinityPreset "component" "api" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.api.podAntiAffinityPreset "component" "api" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.api.nodeAffinityPreset.type "key" .Values.api.nodeAffinityPreset.key "values" .Values.api.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.api.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.api.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.api.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.api.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.api.priorityClassName }}
      priorityClassName: {{ .Values.api.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.api.schedulerName }}
      schedulerName: {{ .Values.api.schedulerName | quote }}
      {{- end }}
      {{- if .Values.api.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.api.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.api.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.api.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.api.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.api.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.api.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.api.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: api
          image: {{ template "rune.api.image" . }}
          imagePullPolicy: {{ .Values.api.image.pullPolicy }}
          {{- if .Values.api.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.api.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.api.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.api.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - rune-api
            - --listen=:{{ .Values.api.containerPorts.http }}
            - --v={{ .Values.api.logLevel }}
            {{- if .Values.api.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.api.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.api.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.api.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.api.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.api.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.api.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.api.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.api.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.api.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.api.resources }}
          resources: {{- toYaml .Values.api.resources | nindent 12 }}
          {{- else if ne .Values.api.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.api.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.iam.containerPorts.http }}
            {{- if .Values.iam.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.api.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.api.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.api.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.api.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.api.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.api.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.api.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.api.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.api.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.api.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.api.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.api.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.api.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.api.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.api.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.api.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: api
  {{- if or .Values.api.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.api.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.api.service.type }}
  {{- if and .Values.api.service.clusterIP (eq .Values.api.service.type "ClusterIP") }}
  clusterIP: {{ .Values.api.service.clusterIP }}
  {{- end }}
  {{- if .Values.api.service.sessionAffinity }}
  sessionAffinity: {{ .Values.api.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.api.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.api.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.api.service.type "LoadBalancer") (eq .Values.api.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.api.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.api.service.type "LoadBalancer") (not (empty .Values.api.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.api.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.api.service.type "LoadBalancer") (not (empty .Values.api.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.api.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.api.service.ports.http }}
      targetPort: http
      {{- if and (or (eq .Values.api.service.type "NodePort") (eq .Values.api.service.type "LoadBalancer")) (not (empty .Values.api.service.nodePorts.http)) }}
      nodePort: {{ .Values.api.service.nodePorts.http }}
      {{- else if eq .Values.api.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.api.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.api.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.api.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: api