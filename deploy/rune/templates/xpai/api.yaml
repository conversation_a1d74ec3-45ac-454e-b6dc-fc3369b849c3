apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.xpai.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai-api
  {{- if or .Values.xpai.api.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.api.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.xpai.api.replicaCount }}
  {{- if .Values.xpai.api.updateStrategy }}
  strategy: {{- toYaml .Values.xpai.api.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.api.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: xpai-api
  template:
    metadata:
      {{- if .Values.xpai.api.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: xpai-api
    spec:
      {{- include "rune.xpai.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.xpai.api.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.xpai.api.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.xpai.api.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.api.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.api.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.api.podAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.api.podAntiAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.xpai.api.nodeAffinityPreset.type "key" .Values.xpai.api.nodeAffinityPreset.key "values" .Values.xpai.api.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.xpai.api.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.api.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.api.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.api.priorityClassName }}
      priorityClassName: {{ .Values.xpai.api.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.xpai.api.schedulerName }}
      schedulerName: {{ .Values.xpai.api.schedulerName | quote }}
      {{- end }}
      {{- if .Values.xpai.api.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.api.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.xpai.api.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.api.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.xpai.api.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.xpai.api.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: xpai
          image: {{ template "rune.xpai.api.image" . }}
          imagePullPolicy: {{ .Values.xpai.api.image.pullPolicy }}
          {{- if .Values.xpai.api.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.xpai.api.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.xpai.api.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - rune-xpai
            - api
            - --listen=:{{ .Values.xpai.api.containerPorts.http }}
            - --v={{ .Values.xpai.api.logLevel }}
            {{- if .Values.xpai.api.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.xpai.api.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.xpai.api.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.xpai.api.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.api.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.xpai.api.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.api.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.xpai.api.resources }}
          resources: {{- toYaml .Values.xpai.api.resources | nindent 12 }}
          {{- else if ne .Values.xpai.api.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.xpai.api.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.iam.containerPorts.http }}
            {{- if .Values.iam.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.xpai.api.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.api.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.api.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.api.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.api.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.api.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.api.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.api.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.api.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.api.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.xpai.api.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.api.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.xpai.api.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai-api
  {{- if or .Values.xpai.api.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.api.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.xpai.api.service.type }}
  {{- if and .Values.xpai.api.service.clusterIP (eq .Values.xpai.api.service.type "ClusterIP") }}
  clusterIP: {{ .Values.xpai.api.service.clusterIP }}
  {{- end }}
  {{- if .Values.xpai.api.service.sessionAffinity }}
  sessionAffinity: {{ .Values.xpai.api.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.xpai.api.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.xpai.api.service.type "LoadBalancer") (eq .Values.xpai.api.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.xpai.api.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.xpai.api.service.type "LoadBalancer") (not (empty .Values.xpai.api.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.xpai.api.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.xpai.api.service.type "LoadBalancer") (not (empty .Values.xpai.api.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.xpai.api.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.xpai.api.service.ports.http }}
      targetPort: http
      {{- if and (or (eq .Values.xpai.api.service.type "NodePort") (eq .Values.xpai.api.service.type "LoadBalancer")) (not (empty .Values.xpai.api.service.nodePorts.http)) }}
      nodePort: {{ .Values.xpai.api.service.nodePorts.http }}
      {{- else if eq .Values.xpai.api.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.xpai.api.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.xpai.api.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.api.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai-api