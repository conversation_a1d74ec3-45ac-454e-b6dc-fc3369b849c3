apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.xpai.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai
  {{- if or .Values.xpai.controller.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.controller.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.xpai.controller.replicaCount }}
  {{- if .Values.xpai.controller.updateStrategy }}
  strategy: {{- toYaml .Values.xpai.controller.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.controller.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: xpai
  template:
    metadata:
      {{- if .Values.xpai.controller.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: xpai
    spec:
      {{- include "rune.xpai.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.xpai.controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.xpai.controller.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.xpai.controller.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.controller.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.controller.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.controller.podAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.controller.podAntiAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.xpai.controller.nodeAffinityPreset.type "key" .Values.xpai.controller.nodeAffinityPreset.key "values" .Values.xpai.controller.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.xpai.controller.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.controller.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.controller.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.controller.priorityClassName }}
      priorityClassName: {{ .Values.xpai.controller.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.xpai.controller.schedulerName }}
      schedulerName: {{ .Values.xpai.controller.schedulerName | quote }}
      {{- end }}
      {{- if .Values.xpai.controller.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.controller.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.xpai.controller.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.controller.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.xpai.controller.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.xpai.controller.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: xpai
          image: {{ template "rune.xpai.controller.image" . }}
          imagePullPolicy: {{ .Values.xpai.controller.image.pullPolicy }}
          {{- if .Values.xpai.controller.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.xpai.controller.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.xpai.controller.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - rune-xpai
            - controller
            - --listen=:{{ .Values.xpai.controller.containerPorts.http }}
            - --v={{ .Values.xpai.controller.logLevel }}
            {{- if .Values.xpai.controller.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.xpai.controller.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.xpai.controller.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.xpai.controller.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.controller.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.xpai.controller.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.controller.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.xpai.controller.resources }}
          resources: {{- toYaml .Values.xpai.controller.resources | nindent 12 }}
          {{- else if ne .Values.xpai.controller.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.xpai.controller.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.iam.containerPorts.http }}
            {{- if .Values.iam.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.xpai.controller.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.controller.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.controller.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.controller.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.controller.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.controller.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.controller.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.controller.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.controller.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.controller.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.xpai.controller.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.controller.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.xpai.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai
  {{- if or .Values.xpai.controller.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.controller.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.xpai.controller.service.type }}
  {{- if and .Values.xpai.controller.service.clusterIP (eq .Values.xpai.controller.service.type "ClusterIP") }}
  clusterIP: {{ .Values.xpai.controller.service.clusterIP }}
  {{- end }}
  {{- if .Values.xpai.controller.service.sessionAffinity }}
  sessionAffinity: {{ .Values.xpai.controller.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.xpai.controller.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.xpai.controller.service.type "LoadBalancer") (eq .Values.xpai.controller.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.xpai.controller.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.xpai.controller.service.type "LoadBalancer") (not (empty .Values.xpai.controller.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.xpai.controller.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.xpai.controller.service.type "LoadBalancer") (not (empty .Values.xpai.controller.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.xpai.controller.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.xpai.controller.service.ports.http }}
      targetPort: http
      {{- if and (or (eq .Values.xpai.controller.service.type "NodePort") (eq .Values.xpai.controller.service.type "LoadBalancer")) (not (empty .Values.xpai.controller.service.nodePorts.http)) }}
      nodePort: {{ .Values.xpai.controller.service.nodePorts.http }}
      {{- else if eq .Values.xpai.controller.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.xpai.controller.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.xpai.controller.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.controller.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai