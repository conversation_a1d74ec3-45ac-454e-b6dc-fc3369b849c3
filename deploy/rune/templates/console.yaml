apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.console.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: console
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.console.replicaCount }}
  {{- if .Values.console.updateStrategy }}
  strategy: {{- toYaml .Values.console.updateStrategy | nindent 4 }}
  {{- end }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: console
  template:
    metadata:
      {{- if .Values.console.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.console.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: console
        {{- if .Values.console.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.console.podLabels "context" $) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "rune.console.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.console.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.console.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.console.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.console.podAffinityPreset "component" "console" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.console.podAntiAffinityPreset "component" "console" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.console.nodeAffinityPreset.type "key" .Values.console.nodeAffinityPreset.key "values" .Values.console.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.console.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.console.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.console.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.console.priorityClassName }}
      priorityClassName: {{ .Values.console.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.console.schedulerName }}
      schedulerName: {{ .Values.console.schedulerName | quote }}
      {{- end }}
      {{- if .Values.console.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.console.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      containers:
        - name: console
          image: {{ include "rune.console.image" . }}
          imagePullPolicy: {{ .Values.console.image.pullPolicy }}
          {{- if .Values.console.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.console.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.console.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.console.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.console.args "context" $) | nindent 12 }}
          {{- else }}
          args:
            {{- if .Values.console.metrics.enabled }}
            # todo: metrics args here
            {{- end }}
            {{- if .Values.console.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.console.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          env:
          - name: NUXT_PUBLIC_API_BASE_URL
            value: {{ include "rune.api.address" . }}
          - name: NUXT_PROXY_OPTIONS_TARGET
            value: {{ include "rune.api.address" . }}
          - name: LISTEN
            value: :{{- .Values.console.containerPorts.http }}
            {{- if .Values.console.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.console.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.console.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.console.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.console.resources }}
          resources: {{- toYaml .Values.console.resources | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.console.containerPorts.http }}
            {{- if .Values.console.metrics.enabled }}
            - name: metrics
              containerPort: {{ .Values.console.metrics.service.port }}
              protocol: TCP
            {{- end }}
          {{- if .Values.console.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- else if .Values.console.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customLivenessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- else if .Values.console.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customReadinessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.console.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- else if .Values.console.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.console.customStartupProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.console.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.console.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
          {{- if .Values.console.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.console.extraVolumeMounts "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.console.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.console.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if .Values.console.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.console.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.console.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: console
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.console.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.console.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.console.service.type }}
  {{- if or (eq .Values.console.service.type "LoadBalancer") (eq .Values.console.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.console.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.console.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.console.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.console.service.ports.http }}
      protocol: TCP
      targetPort: http
      {{- if and (or (eq .Values.console.service.type "NodePort") (eq .Values.console.service.type "LoadBalancer")) (not (empty .Values.console.service.nodePorts.http)) }}
      nodePort: {{ .Values.console.service.nodePorts.http }}
      {{- end }}
    {{- if .Values.console.metrics.enabled }}
    - name: metrics
      port: {{ .Values.console.metrics.service.port }}
      targetPort: metrics
      protocol: TCP
    {{- end }}
    {{- if .Values.console.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.console.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: console
