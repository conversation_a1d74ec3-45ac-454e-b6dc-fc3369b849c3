{{/*
Return the proper image name
{{ include "common.images.image" ( dict "imageRoot" .Values.path.to.the.image "global" $) }}
*/}}
{{- define "common.images.image2" -}}
{{- $registryName := .imageRoot.registry -}}
{{- $repositoryName := .imageRoot.repository -}}
{{- $tag := .imageRoot.tag | toString -}}
{{- if or (not $tag) (eq $tag "latest") -}}
    {{- if hasPrefix "v" .root.Chart.AppVersion  -}}
        {{- $tag = printf "%s" .root.Chart.AppVersion | toString -}}
    {{- else -}}
        {{- $tag = printf "v%s" .root.Chart.AppVersion | toString -}}
    {{- end -}}
{{- end -}}
{{- if .global.imageRegistry }}
    {{- $registryName = .global.imageRegistry -}}
{{- end -}}
{{- if $registryName }}
    {{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else -}}
    {{- printf "%s:%s" $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{- define "rune.api.fullname" -}}
{{ template "common.names.fullname" . }}-api
{{- end -}}

{{- define "rune.iam.fullname" -}}
{{ template "common.names.fullname" . }}-iam
{{- end -}}

{{- define "rune.console.fullname" -}}
{{ template "common.names.fullname" . }}-console
{{- end -}}

{{- define "rune.cloud.fullname" -}}
{{ template "common.names.fullname" . }}-cloud
{{- end -}}

{{- define "rune.cloud.api.fullname" -}}
{{ template "common.names.fullname" . }}-cloud-api
{{- end -}}

{{- define "rune.cloud.controller.fullname" -}}
{{ template "common.names.fullname" . }}-cloud-controller
{{- end -}}

{{- define "rune.xpai.api.fullname" -}}
{{ template "common.names.fullname" . }}-xpai-api
{{- end -}}

{{- define "rune.xpai.controller.fullname" -}}
{{ template "common.names.fullname" . }}-xpai-controller
{{- end -}}

{{/*
Return the proper image name
*/}}
{{- define "rune.api.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.api.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.console.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.console.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.iam.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.iam.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.cloud.api.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.cloud.api.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.cloud.controller.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.cloud.controller.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.xpai.api.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.xpai.api.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "rune.xpai.controller.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.xpai.controller.image "root" . "global" .Values.global) }}
{{- end -}}


{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "rune.api.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.api.image) "context" $) -}}
{{- end -}}

{{- define "rune.iam.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.iam.image) "context" $) -}}
{{- end -}}

{{- define "rune.console.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.console.image) "context" $) -}}
{{- end -}}

{{- define "rune.cloud.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.cloud.api.image .Values.cloud.controller.image) "context" $) -}}
{{- end -}}

{{- define "rune.xpai.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.xpai.api.image .Values.xpai.controller.image) "context" $) -}}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "rune.api.serviceAccountName" -}}
{{- if .Values.api.serviceAccount.create -}}
    {{ default (include "rune.api.fullname" .) .Values.api.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.api.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.iam.serviceAccountName" -}}
{{- if .Values.iam.serviceAccount.create -}}
    {{ default (include "rune.iam.fullname" .) .Values.iam.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.iam.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.cloud.api.serviceAccountName" -}}
{{- if .Values.cloud.api.serviceAccount.create -}}
    {{ default (include "rune.cloud.api.fullname" .) .Values.cloud.api.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.cloud.api.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.cloud.controller.serviceAccountName" -}}
{{- if .Values.cloud.controller.serviceAccount.create -}}
    {{ default (include "rune.cloud.controller.fullname" .) .Values.cloud.controller.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.cloud.controller.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.console.serviceAccountName" -}}
{{- if .Values.console.serviceAccount.create -}}
    {{ default (include "rune.console.fullname" .) .Values.console.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.console.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.xpai.api.serviceAccountName" -}}
{{- if .Values.xpai.api.serviceAccount.create -}}
    {{ default (include "rune.xpai.api.fullname" .) .Values.xpai.api.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.xpai.api.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.xpai.controller.serviceAccountName" -}}
{{- if .Values.xpai.controller.serviceAccount.create -}}
    {{ default (include "rune.xpai.controller.fullname" .) .Values.xpai.controller.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.xpai.controller.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "rune.api.address" -}}
http://{{- include "rune.api.fullname" . -}}:{{- .Values.api.service.ports.http -}}
{{- end -}}

{{- define "rune.mongodb.fullname" -}}
{{- include "common.names.dependency.fullname" (dict "chartName" "mongodb" "chartValues" .Values.mongodb "context" $) -}}
{{- end -}}

{{- define "rune.mongodb.host" -}}
{{- if .Values.mongodb.enabled -}}
  {{- printf "%s-headless" (include "rune.mongodb.fullname" .) -}}
{{- else if .Values.externalMongodb -}}
  {{- .Values.externalMongodb.host -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.port" -}}
{{- if .Values.mongodb.enabled -}}
    {{- .Values.mongodb.service.ports.mongodb -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.port -}}
    {{- .Values.externalMongodb.port -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.database" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.databases -}}
        {{- first .Values.mongodb.auth.databases -}}
    {{- else -}}
        {{- /*keep empty,use default*/ -}}
    {{- end -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.database -}}
    {{- .Values.externalMongodb.database -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.username" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.rootUser -}}
        {{- .Values.mongodb.auth.rootUser -}}
    {{- else -}}
        {{- /*keep empty,use default*/ -}}
    {{- end -}}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.username -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.password" -}}
{{- if .Values.mongodb.enabled -}}
    {{ .Values.mongodb.auth.rootPassword }}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.password -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.password.secret" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.existingSecret -}}
        {{- .Values.mongodb.auth.existingSecret -}}
    {{- else -}}
        {{- include "rune.mongodb.fullname" . -}}
    {{- end -}}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.existingSecret -}}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.password.secret.key" -}}
{{- if .Values.mongodb.enabled -}}
    {{- printf "%s" "mongodb-root-password" -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.existingSecret -}}
    {{- if .Values.externalMongodb.existingSecretPasswordKey -}}
        {{- .Values.externalMongodb.existingSecretPasswordKey -}}
    {{- else -}}
        {{- printf "%s" "mongodb-password" -}}
    {{- end -}}
{{- end -}}
{{- end -}}

{{- define "rune.casdoor.fullname" -}}
{{- include "common.names.dependency.fullname" (dict "chartName" "casdoor" "chartValues" (index .Values "casdoor-helm-charts") "context" $) -}}
{{- end -}}

{{- define "rune.casdoor.address" -}}
http://{{- include "rune.casdoor.fullname" . -}}:{{- (index .Values "casdoor-helm-charts" "service" "port") -}}
{{- end -}}

{{- define "rune.common.env" }}
{{- end -}}

{{- define "rune.iam.env" }}
{{- if (index .Values "casdoor-helm-charts" "enabled") }}
- name: CASDOOR_ADDRESS
  value: {{ include "rune.casdoor.address" . | quote }}
- name: CASDOOR_CLIENTID
  value: {{ (index .Values "casdoor-helm-charts" "clientId") | quote }}
- name: CASDOOR_CLIENTSECRET
  value: {{ (index .Values "casdoor-helm-charts" "clientSecret") | quote }}
{{- end -}}
{{- end -}}

{{- define "rune.mongodb.env" }}
{{- if .Values.mongodb.enabled -}}
- name: MONGODB_ADDRESS
  value: {{ printf "%s:%s" (include "rune.mongodb.host" .) (include "rune.mongodb.port" .) | quote }}
{{- if (include "rune.mongodb.username" .) }}
- name: MONGODB_USERNAME
  value: {{ include "rune.mongodb.username" . }}
{{- end }}
{{- if (include "rune.mongodb.database" .) }}
- name: MONGODB_DATABASE
  value: {{ include "rune.mongodb.database" . }}
{{- end }}
- name: MONGODB_PASSWORD
{{- if (include "rune.mongodb.password.secret" . ) }}
  valueFrom:
    secretKeyRef:
      name: {{ include "rune.mongodb.password.secret" . }}
      key: {{ include "rune.mongodb.password.secret.key" . }}
{{- else }}
  value: {{ include "rune.mongodb.password" . | quote }}
{{- end }}
{{- end }}
{{- end }}


{{- define "rune.etcd.fullname" -}}
{{ template "common.names.dependency.fullname" (dict "chartName" "etcd" "chartValues" .Values.etcd "context" $) }}
{{- end -}}

{{- define "rune.etcd.servers" -}}
{{- range $i := until (int .Values.etcd.replicaCount) }}
  {{- if $i }}
    {{- print "," -}}
  {{- end }}
    {{- if (index $.Values "etcd" "auth" "client" "secureTransport") -}}
    {{- printf "https://%s-%d.%s-headless.%s:%s" (include "rune.etcd.fullname" $) $i (include "rune.etcd.fullname" $) $.Release.Namespace (include "rune.etcd.port" $) -}}
    {{- else -}}
    {{- printf "http://%s-%d.%s-headless.%s:%s" (include "rune.etcd.fullname" $) $i (include "rune.etcd.fullname" $) $.Release.Namespace (include "rune.etcd.port" $) -}}
    {{- end -}}

{{- end -}}
{{- end -}}


{{- define "rune.etcd.port" -}}
{{- .Values.etcd.service.ports.client -}}
{{- end -}}

{{- define "rune.etcd.env" -}}
{{- if .Values.etcd.enabled -}}
- name: ETCD_SERVERS
  value: {{ include "rune.etcd.servers" . | quote }}
{{- if .Values.etcd.auth.rbac.create }} 
- name: ETCD_USERNAME
  value: {{ .Values.etcd.auth.rbac.username | default "root" | quote }}
- name: ETCD_PASSWORD
{{- if .Values.etcd.auth.rbac.rootPassword }}
  value: {{ .Values.etcd.auth.rbac.rootPassword | quote }}
{{- else }}
  valueFrom:
    secretKeyRef:
      name: {{ .Values.etcd.auth.existingSecret | default (include "rune.etcd.fullname" .) }}
      key: {{ .Values.etcd.auth.existingSecretPasswordKey | default "etcd-root-password" }}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}