apiVersion: v2
appVersion: 0.0.0
dependencies:
  - name: common
    version: 2.x.x
    repository: oci://registry-1.docker.io/bitnamicharts
  - name: etcd
    version: 9.15.2
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: etcd.enabled
  - name: mongodb
    version: 16.5.26
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: mongodb.enabled
  - name: casdoor-helm-charts
    repository: oci://registry-1.docker.io/casbin
    version: v1.952.0
    condition: casdoor-helm-charts.enabled
  - name: postgresql
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 16.7.14
    condition: postgresql.enabled
name: rune
version: 0.0.0
