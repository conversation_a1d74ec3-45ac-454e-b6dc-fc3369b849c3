{{- if .Values.storageClass.create -}}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ .Release.Name }}
{{- if .Values.storageClass.annotations }}
  annotations:
{{ toYaml .Values.storageClass.annotations | indent 4 }}
{{- end }}
  labels:
    app: {{ include "storage-nfs.name" . }}
    chart: {{ include "storage-nfs.chart" . }}
    release: {{ .Release.Name }}
provisioner: {{ .Values.driverName }}
parameters:
{{- if .Values.storageClass.auth }}
  csi.storage.k8s.io/node-stage-secret-name: {{ include "storage-nfs.secretName" . }}-secret
  csi.storage.k8s.io/node-stage-secret-namespace: {{ .Release.Namespace }}
{{- end }}
  server: {{ .Values.storageClass.server }}
  share: {{ .Values.storageClass.share }}
{{- if .Values.storageClass.subDir }}
  subDir: {{ .Values.storageClass.subDir }}
{{- end }}
  mountPermissions: {{ .Values.storageClass.mountPermissions }}
reclaimPolicy: {{ .Values.storageClass.reclaimPolicy }}
volumeBindingMode: Immediate
allowVolumeExpansion: {{ .Values.storageClass.allowVolumeExpansion }}
{{- if .Values.storageClass.mountOptions }}
mountOptions:
{{- range .Values.storageClass.mountOptions }}
  - {{ . }}
{{- end }}
{{- end }}
{{- end -}}
