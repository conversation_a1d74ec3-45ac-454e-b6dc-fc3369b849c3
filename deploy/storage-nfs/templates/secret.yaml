{{- if .Values.storageClass.auth -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "storage-nfs.secretName" . }}-secret
  namespace: {{ .Release.Namespace }}
  {{- with .Values.secret.annotations }}
  annotations: {{- . | toYaml | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "storage-nfs.name" . }}
    chart: {{ include "storage-nfs.chart" . }}
    release: {{ .Release.Name }}
type: Opaque
data:
  username: {{ .Values.secret.username }}
  password: {{ .Values.secret.password }}
{{- end -}}
