apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ include "storage-nfs.fullname" . }}-static-pv
spec:
  capacity:
    storage: 1Pi
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
{{- if .Values.storageClass.mountOptions }}
  mountOptions:
{{- range .Values.storageClass.mountOptions }}
    - {{ . }}
{{- end }}
{{- end }}
  csi:
    driver: {{ .Values.driverName }}
    volumeHandle: {{ include "storage-nfs.fullname" . }}-static-pv
    volumeAttributes:
      server: {{ .Values.storageClass.server }}
      share: {{ .Values.storageClass.share }}
{{- if .Values.storageClass.auth }}
    nodeStageSecretRef:
      name: {{ include "storage-nfs.secretName" . }}-secret
      namespace: {{ .Release.Namespace }}
{{- end }}
  persistentVolumeReclaimPolicy: Retain
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "storage-nfs.fullname" . }}-static-pvc
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Pi
  volumeName: {{ include "storage-nfs.fullname" . }}-static-pv
  storageClassName: ""