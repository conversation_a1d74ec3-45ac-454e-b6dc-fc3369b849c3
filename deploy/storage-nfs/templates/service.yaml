{{- if .Values.s3gateway.create -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "storage-nfs.fullname" . }}
  labels:
    {{- include "storage-nfs.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "storage-nfs.selectorLabels" . | nindent 4 }}
{{- end }}
