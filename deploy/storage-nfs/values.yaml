imagePullSecrets: []
image:
  repository: nginx
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
podSecurityContext: {}
  # fsGroup: 2000
driverName: "nfs.csi.k8s.io"

nodeSelector: {}
tolerations: []
affinity: {}
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

storageClass:
  create: true
  reclaimPolicy: Retain
  allowVolumeExpansion: true
  annotations: {}
  server: ""
  share: ""
  subDir: ""
  mountPermissions: "0"
  auth: false
  mountOptions:
  - nfsvers=4.1
  - noresvport
  - hard
  
  

secret:
  # Specifies whether the secret should be created
  annotations: {}
  username: ""
  password: ""

s3gateway:
  create: false
  replicaCount: 1


service:
  type: ClusterIP
  port: 80

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

livenessProbe:
  httpGet:
    path: /
    port: http
readinessProbe:
  httpGet:
    path: /
    port: http

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local







