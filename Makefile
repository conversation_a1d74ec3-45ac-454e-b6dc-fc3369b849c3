
BUILD_DATE?=$(shell date -u +'%Y-%m-%dT%H:%M:%SZ')
GIT_VERSION?=$(shell git describe --tags --dirty --abbrev=0 2>/dev/null || git symbolic-ref --short HEAD)
GIT_COMMIT?=$(shell git rev-parse HEAD 2>/dev/null)
GIT_BRANCH?=$(shell git symbolic-ref --short HEAD 2>/dev/null)
VERSION?=$(shell echo "${GIT_VERSION}" | sed -e 's/^v//')

BIN_DIR?=bin
IMAGE_REGISTRY?=registry.cn-hangzhou.aliyuncs.com
IMAGE_REPOSITORY?=xiaoshiai
IMAGE_NAME?=rune

LDFLAGS+=-w -s
LDFLAGS+=-X 'xiaoshiai.cn/common/version.gitVersion=${GIT_VERSION}'
LDFLAGS+=-X 'xiaoshiai.cn/common/version.gitCommit=${GIT_COMMIT}'
LDFLAGS+=-X 'xiaoshiai.cn/common/version.buildDate=${BUILD_DATE}'
BUILD_TARGET?=./cmd/...
##@ All
all: build ## build all

##@ General
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

generate: generate-apis ## Generate code for the project.

generate-apis:
	$(CONTROLLER_GEN) paths="./pkg/apis/..." crd  output:crd:artifacts:config=deploy/rune/crds
	$(CONTROLLER_GEN) paths="./pkg/..." object

define build-binary
	@echo "Building ${1}-${2}";
	@mkdir -p ${BIN_DIR}/${1}-${2};
	GOOS=${1} GOARCH=$(2) CGO_ENABLED=0 go build -gcflags=all="-N -l" -ldflags="${LDFLAGS}" -o ${BIN_DIR}/${1}-${2} $(BUILD_TARGET)
endef
##@ Build
.PHONY: build
build: ## Build local binary.
	$(call build-binary,linux,amd64)
	$(call build-binary,linux,arm64)

.PHONY: release
release:release-image release-helm  ## Release all artifacts.

FULL_IMAGE_NAME?=$(IMAGE_REGISTRY)/$(IMAGE_REPOSITORY)/$(IMAGE_NAME):$(GIT_VERSION)
BUILDX_PLATFORMS?=linux/amd64,linux/arm64
release-image:
	docker buildx build --platform=${BUILDX_PLATFORMS} --push -t ${FULL_IMAGE_NAME} -f Dockerfile ${BIN_DIR}

package-helm: ## Package helm chart.
	helm package -u --version ${VERSION} --app-version ${GIT_VERSION} --destination ${BIN_DIR} deploy/rune

HELM_REPO_USERNAME?=kubegems
HELM_REPO_PASSWORD?=
CHARTMUSEUM_ADDR?=https://${HELM_REPO_USERNAME}:${HELM_REPO_PASSWORD}@charts.kubegems.io
.PHONY: release-helm
release-helm: ## Release helm chart.
	curl -f --data-binary "@${BIN_DIR}/rune-${VERSION}.tgz" ${CHARTMUSEUM_ADDR}/rune/api/charts

##@ Tools
CONTROLLER_GEN = ${BIN_DIR}/controller-gen
controller-gen: ## Download controller-gen locally if necessary.
	GOBIN=$(abspath ${BIN_DIR}) go install sigs.k8s.io/controller-tools/cmd/controller-gen@v0.16.5

clean:
	rm -rf ${BIN_DIR}
