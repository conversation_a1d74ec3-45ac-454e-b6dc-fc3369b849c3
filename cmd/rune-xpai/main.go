package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/pai"
)

func main() {
	cmd := NewCommand()     // Create a new command for the xpai server
	cmd.SilenceUsage = true // Suppress usage output on error
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:  "xpai",
		Long: "Platform for AI",
	}
	cmd.AddCommand(
		NewAPICommand(),
		NewControllerCommand(),
	)
	return cmd
}

func NewAPICommand() *cobra.Command {
	options := pai.NewDefaultOptions()
	cmd := &cobra.Command{
		Use:                "api",
		Long:               "Run the PAI API server",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return pai.Run(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}

func NewControllerCommand() *cobra.Command {
	options := pai.NewDefaultControllerOptions()
	cmd := &cobra.Command{
		Use:                "controller",
		Short:              "Run the PAI controller",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return pai.RunController(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
