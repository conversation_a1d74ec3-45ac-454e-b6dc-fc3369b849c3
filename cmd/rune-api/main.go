package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/apiserver"
)

func main() {
	cmd := NewCommand()
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	options := apiserver.NewDefaultOptions()
	cmd := &cobra.Command{
		Use:                "apiserver",
		Long:               "API Server for XPai",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return apiserver.Run(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
