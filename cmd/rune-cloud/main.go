package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/cloud"
)

func main() {
	cmd := NewCommand()
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.<PERSON>derr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:  "cloud",
		Long: "Multi-tenant Cloud Server",
	}
	cmd.AddCommand(
		NewAPICommand(),
		NewControllerCommand(),
	)
	return cmd
}

func NewAPICommand() *cobra.Command {
	options := cloud.NewDefaultOptions()
	cmd := &cobra.Command{
		Use:                "api",
		Long:               "Run the Cloud API server",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return cloud.Run(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}

func NewControllerCommand() *cobra.Command {
	options := cloud.NewDefaultControllerOptions()
	cmd := &cobra.Command{
		Use:                "controller",
		Short:              "Run the Cloud controller",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return cloud.RunController(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
