package s3server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pai/storage"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3err"
)

type BucketFn func(string) FilesystemAdapter

type iamAndMeta struct {
	accounts  []Account
	policy    BucketPolicy
	fsAdapter string
}
type StorageSetCache struct {
	sync.RWMutex
	root     Account
	store    store.Store
	bucketFn BucketFn
	cache    map[string]*iamAndMeta
}

func (s *StorageSetCache) removeCacheByFs(name string) {
	s.Lock()
	defer s.Unlock()
	for k, v := range s.cache {
		if v.fsAdapter == name {
			delete(s.cache, k)
		}
	}
}

func (s *StorageSetCache) getBucketPolicy(bucket string) ([]byte, error) {
	s.RLock()
	defer s.RUnlock()
	iamAndMeta, ok := s.cache[bucket]
	if !ok {
		return nil, ErrNoSuchKey
	}
	return json.Marshal(iamAndMeta.policy)
}

// GetUserAccount implements IAMService.
func (s *StorageSetCache) GetUserAccount(access string) (Account, error) {
	log.FromContext(context.Background()).Info("GetUserAccount", "access", access)
	if access == s.root.Access {
		return s.root, nil
	}
	s.RLock()
	defer s.RUnlock()
	for _, ca := range s.cache {
		for _, a := range ca.accounts {
			if a.Access == access {
				return a, nil
			}
		}
	}
	return Account{}, ErrNoSuchUser
}

func (s *StorageSetCache) canGetBucketInfo(bucket, access string) bool {
	s.RLock()
	defer s.RUnlock()
	iamAndMeta, ok := s.cache[bucket]
	if !ok {
		return false
	}
	for _, account := range iamAndMeta.accounts {
		if account.Access == access {
			return true
		}
	}
	return false
}

// DeleteAttribute implements MetadataStorer.
func (s *StorageSetCache) DeleteAttribute(bucket string, object string, attribute string) error {
	return nil
}

// DeleteAttributes implements MetadataStorer.
func (s *StorageSetCache) DeleteAttributes(bucket string, object string) error {
	return nil
}

// ListAttributes implements MetadataStorer.
func (s *StorageSetCache) ListAttributes(bucket string, object string) ([]string, error) {
	return []string{}, nil
}

// RetrieveAttribute implements MetadataStorer.
func (s *StorageSetCache) RetrieveAttribute(f *os.File, bucket string, object string, attribute string) ([]byte, error) {
	return nil, ErrNoSuchKey
}

// StoreAttribute implements MetadataStorer.
func (s *StorageSetCache) StoreAttribute(f *os.File, bucket string, object string, attribute string, value []byte) error {
	return nil
}

// CreateAccount implements IAMService.
func (s *StorageSetCache) CreateAccount(account Account) error {
	log.FromContext(context.Background()).Info("CreateAccount", "account", account)
	return s3err.GetAPIError(s3err.ErrAdminMethodNotSupported)
}

// DeleteUserAccount implements IAMService.
func (s *StorageSetCache) DeleteUserAccount(access string) error {
	log.FromContext(context.Background()).Info("DeleteUserAccount", "access", access)
	return s3err.GetAPIError(s3err.ErrAdminMethodNotSupported)
}

// ListUserAccounts implements IAMService.
func (s *StorageSetCache) ListUserAccounts() ([]Account, error) {
	log.FromContext(context.Background()).Info("ListUserAccounts")
	return []Account{}, s3err.GetAPIError(s3err.ErrAdminMethodNotSupported)
}

// Shutdown implements IAMService.
func (s *StorageSetCache) Shutdown() error {
	log.FromContext(context.Background()).Info("Shutdown")
	return nil
}

// UpdateUserAccount implements IAMService.
func (s *StorageSetCache) UpdateUserAccount(access string, props MutableProps) error {
	log.FromContext(context.Background()).Info("UpdateUserAccount", "access", access)
	return s3err.GetAPIError(s3err.ErrAdminMethodNotSupported)
}

func newStorageSetCache(root Account, store store.Store, fn BucketFn) *StorageSetCache {
	return &StorageSetCache{
		root:     root,
		store:    store,
		bucketFn: fn,
		cache:    make(map[string]*iamAndMeta),
	}
}

func (s *StorageSetCache) getFilesystemAdapterByBucket(bucket string) (string, error) {
	s.RLock()
	defer s.RUnlock()
	if fs, ok := s.cache[bucket]; ok {
		return fs.fsAdapter, nil
	}
	return "", s3err.GetAPIError(s3err.ErrNoSuchBucket)
}

func (s *StorageSetCache) setCache(bucket string, i *iamAndMeta) {
	s.Lock()
	s.cache[bucket] = i
	s.Unlock()
}
func (s *StorageSetCache) deleteCache(bucket string) {
	s.Lock()
	delete(s.cache, bucket)
	s.Unlock()
}

func generateCacheFromStorageset(ss *storage.StorageSet) *iamAndMeta {
	bucket := ss.Name
	managerAccessKey := ss.S3.AccessKeyID
	managerSecretKey := ss.S3.SecretAccessKey
	readonlyAccessKey := ss.S3.ReadOnlyAccessKeyID
	readonlySecretKey := ss.S3.ReadOnlySecretAccessKey
	return &iamAndMeta{
		accounts: []Account{
			{
				Access: managerAccessKey,
				Secret: managerSecretKey,
				Role:   RoleManager,
			},
			{
				Access: readonlyAccessKey,
				Secret: readonlySecretKey,
				Role:   RoleReadOnly,
			},
		},
		policy: BucketPolicy{
			Statement: []BucketPolicyItem{
				//manager
				{
					Effect: BucketPolicyAccessTypeAllow,
					Principals: map[string]struct{}{
						managerAccessKey: {},
					},
					Actions: map[Action]struct{}{
						AllActions: {},
					},
					Resources: map[string]struct{}{
						fmt.Sprintf("arn:aws:s3:::%s/*", bucket): {},
						fmt.Sprintf("arn:aws:s3:::%s", bucket):   {},
					},
				},
				// readonly
				{
					Effect: BucketPolicyAccessTypeAllow,
					Principals: map[string]struct{}{
						readonlyAccessKey: {},
					},
					Actions: map[Action]struct{}{
						GetObjectAction:  {},
						ListBucketAction: {},
					},
					Resources: map[string]struct{}{
						fmt.Sprintf("arn:aws:s3:::%s/*", bucket): {},
						fmt.Sprintf("arn:aws:s3:::%s", bucket):   {},
					},
				},
			},
		},
		fsAdapter: ss.StorageClusterName,
	}
}

func (s *StorageSetCache) loadExistingWatchStorageSet(ctx context.Context) {
	list := store.List[storage.StorageSet]{}
	if err := s.store.List(ctx, &list); err != nil {
		log.FromContext(ctx).Error(err, "failed to load storageset")
		return
	}
	if len(list.Items) == 0 {
		return
	}
	for index := range list.Items {
		// todo - etcd中的storageset被标记删除了，那对应的目录是不是需要被删除
		// todo - 如果不需要被删除，又创建出同名的怎么办?
		//有删除标记，不加载到系统
		// if !element.DeletionTimestamp.IsZero() {
		// 	continue
		// }
		element := list.Items[index]
		fsAdapter := s.bucketFn(element.StorageClusterName)
		if fsAdapter == nil {
			log.FromContext(ctx).Error(fmt.Errorf("failed to get filesystem adapter"), "failed to get filesystem adapter")
			return
		}
		if err := fsAdapter.CreateBucket(ctx, &s3.CreateBucketInput{Bucket: &element.Name}, nil); err != nil {
			if !errors.Is(err, s3err.GetAPIError(s3err.ErrBucketAlreadyExists)) {
				log.FromContext(ctx).Error(err, "failed to create bucket")
				return
			}
		}
		s.setCache(element.Name, generateCacheFromStorageset(&element))
	}
}

func (s *StorageSetCache) loadAndWatchStorageSet(ctx context.Context) {
	s.loadExistingWatchStorageSet(ctx)
	list := &store.List[storage.StorageSet]{}
	watcher, err := s.store.Watch(ctx, list)
	if err != nil {
		log.FromContext(ctx).Error(err, "failed to watch storageset")
		return
	}
	defer watcher.Stop()
	for {
		select {
		case <-ctx.Done():
			log.FromContext(ctx).Info("close watch storageset")
			return
		case event, ok := <-watcher.Events():
			if !ok {
				log.FromContext(ctx).Info("watcher storageset channel closed")
				return
			}
			switch event.Type {
			case store.WatchEventCreate:
				obj, ok := event.Object.(*storage.StorageSet)
				if !ok {
					log.FromContext(ctx).Error(fmt.Errorf("failed to convert to storageset"), "failed to convert to storageset")
					return
				}
				fsAdapter := s.bucketFn(obj.StorageClusterName)
				if fsAdapter == nil {
					log.FromContext(ctx).Error(fmt.Errorf("failed to get filesystem adapter"), "failed to get filesystem adapter")
					return
				}
				err := fsAdapter.CreateBucket(ctx, &s3.CreateBucketInput{Bucket: &obj.Name}, nil)
				if err != nil {
					if errors.Is(err, s3err.GetAPIError(s3err.ErrBucketAlreadyExists)) {
						continue
					}
					log.FromContext(ctx).Error(err, "failed to create bucket")
					return
				}
				iamAndMeta := generateCacheFromStorageset(obj)
				s.setCache(obj.Name, iamAndMeta)
			case store.WatchEventUpdate:
				//删除对应的操作是把deletionTimestamp设置成操作时间,而不是WatchEventDelete事件
				obj, ok := event.Object.(*storage.StorageSet)
				if !ok {
					log.FromContext(ctx).Error(fmt.Errorf("failed to convert to storageset"), "failed to convert to storageset")
					return
				}
				if !obj.DeletionTimestamp.IsZero() {
					// 删除操作
					if err := s.deleteStorageset(ctx, obj.Name, obj.StorageClusterName); err != nil {
						log.FromContext(ctx).Error(err, "failed to delete bucket")
						return
					}
				}
			case store.WatchEventDelete:
				//如果是直接删除
				obj, ok := event.Object.(*storage.StorageSet)
				if !ok {
					log.FromContext(ctx).Error(fmt.Errorf("failed to convert to storageset"), "failed to convert to storageset")
					return
				}
				if err := s.deleteStorageset(ctx, obj.Name, obj.StorageClusterName); err != nil {
					log.FromContext(ctx).Error(err, "failed to delete bucket")
					return
				}
			default:
				log.FromContext(ctx).Info("ignore storageset event", "type", event.Type)
			}
		}
	}
}

func (s *StorageSetCache) deleteStorageset(ctx context.Context, storagesetName, storageclusterName string) error {
	s.deleteCache(storagesetName)
	fsAdapter := s.bucketFn(storageclusterName)
	if fsAdapter == nil {
		log.FromContext(ctx).Error(fmt.Errorf("failed to get filesystem adapter"), "failed to get filesystem adapter")
		return fmt.Errorf("not found filesyeyem adapter,name:%s", storageclusterName)
	}
	if err := fsAdapter.DeleteBucket(ctx, storagesetName); err != nil {
		log.FromContext(ctx).Error(err, "failed to delete bucket")
		return err
	}
	return nil
}
