package s3server

import (
	"context"
	"fmt"
	"sync"
	"time"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pai/storage"
)

const (
	defaultMountTimeout   = 10 * time.Second
	defaultUnmountTimeout = 10 * time.Second
)

type FilesystemManager struct {
	BackendUnsupported
	Cache    *StorageSetCache
	Store    store.Store
	adapters map[string]FilesystemAdapter // 存储集群名称
	mutex    sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
}

func NewFilesystemManager(root Account, storage store.Store) (*FilesystemManager, error) {
	ctx, cancel := context.WithCancel(context.Background())
	fsm := &FilesystemManager{
		Store:    storage,
		adapters: make(map[string]FilesystemAdapter),
		ctx:      ctx,
		cancel:   cancel,
	}

	fsm.Cache = newStorageSetCache(root, storage, fsm.locateFilesystemAdapterByFsname)
	err := fsm.loadExistingFilesystems()
	if err != nil {
		return nil, err
	}
	go fsm.watchStorageCluster()
	go fsm.Cache.loadAndWatchStorageSet(ctx)
	return fsm, nil
}

func (fm *FilesystemManager) locateFilesystemAdapterByFsname(fsName string) FilesystemAdapter {
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()
	return fm.adapters[fsName]
}

// 通过存储桶定位底层fs
func (fm *FilesystemManager) locateFilesystemAdapterByBucket(bucket string) (FilesystemAdapter, error) {
	fsName, err := fm.Cache.getFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()
	adapter, ok := fm.adapters[fsName]
	if !ok {
		return nil, fmt.Errorf("filesystem %s not found", fsName)
	}
	return adapter, nil

}

func (fm *FilesystemManager) listAdapters() []FilesystemAdapter {
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()
	result := make([]FilesystemAdapter, 0)
	for _, adapter := range fm.adapters {
		result = append(result, adapter)
	}
	return result
}

// 当重新加入fs集群后，需要从etcd中获取bucket，更新缓存
func (fm *FilesystemManager) addFilesystem(fs FilesystemAdapter) {
	fm.mutex.Lock()
	defer fm.mutex.Unlock()
	fm.adapters[fs.GetName()] = fs
	fm.Cache.loadExistingWatchStorageSet(fm.ctx)
	log.FromContext(fm.ctx).Info("Created filesystem adapter", "name", fs.GetName(), "type", fs.GetType())
}

func (fm *FilesystemManager) existFilesystem(name string) bool {
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()
	_, exist := fm.adapters[name]
	return exist
}

// todo - 移除文件系统过程中，需要移除对应fs中缓存的桶,并不会删除文件
func (fm *FilesystemManager) removeFilesystem(name string) error {
	fm.mutex.Lock()
	defer func() {
		fm.mutex.Unlock()
		fm.Cache.removeCacheByFs(name)
	}()

	adapter, exists := fm.adapters[name]
	if !exists {
		return fmt.Errorf("filesystem %s not found", name)
	}

	if err := fm.unmountFilesystem(name, adapter); err != nil {
		return fmt.Errorf("failed to unmount filesystem %s: %w", name, err)
	}

	delete(fm.adapters, name)
	log.FromContext(fm.ctx).Info("Removed filesystem adapter", "name", name)
	return nil
}

func (fm *FilesystemManager) loadExistingFilesystems() error {
	list := store.List[storage.StorageCluster]{}
	if err := fm.Store.List(fm.ctx, &list); err != nil {
		return err
	}
	if len(list.Items) == 0 {
		return nil
	}
	for _, element := range list.Items {
		//todo - 有删除标记，不加载到系统
		// if !element.DeletionTimestamp.IsZero() {
		// 	continue
		// }
		fsa, errParse := newFilesystemFromStorageCluster(element, fm.Cache)
		if errParse != nil {
			return errParse
		}
		fm.addFilesystem(fsa)
	}
	log.FromContext(fm.ctx).Info("Loading existing filesystem configurations")
	return nil
}
func (fm *FilesystemManager) unmountFilesystem(name string, adapter FilesystemAdapter) error {
	if !adapter.IsMounted() {
		return nil
	}
	if err := adapter.Unmount(defaultUnmountTimeout, false); err != nil {
		log.FromContext(fm.ctx).Info("failed to unmount filesystem", "name", name, "type", adapter.GetType())
		if err1 := adapter.Unmount(defaultUnmountTimeout, true); err1 != nil {
			return fmt.Errorf("failed to force unmount name:%s,type:%s,error: %w", name, adapter.GetType(), err)
		}
	}
	return nil
}

func (fm *FilesystemManager) watchStorageCluster() {
	list := &store.List[storage.StorageCluster]{}
	watcher, err := fm.Store.Watch(fm.ctx, list)
	if err != nil {
		log.FromContext(fm.ctx).Error(err, "failed to watch storagecluster")
		return
	}
	defer watcher.Stop()
	for {
		select {
		case <-fm.ctx.Done():
			log.FromContext(fm.ctx).Info("close watch storagecluster")
			return

		case event, ok := <-watcher.Events():
			if !ok {
				log.FromContext(fm.ctx).Info("watcher storagecluster channel closed")
				return
			}
			switch event.Type {
			case store.WatchEventCreate:
				// 新的存储集群加入
				obj, ok := event.Object.(*storage.StorageCluster)
				if !ok {
					log.FromContext(fm.ctx).Error(fmt.Errorf("failed to convert to storagecluster"), "failed to convert to storagecluster")
					return
				}
				if fm.existFilesystem(obj.Name) {
					continue
				}
				fsa, errParse := newFilesystemFromStorageCluster(*obj, fm.Cache)
				if errParse != nil {
					log.FromContext(fm.ctx).Error(errParse, "failed to parse storagecluster to filesystemadapter")
					continue
				}
				fm.addFilesystem(fsa)

			case store.WatchEventDelete:
				// 删除已有的存储集群
				name := event.Object.GetName()
				if err := fm.removeFilesystem(name); err != nil {
					log.FromContext(fm.ctx).Error(err, "failed to remove filesystem", "name", name)
					return
				}
			case store.WatchEventUpdate:
				obj, ok := event.Object.(*storage.StorageCluster)
				if !ok {
					log.FromContext(fm.ctx).Error(fmt.Errorf("failed to convert to storagecluster"), "failed to convert to storagecluster")
					return
				}
				if !obj.DeletionTimestamp.IsZero() {
					if err := fm.removeFilesystem(obj.Name); err != nil {
						log.FromContext(fm.ctx).Error(err, "failed to remove filesystem", "name", obj.Name)
						return
					}
				}
				//ignore
			default:
				log.FromContext(fm.ctx).Info("ignore storagecluster event", "type", event.Type)
			}
		}
	}
}
