package s3server

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"path/filepath"
	"slices"
	"sync"
	"time"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/log"
	v1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

// 所有文件系统的根目录挂载到/s3agteway
const rootDir = "/s3gateway"

type StorageVolume struct {
	accounts  []Account
	policy    BucketPolicy
	realPath  string
	bucketRef string
}

func (sv *StorageVolume) hasPrivilege(access string) bool {
	return slices.ContainsFunc(sv.accounts, func(a Account) bool {
		return a.Access == access
	})
}

func (sv *StorageVolume) setAccountAndPolicy(accounts []v1.S3Account) {
	sv.accounts = make([]Account, len(accounts))
	for i, account := range accounts {
		var role = RoleReadOnly
		if account.Role == v1.S3AccountRoleRW {
			role = RoleManager
		}
		sv.accounts[i] = Account{
			Access: account.AccessKey,
			Secret: account.SecretKey,
			Role:   role,
		}
	}
	sv.generateBucketPolicy()
}

func (sv *StorageVolume) getBucketPolicy(bucket string) ([]byte, error) {
	if bucket != sv.bucketRef {
		return nil, fs.ErrNotExist
	}
	return json.Marshal(sv.policy)
}

func (sv *StorageVolume) generateBucketPolicy() {
	for _, account := range sv.accounts {
		switch account.Role {
		case RoleManager:
			sv.policy.Statement = append(sv.policy.Statement, BucketPolicyItem{
				Effect: BucketPolicyAccessTypeAllow,
				Principals: map[string]struct{}{
					account.Access: {},
				},
				Actions: map[Action]struct{}{
					AllActions: {},
				},
				Resources: map[string]struct{}{
					fmt.Sprintf("arn:aws:s3:::%s/*", sv.bucketRef): {},
					fmt.Sprintf("arn:aws:s3:::%s", sv.bucketRef):   {},
				},
			})
		case RoleReadOnly:
			sv.policy.Statement = append(sv.policy.Statement, BucketPolicyItem{
				Effect: BucketPolicyAccessTypeAllow,
				Principals: map[string]struct{}{
					account.Access: {},
				},
				Actions: map[Action]struct{}{
					GetObjectAction:  {},
					ListBucketAction: {},
				},
				Resources: map[string]struct{}{
					fmt.Sprintf("arn:aws:s3:::%s/*", sv.bucketRef): {},
					fmt.Sprintf("arn:aws:s3:::%s", sv.bucketRef):   {},
				},
			})
		}
	}
}

type StorageVolumeCache struct {
	sync.RWMutex
	ctx     context.Context
	cluster string
	client  client.Client
	root    Account
	cache   map[string]*StorageVolume
}

// GetUserAccount implements IAMService.
func (s *StorageVolumeCache) getUserAccount(access string) (Account, error) {
	log.FromContext(context.Background()).Info("GetUserAccount", "access", access)
	if access == s.root.Access {
		return s.root, nil
	}
	s.RLock()
	defer s.RUnlock()
	for _, ca := range s.cache {
		for _, a := range ca.accounts {
			if a.Access == access {
				return a, nil
			}
		}
	}
	return Account{}, ErrNoSuchUser
}

func NewStorageVolumeCache(ctx context.Context, client client.Client, root Account, cluster string) (*StorageVolumeCache, error) {
	s := &StorageVolumeCache{
		ctx:     ctx,
		cluster: cluster,
		client:  client,
		root:    root,
		cache:   make(map[string]*StorageVolume),
	}
	return s, nil
}

func (s *StorageVolumeCache) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	storageVolume := &v1.StorageVolume{}
	if err := s.client.Get(ctx, req.NamespacedName, storageVolume); err != nil {
		if apierrors.IsNotFound(err) {
			s.del(req.Name)
			return ctrl.Result{}, nil
		}
		return ctrl.Result{
			Requeue:      true,
			RequeueAfter: time.Second * 10,
		}, err
	}
	if !storageVolume.GetDeletionTimestamp().IsZero() {
		s.del(req.Name)
		return ctrl.Result{}, nil
	}
	status := storageVolume.Status
	accounts := storageVolume.Spec.Accounts
	if status.Phase == v1.StorageVolumePhaseBound {
		sv := &StorageVolume{
			realPath:  status.StoragePath,
			bucketRef: storageVolume.Name,
		}
		sv.setAccountAndPolicy(accounts)
		s.set(storageVolume.Name, sv)
		return ctrl.Result{}, nil
	}
	return ctrl.Result{
		Requeue:      true,
		RequeueAfter: time.Second * 10,
	}, nil
}

func (s *StorageVolumeCache) getBucketPloicy(bucket string) ([]byte, error) {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return nil, fs.ErrNotExist
	}
	return cacheBucket.getBucketPolicy(bucket)
}

func (s *StorageVolumeCache) set(key string, value *StorageVolume) {
	s.Lock()
	defer s.Unlock()
	s.cache[key] = value
}

func (s *StorageVolumeCache) del(key string) {
	s.Lock()
	defer s.Unlock()
	delete(s.cache, key)
}

// 获取存储桶的真实路径
func (s *StorageVolumeCache) getBucketPath(bucket string) (string, error) {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return "", fs.ErrNotExist
	}
	return filepath.Join(rootDir, cacheBucket.realPath), nil
}

// 获取所有存储桶的路径列表
func (s *StorageVolumeCache) listBucketsPath() map[string]string {
	s.RLock()
	defer s.RUnlock()
	var paths = make(map[string]string)
	for key, value := range s.cache {
		paths[key] = filepath.Join(rootDir, value.realPath)
	}
	//sort.Strings(paths)
	return paths
}

func (s *StorageVolumeCache) hasBucketPrivilege(bucket, accessKey string) bool {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return false
	}
	return cacheBucket.hasPrivilege(accessKey)
}

// OnStorageVolumeChange 处理 StorageVolume 变化事件
// 这个方法将被 Controller 调用
func (s *StorageVolumeCache) OnStorageVolumeChange(volume *v1.StorageVolume, eventType string) {
	log.FromContext(s.ctx).Info("StorageVolume changed", "name", volume.Name, "event", eventType, "phase", volume.Status.Phase)

	switch eventType {
	case "CREATE", "UPDATE":
		status := volume.Status
		accounts := volume.Spec.Accounts
		if status.Phase == v1.StorageVolumePhaseBound {
			sv := &StorageVolume{
				realPath:  status.StoragePath,
				bucketRef: volume.Name,
			}
			sv.setAccountAndPolicy(accounts)
			s.set(volume.Name, sv)
		} else {
			// 如果状态不是 Bound，从缓存中移除
			s.del(volume.Name)
		}
	case "DELETE":
		log.FromContext(s.ctx).Info("delete storagevolume", "name", volume.Name)
		s.del(volume.Name)
	}
}
