package s3server

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"path/filepath"
	"slices"
	"sync"

	"k8s.io/apimachinery/pkg/watch"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/log"
	v1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

// 所有文件系统的根目录挂载到/s3agteway
const rootDir = "/s3gateway"

type StorageVolume struct {
	accounts  []Account
	policy    BucketPolicy
	realPath  string
	bucketRef string
}

func (sv *StorageVolume) hasPrivilege(access string) bool {
	return slices.ContainsFunc(sv.accounts, func(a Account) bool {
		return a.Access == access
	})
}

func (sv *StorageVolume) setAccountAndPolicy(accounts []v1.S3Account) {
	sv.accounts = make([]Account, len(accounts))
	for i, account := range accounts {
		var role = RoleReadOnly
		if account.Role == v1.S3AccountRoleRW {
			role = RoleManager
		}
		sv.accounts[i] = Account{
			Access: account.AccessKey,
			Secret: account.SecretKey,
			Role:   role,
		}
	}
	sv.generateBucketPolicy()
}

func (sv *StorageVolume) getBucketPolicy(bucket string) ([]byte, error) {
	if bucket != sv.bucketRef {
		return nil, fs.ErrNotExist
	}
	return json.Marshal(sv.policy)
}

func (sv *StorageVolume) generateBucketPolicy() {
	for _, account := range sv.accounts {
		switch account.Role {
		case RoleManager:
			sv.policy.Statement = append(sv.policy.Statement, BucketPolicyItem{
				Effect: BucketPolicyAccessTypeAllow,
				Principals: map[string]struct{}{
					account.Access: {},
				},
				Actions: map[Action]struct{}{
					AllActions: {},
				},
				Resources: map[string]struct{}{
					fmt.Sprintf("arn:aws:s3:::%s/*", sv.bucketRef): {},
					fmt.Sprintf("arn:aws:s3:::%s", sv.bucketRef):   {},
				},
			})
		case RoleReadOnly:
			sv.policy.Statement = append(sv.policy.Statement, BucketPolicyItem{
				Effect: BucketPolicyAccessTypeAllow,
				Principals: map[string]struct{}{
					account.Access: {},
				},
				Actions: map[Action]struct{}{
					GetObjectAction:  {},
					ListBucketAction: {},
				},
				Resources: map[string]struct{}{
					fmt.Sprintf("arn:aws:s3:::%s/*", sv.bucketRef): {},
					fmt.Sprintf("arn:aws:s3:::%s", sv.bucketRef):   {},
				},
			})
		}
	}
}

type StorageVolumeCache struct {
	sync.RWMutex
	ctx     context.Context
	cluster string
	client  client.WithWatch
	root    Account
	cache   map[string]*StorageVolume
}

// GetUserAccount implements IAMService.
func (s *StorageVolumeCache) getUserAccount(access string) (Account, error) {
	log.FromContext(context.Background()).Info("GetUserAccount", "access", access)
	if access == s.root.Access {
		return s.root, nil
	}
	s.RLock()
	defer s.RUnlock()
	for _, ca := range s.cache {
		for _, a := range ca.accounts {
			if a.Access == access {
				return a, nil
			}
		}
	}
	return Account{}, ErrNoSuchUser
}

func NewStorageVolumeCache(ctx context.Context, client client.WithWatch, root Account, cluster string) (*StorageVolumeCache, error) {
	s := &StorageVolumeCache{
		ctx:     ctx,
		cluster: cluster,
		client:  client,
		root:    root,
		cache:   make(map[string]*StorageVolume),
	}
	if err := s.load(); err != nil {
		return nil, err
	}
	go s.watch()
	return s, nil
}

func (s *StorageVolumeCache) getBucketPloicy(bucket string) ([]byte, error) {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return nil, fs.ErrNotExist
	}
	return cacheBucket.getBucketPolicy(bucket)
}

func (s *StorageVolumeCache) load() error {
	storageVolumes := &v1.StorageVolumeList{}
	if err := s.client.List(s.ctx, storageVolumes, client.MatchingFields{"spec.clusterReference.name": s.cluster}); err != nil {
		return err
	}
	for _, volume := range storageVolumes.Items {
		status := volume.Status
		if status.StoragePath != "" {
			sv := &StorageVolume{
				realPath:  status.StoragePath,
				bucketRef: volume.Name,
			}
			sv.setAccountAndPolicy(status.Accounts)
			s.set(volume.Name, sv)
		}
	}
	return nil
}

func (s *StorageVolumeCache) watch() {
	storageVolumes := &v1.StorageVolumeList{}
	watcher, err := s.client.Watch(s.ctx, storageVolumes, client.MatchingFields{"spec.clusterReference.name": s.cluster})
	if err != nil {
		log.FromContext(s.ctx).Error(err, "failed to watch storagevolume")
		return
	}
	for event := range watcher.ResultChan() {
		volume, ok := event.Object.(*v1.StorageVolume)
		if !ok {
			continue
		}
		switch event.Type {
		case watch.Added, watch.Modified:
			status := volume.Status
			if status.StoragePath != "" {
				sv := &StorageVolume{
					realPath:  status.StoragePath,
					bucketRef: volume.Name,
				}
				sv.setAccountAndPolicy(status.Accounts)
				s.set(volume.Name, sv)
			}

		case watch.Deleted:
			log.FromContext(s.ctx).Info("delete storagevolume", "name", volume.Name)
			s.del(volume.Name)
		}
	}
}
func (s *StorageVolumeCache) set(key string, value *StorageVolume) {
	s.Lock()
	defer s.Unlock()
	s.cache[key] = value
}

func (s *StorageVolumeCache) del(key string) {
	s.Lock()
	defer s.Unlock()
	delete(s.cache, key)
}

// 获取存储桶的真实路径
func (s *StorageVolumeCache) getBucketPath(bucket string) (string, error) {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return "", fs.ErrNotExist
	}
	return filepath.Join(rootDir, cacheBucket.realPath), nil
}

// 获取所有存储桶的路径列表
func (s *StorageVolumeCache) listBucketsPath() map[string]string {
	s.RLock()
	defer s.RUnlock()
	var paths = make(map[string]string)
	for key, value := range s.cache {
		paths[key] = filepath.Join(rootDir, value.realPath)
	}
	//sort.Strings(paths)
	return paths
}

func (s *StorageVolumeCache) hasBucketPrivilege(bucket, accessKey string) bool {
	s.RLock()
	defer s.RUnlock()
	cacheBucket, ok := s.cache[bucket]
	if !ok {
		return false
	}
	return cacheBucket.hasPrivilege(accessKey)
}
