// Copyright 2023 Versity Software
// This file is licensed under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

package s3server

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/google/uuid"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3api/utils"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3err"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3response"
)

type Posix struct {
	BackendUnsupported
	bucketCache *StorageVolumeCache

	// chownuid/gid enable chowning of files to the account uid/gid
	// when objects are uploaded
	chownuid bool
	chowngid bool

	// euid/egid are the effective uid/gid of the running versitygw process
	// used to determine if chowning is needed
	euid int
	egid int
	// newDirPerm is the permission to set on newly created directories
	newDirPerm fs.FileMode

	// forceNoTmpFile is a flag to disable the use of O_TMPFILE even
	// if the filesystem supports it. This is needed for cases where
	// there are different filesystems mounted below the bucket level.
	forceNoTmpFile bool
}

var _ Backend = &Posix{}

const (
	metaTmpDir          = ".sgwtmp"
	metaTmpMultipartDir = metaTmpDir + "/multipart"
	emptyMD5            = "\"d41d8cd98f00b204e9800998ecf8427e\""
	doFalloc            = true
	skipFalloc          = false
)

// PosixOpts are the options for the Posix backend
type PosixOpts struct {
	// ChownUID sets the UID of the object to the UID of the user on PUT
	ChownUID bool
	// ChownGID sets the GID of the object to the GID of the user on PUT
	ChownGID bool
	// BucketLinks enables symlinks to directories to be treated as buckets
	//BucketLinks bool
	//VersioningDir sets the version directory to enable object versioning
	//VersioningDir string
	// NewDirPerm specifies the permission to set on newly created directories
	NewDirPerm fs.FileMode
	// ForceNoTmpFile disables the use of O_TMPFILE even if the filesystem
	// supports it
	ForceNoTmpFile bool
}

func NewPosix(cache *StorageVolumeCache, opts PosixOpts) *Posix {
	return &Posix{
		bucketCache: cache,
		euid:        os.Geteuid(),
		egid:        os.Getegid(),
		chownuid:    opts.ChownUID,
		chowngid:    opts.ChownGID,
		//bucketlinks:    opts.BucketLinks,
		newDirPerm:     opts.NewDirPerm,
		forceNoTmpFile: opts.ForceNoTmpFile,
	}
}

func (p *Posix) GetUserAccount(access string) (Account, error) {
	return p.bucketCache.getUserAccount(access)
}

func (p *Posix) Shutdown() {

}

func (p *Posix) StatBucket(ctx context.Context, bucket string) error {
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return err
	}
	_, err = os.Stat(bucketPath)
	return err
}

func (p *Posix) String() string {
	return "Posix Gateway"
}

func (p *Posix) ListBuckets(_ context.Context, input s3response.ListBucketsInput) (s3response.ListAllMyBucketsResult, error) {
	fis := p.listBucketFileInfos()
	var cToken string
	var buckets []s3response.ListAllMyBucketsEntry
	for key, fi := range fis {
		if !strings.HasPrefix(key, input.Prefix) {
			continue
		}

		if len(buckets) == int(input.MaxBuckets) {
			cToken = buckets[len(buckets)-1].Name
			break
		}

		if key <= input.ContinuationToken {
			continue
		}

		// return all the buckets for admin users
		if input.IsAdmin {
			buckets = append(buckets, s3response.ListAllMyBucketsEntry{
				Name:         key,
				CreationDate: fi.ModTime(),
			})
			continue
		}
		if p.bucketCache.hasBucketPrivilege(key, input.Access) {
			buckets = append(buckets, s3response.ListAllMyBucketsEntry{
				Name:         key,
				CreationDate: fi.ModTime(),
			})
		}
	}
	sort.Slice(buckets, func(i, j int) bool {
		return buckets[i].Name < buckets[j].Name
	})
	return s3response.ListAllMyBucketsResult{
		Buckets: s3response.ListAllMyBucketsList{
			Bucket: buckets,
		},
		Owner: s3response.CanonicalUser{
			ID: input.Owner,
		},
		Prefix:            input.Prefix,
		ContinuationToken: cToken,
	}, nil
}

func (p *Posix) HeadBucket(_ context.Context, input *s3.HeadBucketInput) (*s3.HeadBucketOutput, error) {
	if input.Bucket == nil {
		return nil, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	bucketPath, err := p.bucketCache.getBucketPath(*input.Bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return nil, err
	}
	_, err = os.Lstat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return nil, fmt.Errorf("stat bucket: %w", err)
	}

	return &s3.HeadBucketOutput{}, nil
}

func ValueToPtr[T any](v T) *T {
	return &v
}

// 不被允许创建存储桶
func (p *Posix) CreateBucket(ctx context.Context, input *s3.CreateBucketInput, acl []byte) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) DeleteBucket(_ context.Context, bucket string) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) PutBucketOwnershipControls(_ context.Context, bucket string, ownership types.ObjectOwnership) error {
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return fmt.Errorf("stat bucket: %w", err)
	}
	return nil
}
func (p *Posix) GetBucketOwnershipControls(_ context.Context, bucket string) (types.ObjectOwnership, error) {
	var ownship types.ObjectOwnership
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return ownship, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return ownship, err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return ownship, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return ownship, fmt.Errorf("stat bucket: %w", err)
	}
	return ownship, s3err.GetAPIError(s3err.ErrOwnershipControlsNotFound)
}
func (p *Posix) DeleteBucketOwnershipControls(_ context.Context, bucket string) error {
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return fmt.Errorf("stat bucket: %w", err)
	}
	return nil
}

func (p *Posix) PutBucketVersioning(ctx context.Context, bucket string, status types.BucketVersioningStatus) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetBucketVersioning(_ context.Context, bucket string) (s3response.GetBucketVersioningOutput, error) {
	return s3response.GetBucketVersioningOutput{}, s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) ListObjectVersions(ctx context.Context, input *s3.ListObjectVersionsInput) (s3response.ListVersionsResult, error) {
	return s3response.ListVersionsResult{}, s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) CreateMultipartUpload(ctx context.Context, mpu s3response.CreateMultipartUploadInput) (s3response.InitiateMultipartUploadResult, error) {
	if mpu.Bucket == nil {
		return s3response.InitiateMultipartUploadResult{}, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if mpu.Key == nil {
		return s3response.InitiateMultipartUploadResult{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	bucket := *mpu.Bucket
	object := *mpu.Key

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.InitiateMultipartUploadResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.InitiateMultipartUploadResult{}, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.InitiateMultipartUploadResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.InitiateMultipartUploadResult{}, fmt.Errorf("stat bucket: %w", err)
	}

	if strings.HasSuffix(*mpu.Key, "/") {
		// directory objects can't be uploaded with mutlipart uploads
		// because posix directories can't contain data
		return s3response.InitiateMultipartUploadResult{}, s3err.GetAPIError(s3err.ErrDirectoryObjectContainsData)
	}

	// parse object tags
	tags, err := ParseObjectTags(getString(mpu.Tagging))
	if err != nil {
		return s3response.InitiateMultipartUploadResult{}, err
	}

	// generate random uuid for upload id
	uploadID := uuid.New().String()
	// hash object name for multipart container
	objNameSum := sha256.Sum256([]byte(*mpu.Key))
	// multiple uploads for same object name allowed,
	// they will all go into the same hashed name directory
	objdir := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", objNameSum))
	tmppath := filepath.Join(bucketPath, objdir)
	// the unique upload id is a directory for all of the parts
	// associated with this specific multipart upload
	err = os.MkdirAll(filepath.Join(tmppath, uploadID), 0755)
	if err != nil {
		return s3response.InitiateMultipartUploadResult{}, fmt.Errorf("create upload temp dir: %w", err)
	}

	// set object tagging
	if tags != nil {
		err := p.PutObjectTagging(ctx, bucket, filepath.Join(objdir, uploadID), tags)
		if err != nil {
			// cleanup object if returning error
			os.RemoveAll(filepath.Join(tmppath, uploadID))
			os.Remove(tmppath)
			return s3response.InitiateMultipartUploadResult{}, err
		}
	}
	return s3response.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadId: uploadID,
	}, nil
}

// getChownIDs returns the uid and gid that should be used for chowning
// the object to the account uid/gid. It also returns a boolean indicating
// if chowning is needed.
func (p *Posix) getChownIDs(acct Account) (int, int, bool) {
	uid := p.euid
	gid := p.egid
	var needsChown bool
	if p.chownuid && acct.UserID != p.euid {
		uid = acct.UserID
		needsChown = true
	}
	if p.chowngid && acct.GroupID != p.egid {
		gid = acct.GroupID
		needsChown = true
	}

	return uid, gid, needsChown
}

func getPartChecksum(algo types.ChecksumAlgorithm, part types.CompletedPart) string {
	switch algo {
	case types.ChecksumAlgorithmCrc32:
		return GetStringFromPtr(part.ChecksumCRC32)
	case types.ChecksumAlgorithmCrc32c:
		return GetStringFromPtr(part.ChecksumCRC32C)
	case types.ChecksumAlgorithmSha1:
		return GetStringFromPtr(part.ChecksumSHA1)
	case types.ChecksumAlgorithmSha256:
		return GetStringFromPtr(part.ChecksumSHA256)
	default:
		return ""
	}
}

func (p *Posix) CompleteMultipartUpload(ctx context.Context, input *s3.CompleteMultipartUploadInput) (s3response.CompleteMultipartUploadResult, string, error) {
	acct, ok := ctx.Value("account").(Account)
	if !ok {
		acct = Account{}
	}

	var res s3response.CompleteMultipartUploadResult

	if input.Bucket == nil {
		return res, "", s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return res, "", s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if input.UploadId == nil {
		return res, "", s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}
	if input.MultipartUpload == nil {
		return res, "", s3err.GetAPIError(s3err.ErrInvalidRequest)
	}

	bucket := *input.Bucket
	object := *input.Key
	uploadID := *input.UploadId
	parts := input.MultipartUpload.Parts

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return res, "", s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return res, "", err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return res, "", s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return res, "", fmt.Errorf("stat bucket: %w", err)
	}

	sum, err := p.checkUploadIDExists(bucket, object, uploadID)
	if err != nil {
		return res, "", err
	}

	objdir := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", sum))

	checksums, err := p.retrieveChecksums(nil, bucket, filepath.Join(objdir, uploadID))
	if err != nil && !errors.Is(err, ErrNoSuchKey) {
		return res, "", fmt.Errorf("get mp checksums: %w", err)
	}
	var checksumAlgorithm types.ChecksumAlgorithm
	if checksums.Algorithm != "" {
		checksumAlgorithm = checksums.Algorithm
	}

	// check all parts ok
	last := len(parts) - 1
	var totalsize int64

	// The initialie values is the lower limit of partNumber: 0
	var partNumber int32
	for i, part := range parts {
		if part.PartNumber == nil {
			return res, "", s3err.GetAPIError(s3err.ErrInvalidPart)
		}
		if *part.PartNumber < 1 {
			return res, "", s3err.GetAPIError(s3err.ErrInvalidCompleteMpPartNumber)
		}
		if *part.PartNumber <= partNumber {
			return res, "", s3err.GetAPIError(s3err.ErrInvalidPartOrder)
		}

		partNumber = *part.PartNumber

		partObjPath := filepath.Join(objdir, uploadID, fmt.Sprintf("%v", *part.PartNumber))
		fullPartPath := filepath.Join(bucketPath, partObjPath)
		fi, err := os.Lstat(fullPartPath)
		if err != nil {
			return res, "", s3err.GetAPIError(s3err.ErrInvalidPart)
		}

		totalsize += fi.Size()
		// all parts except the last need to be greater, thena
		// the minimum allowed size (5 Mib)
		if i < last && fi.Size() < MinPartSize {
			return res, "", s3err.GetAPIError(s3err.ErrEntityTooSmall)
		}
		if parts[i].ETag == nil {
			return res, "", s3err.GetAPIError(s3err.ErrInvalidPart)
		}
	}

	if input.MpuObjectSize != nil && totalsize != *input.MpuObjectSize {
		return res, "", s3err.GetIncorrectMpObjectSizeErr(totalsize, *input.MpuObjectSize)
	}

	var hashRdr *utils.HashReader
	var compositeChecksumRdr *utils.CompositeChecksumReader
	switch checksums.Type {
	case types.ChecksumTypeFullObject:
		hashRdr, err = utils.NewHashReader(nil, "", utils.HashType(strings.ToLower(string(checksumAlgorithm))))
		if err != nil {
			return res, "", fmt.Errorf("initialize hash reader: %w", err)
		}
	case types.ChecksumTypeComposite:
		compositeChecksumRdr, err = utils.NewCompositeChecksumReader(utils.HashType(strings.ToLower(string(checksumAlgorithm))))
		if err != nil {
			return res, "", fmt.Errorf("initialize composite checksum reader: %w", err)
		}
	}

	f, err := p.openTmpFile(filepath.Join(bucketPath, metaTmpDir), bucketPath, object,
		totalsize, acct, skipFalloc, p.forceNoTmpFile)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return res, "", s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return res, "", fmt.Errorf("open temp file: %w", err)
	}
	defer f.cleanup()

	for _, part := range parts {
		partObjPath := filepath.Join(objdir, uploadID, fmt.Sprintf("%v", *part.PartNumber))
		fullPartPath := filepath.Join(bucketPath, partObjPath)
		pf, err := os.Open(fullPartPath)
		if err != nil {
			return res, "", fmt.Errorf("open part %v: %v", *part.PartNumber, err)
		}

		var rdr io.Reader = pf
		if checksums.Type == types.ChecksumTypeFullObject {
			hashRdr.SetReader(rdr)
			rdr = hashRdr
		} else if checksums.Type == types.ChecksumTypeComposite {
			err := compositeChecksumRdr.Process(getPartChecksum(checksumAlgorithm, part))
			if err != nil {
				return res, "", fmt.Errorf("process %v part checksum: %w", *part.PartNumber, err)
			}
		}

		_, err = io.Copy(f.File(), rdr)
		pf.Close()
		if err != nil {
			if errors.Is(err, syscall.EDQUOT) {
				return res, "", s3err.GetAPIError(s3err.ErrQuotaExceeded)
			}
			return res, "", fmt.Errorf("copy part %v: %v", part.PartNumber, err)
		}
	}

	objname := filepath.Join(bucketPath, object)
	dir := filepath.Dir(objname)
	if dir != "" {
		uid, gid, doChown := p.getChownIDs(acct)
		err = MkdirAll(dir, uid, gid, doChown, p.newDirPerm)
		if err != nil {
			return res, "", err
		}
	}

	var versionID string
	var crc32 *string
	var crc32c *string
	var sha1 *string
	var sha256 *string
	var crc64nvme *string

	// Calculate, compare with the provided checksum and store them
	if checksums.Type != "" {
		var sum string
		switch checksums.Type {
		case types.ChecksumTypeComposite:
			sum = compositeChecksumRdr.Sum()
		case types.ChecksumTypeFullObject:
			sum = hashRdr.Sum()
		}

		switch checksumAlgorithm {
		case types.ChecksumAlgorithmCrc32:
			if input.ChecksumCRC32 != nil && *input.ChecksumCRC32 != sum {
				return res, "", s3err.GetChecksumBadDigestErr(checksumAlgorithm)
			}
			//checksum.CRC32 = &sum
			crc32 = &sum
		case types.ChecksumAlgorithmCrc32c:
			if input.ChecksumCRC32C != nil && *input.ChecksumCRC32C != sum {
				return res, "", s3err.GetChecksumBadDigestErr(checksumAlgorithm)
			}
			//checksum.CRC32C = &sum
			crc32c = &sum
		case types.ChecksumAlgorithmSha1:
			if input.ChecksumSHA1 != nil && *input.ChecksumSHA1 != sum {
				return res, "", s3err.GetChecksumBadDigestErr(checksumAlgorithm)
			}
			//checksum.SHA1 = &sum
			sha1 = &sum
		case types.ChecksumAlgorithmSha256:
			if input.ChecksumSHA256 != nil && *input.ChecksumSHA256 != sum {
				return res, "", s3err.GetChecksumBadDigestErr(checksumAlgorithm)
			}
			//checksum.SHA256 = &sum
			sha256 = &sum
		case types.ChecksumAlgorithmCrc64nvme:
			if input.ChecksumCRC64NVME != nil && *input.ChecksumCRC64NVME != sum {
				return res, "", s3err.GetChecksumBadDigestErr(checksumAlgorithm)
			}
			//checksum.CRC64NVME = &sum
			crc64nvme = &sum
		}

	}
	// Calculate s3 compatible md5sum for complete multipart.
	s3MD5 := GetMultipartMD5(parts)
	err = f.link()
	if err != nil {
		return res, "", fmt.Errorf("link object in namespace: %w", err)
	}
	// cleanup tmp dirs
	os.RemoveAll(filepath.Join(bucketPath, objdir, uploadID))
	// use Remove for objdir in case there are still other uploads
	// for same object name outstanding, this will fail if there are
	os.Remove(filepath.Join(bucketPath, objdir))

	return s3response.CompleteMultipartUploadResult{
		Bucket:            &bucket,
		ETag:              &s3MD5,
		Key:               &object,
		ChecksumCRC32:     crc32,
		ChecksumCRC32C:    crc32c,
		ChecksumSHA1:      sha1,
		ChecksumSHA256:    sha256,
		ChecksumCRC64NVME: crc64nvme,
		ChecksumType:      &checksums.Type,
	}, versionID, nil
}

func (p *Posix) checkUploadIDExists(bucket, object, uploadID string) ([32]byte, error) {
	sum := sha256.Sum256([]byte(object))
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return [32]byte{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return [32]byte{}, err
	}

	objdir := filepath.Join(bucketPath, metaTmpMultipartDir, fmt.Sprintf("%x", sum))
	_, err = os.Stat(filepath.Join(objdir, uploadID))
	if errors.Is(err, fs.ErrNotExist) {
		return [32]byte{}, s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}
	if err != nil {
		return [32]byte{}, fmt.Errorf("stat upload: %w", err)
	}
	return sum, nil
}

func (p *Posix) retrieveUploadId(bucket, object string) (string, [32]byte, error) {
	sum := sha256.Sum256([]byte(object))
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return "", [32]byte{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return "", [32]byte{}, err
	}
	objdir := filepath.Join(bucketPath, metaTmpMultipartDir, fmt.Sprintf("%x", sum))

	entries, err := os.ReadDir(objdir)
	if err != nil || len(entries) == 0 {
		return "", [32]byte{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	return entries[0].Name(), sum, nil
}

type objectMetadata struct {
	ContentType        *string
	ContentEncoding    *string
	ContentDisposition *string
	ContentLanguage    *string
	CacheControl       *string
	Expires            *string
}

func (p *Posix) AbortMultipartUpload(_ context.Context, mpu *s3.AbortMultipartUploadInput) error {
	if mpu.Bucket == nil {
		return s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if mpu.Key == nil {
		return s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if mpu.UploadId == nil {
		return s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}

	bucket := *mpu.Bucket
	object := *mpu.Key
	uploadID := *mpu.UploadId
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return fmt.Errorf("stat bucket: %w", err)
	}

	sum := sha256.Sum256([]byte(object))
	objdir := filepath.Join(bucketPath, metaTmpMultipartDir, fmt.Sprintf("%x", sum))

	_, err = os.Stat(filepath.Join(objdir, uploadID))
	if err != nil {
		return s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}

	err = os.RemoveAll(filepath.Join(objdir, uploadID))
	if err != nil {
		return fmt.Errorf("remove multipart upload container: %w", err)
	}
	os.Remove(objdir)

	return nil
}

func (p *Posix) ListMultipartUploads(_ context.Context, mpu *s3.ListMultipartUploadsInput) (s3response.ListMultipartUploadsResult, error) {
	var lmu s3response.ListMultipartUploadsResult

	if mpu.Bucket == nil {
		return lmu, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}

	bucket := *mpu.Bucket
	var delimiter string
	if mpu.Delimiter != nil {
		delimiter = *mpu.Delimiter
	}
	var prefix string
	if mpu.Prefix != nil {
		prefix = *mpu.Prefix
	}
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return lmu, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return lmu, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return lmu, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return lmu, fmt.Errorf("stat bucket: %w", err)
	}

	// ignore readdir error and use the empty list returned
	objs, _ := os.ReadDir(filepath.Join(bucketPath, metaTmpMultipartDir))

	var uploads []s3response.Upload
	var resultUpds []s3response.Upload

	var keyMarker string
	if mpu.KeyMarker != nil {
		keyMarker = *mpu.KeyMarker
	}
	var uploadIDMarker string
	if mpu.UploadIdMarker != nil {
		uploadIDMarker = *mpu.UploadIdMarker
	}
	keyMarkerInd, uploadIdMarkerFound := -1, false

	for _, obj := range objs {
		if !obj.IsDir() {
			continue
		}
		var objectName string
		if mpu.Prefix != nil && !strings.HasPrefix(objectName, *mpu.Prefix) {
			continue
		}

		upids, err := os.ReadDir(filepath.Join(bucketPath, metaTmpMultipartDir, obj.Name()))
		if err != nil {
			continue
		}

		for _, upid := range upids {
			if !upid.IsDir() {
				continue
			}

			fi, err := upid.Info()
			if err != nil {
				return lmu, fmt.Errorf("stat %q: %w", upid.Name(), err)
			}

			uploadID := upid.Name()
			if !uploadIdMarkerFound && uploadIDMarker == uploadID {
				uploadIdMarkerFound = true
			}
			if keyMarkerInd == -1 && objectName == keyMarker {
				keyMarkerInd = len(uploads)
			}

			checksum, err := p.retrieveChecksums(nil, bucket, filepath.Join(metaTmpMultipartDir, obj.Name(), uploadID))
			if err != nil && !errors.Is(err, ErrNoSuchKey) {
				return lmu, fmt.Errorf("get mp checksum: %w", err)
			}

			uploads = append(uploads, s3response.Upload{
				Key:               objectName,
				UploadID:          uploadID,
				StorageClass:      types.StorageClassStandard,
				Initiated:         fi.ModTime(),
				ChecksumAlgorithm: checksum.Algorithm,
				ChecksumType:      checksum.Type,
			})
		}
	}

	maxUploads := int(*mpu.MaxUploads)
	if (uploadIDMarker != "" && !uploadIdMarkerFound) || (keyMarker != "" && keyMarkerInd == -1) {
		return s3response.ListMultipartUploadsResult{
			Bucket:         bucket,
			Delimiter:      delimiter,
			KeyMarker:      keyMarker,
			MaxUploads:     maxUploads,
			Prefix:         prefix,
			UploadIDMarker: uploadIDMarker,
			Uploads:        []s3response.Upload{},
		}, nil
	}

	sort.SliceStable(uploads, func(i, j int) bool {
		return uploads[i].Key < uploads[j].Key
	})

	for i := keyMarkerInd + 1; i < len(uploads); i++ {
		if maxUploads == 0 {
			break
		}
		if keyMarker != "" && uploadIDMarker != "" && uploads[i].UploadID < uploadIDMarker {
			continue
		}
		if i != len(uploads)-1 && len(resultUpds) == maxUploads {
			return s3response.ListMultipartUploadsResult{
				Bucket:             bucket,
				Delimiter:          delimiter,
				KeyMarker:          keyMarker,
				MaxUploads:         maxUploads,
				NextKeyMarker:      resultUpds[i-1].Key,
				NextUploadIDMarker: resultUpds[i-1].UploadID,
				IsTruncated:        true,
				Prefix:             prefix,
				UploadIDMarker:     uploadIDMarker,
				Uploads:            resultUpds,
			}, nil
		}

		resultUpds = append(resultUpds, uploads[i])
	}

	return s3response.ListMultipartUploadsResult{
		Bucket:         bucket,
		Delimiter:      delimiter,
		KeyMarker:      keyMarker,
		MaxUploads:     maxUploads,
		Prefix:         prefix,
		UploadIDMarker: uploadIDMarker,
		Uploads:        resultUpds,
	}, nil
}

func (p *Posix) ListParts(ctx context.Context, input *s3.ListPartsInput) (s3response.ListPartsResult, error) {
	var lpr s3response.ListPartsResult

	if input.Bucket == nil {
		return lpr, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return lpr, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if input.UploadId == nil {
		return lpr, s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}

	bucket := *input.Bucket
	object := *input.Key
	uploadID := *input.UploadId
	stringMarker := ""
	if input.PartNumberMarker != nil {
		stringMarker = *input.PartNumberMarker
	}

	maxParts := int(*input.MaxParts)

	var partNumberMarker int
	if stringMarker != "" {
		var err error
		partNumberMarker, err = strconv.Atoi(stringMarker)
		if err != nil {
			return lpr, s3err.GetAPIError(s3err.ErrInvalidPartNumberMarker)
		}
	}
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return lpr, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return lpr, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return lpr, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return lpr, fmt.Errorf("stat bucket: %w", err)
	}

	sum, err := p.checkUploadIDExists(bucket, object, uploadID)
	if err != nil {
		return lpr, err
	}

	objdir := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", sum))
	tmpdir := filepath.Join(bucketPath, objdir)

	ents, err := os.ReadDir(filepath.Join(tmpdir, uploadID))
	if errors.Is(err, fs.ErrNotExist) {
		return lpr, s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}
	if err != nil {
		return lpr, fmt.Errorf("readdir upload: %w", err)
	}

	checksum, err := p.retrieveChecksums(nil, tmpdir, uploadID)
	if err != nil && !errors.Is(err, ErrNoSuchKey) {
		return lpr, fmt.Errorf("get mp checksum: %w", err)
	}
	if checksum.Algorithm == "" {
		checksum.Algorithm = types.ChecksumAlgorithm("null")
	}
	if checksum.Type == "" {
		checksum.Type = types.ChecksumType("null")
	}

	parts := make([]s3response.Part, 0, len(ents))
	for i, e := range ents {
		if i%128 == 0 {
			select {
			case <-ctx.Done():
				return s3response.ListPartsResult{}, ctx.Err()
			default:
			}
		}
		pn, err := strconv.Atoi(e.Name())
		if err != nil {
			// file is not a valid part file
			continue
		}
		if pn <= partNumberMarker {
			continue
		}

		partPath := filepath.Join(objdir, uploadID, e.Name())
		// b, err := p.meta.RetrieveAttribute(nil, bucket, partPath, etagkey)
		// etag := string(b)
		// if err != nil {
		// 	etag = ""
		// }

		checksum, err := p.retrieveChecksums(nil, bucket, partPath)
		if err != nil && !errors.Is(err, ErrNoSuchKey) {
			continue
		}

		fi, err := os.Lstat(filepath.Join(bucketPath, partPath))
		if err != nil {
			continue
		}

		parts = append(parts, s3response.Part{
			PartNumber:        pn,
			ETag:              "",
			LastModified:      fi.ModTime(),
			Size:              fi.Size(),
			ChecksumCRC32:     checksum.CRC32,
			ChecksumCRC32C:    checksum.CRC32C,
			ChecksumSHA1:      checksum.SHA1,
			ChecksumSHA256:    checksum.SHA256,
			ChecksumCRC64NVME: checksum.CRC64NVME,
		})
	}

	sort.Slice(parts,
		func(i int, j int) bool { return parts[i].PartNumber < parts[j].PartNumber })

	oldLen := len(parts)
	if maxParts > 0 && len(parts) > maxParts {
		parts = parts[:maxParts]
	}
	newLen := len(parts)

	nextpart := 0
	if len(parts) != 0 {
		nextpart = parts[len(parts)-1].PartNumber
	}

	// userMetaData := make(map[string]string)
	// upiddir := filepath.Join(objdir, uploadID)
	// p.loadObjectMetaData(bucket, upiddir, nil, userMetaData)

	return s3response.ListPartsResult{
		Bucket:               bucket,
		IsTruncated:          oldLen != newLen,
		Key:                  object,
		MaxParts:             maxParts,
		NextPartNumberMarker: nextpart,
		PartNumberMarker:     partNumberMarker,
		Parts:                parts,
		UploadID:             uploadID,
		StorageClass:         types.StorageClassStandard,
		ChecksumAlgorithm:    checksum.Algorithm,
		ChecksumType:         checksum.Type,
	}, nil
}

type hashConfig struct {
	value    *string
	hashType utils.HashType
}

func (p *Posix) UploadPart(ctx context.Context, input *s3.UploadPartInput) (*s3.UploadPartOutput, error) {
	acct, ok := ctx.Value("account").(Account)
	if !ok {
		acct = Account{}
	}

	if input.Bucket == nil {
		return nil, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	bucket := *input.Bucket
	object := *input.Key
	uploadID := *input.UploadId
	part := input.PartNumber
	length := int64(0)
	if input.ContentLength != nil {
		length = *input.ContentLength
	}
	r := input.Body

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return nil, err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return nil, fmt.Errorf("stat bucket: %w", err)
	}

	sum := sha256.Sum256([]byte(object))
	objdir := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", sum))
	mpPath := filepath.Join(objdir, uploadID)

	_, err = os.Stat(filepath.Join(bucketPath, mpPath))
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}
	if err != nil {
		return nil, fmt.Errorf("stat uploadid: %w", err)
	}

	partPath := filepath.Join(mpPath, fmt.Sprintf("%v", *part))

	f, err := p.openTmpFile(filepath.Join(bucketPath, objdir),
		bucketPath, partPath, length, acct, doFalloc, p.forceNoTmpFile)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return nil, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return nil, fmt.Errorf("open temp file: %w", err)
	}
	defer f.cleanup()

	hash := md5.New()
	tr := io.TeeReader(r, hash)

	hashConfigs := []hashConfig{
		{input.ChecksumCRC32, utils.HashTypeCRC32},
		{input.ChecksumCRC32C, utils.HashTypeCRC32C},
		{input.ChecksumSHA1, utils.HashTypeSha1},
		{input.ChecksumSHA256, utils.HashTypeSha256},
		{input.ChecksumCRC64NVME, utils.HashTypeCRC64NVME},
	}

	var hashRdr *utils.HashReader
	for _, config := range hashConfigs {
		if config.value != nil {
			hashRdr, err = utils.NewHashReader(tr, *config.value, config.hashType)
			if err != nil {
				return nil, fmt.Errorf("initialize hash reader: %w", err)
			}

			tr = hashRdr
		}
	}

	// If only the checksum algorithm is provided register
	// a new HashReader to calculate the object checksum
	if hashRdr == nil && input.ChecksumAlgorithm != "" {
		hashRdr, err = utils.NewHashReader(tr, "", utils.HashType(strings.ToLower(string(input.ChecksumAlgorithm))))
		if err != nil {
			return nil, fmt.Errorf("initialize hash reader: %w", err)
		}

		tr = hashRdr
	}

	checksums, chErr := p.retrieveChecksums(nil, bucket, mpPath)
	if chErr != nil && !errors.Is(chErr, ErrNoSuchKey) {
		return nil, fmt.Errorf("retreive mp checksum: %w", chErr)
	}

	// If checksum isn't provided for the part,
	// but it has been provided on mp initalization
	if hashRdr == nil && chErr == nil && checksums.Algorithm != "" {
		return nil, s3err.GetChecksumTypeMismatchErr(checksums.Algorithm, "null")
	}

	// Check if the provided checksum algorithm match
	// the one specified on mp initialization
	if hashRdr != nil && chErr == nil && checksums.Type != "" {
		algo := types.ChecksumAlgorithm(strings.ToUpper(string(hashRdr.Type())))
		if checksums.Algorithm != algo {
			return nil, s3err.GetChecksumTypeMismatchErr(checksums.Algorithm, algo)
		}
	}

	_, err = io.Copy(f, tr)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return nil, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return nil, fmt.Errorf("write part data: %w", err)
	}

	etag := GenerateEtag(hash)
	// err = p.meta.StoreAttribute(f.File(), bucket, partPath, etagkey, []byte(etag))
	// if err != nil {
	// 	return nil, fmt.Errorf("set etag attr: %w", err)
	// }

	res := &s3.UploadPartOutput{
		ETag: &etag,
	}

	if hashRdr != nil {
		// checksum := s3response.Checksum{
		// 	Algorithm: input.ChecksumAlgorithm,
		// }

		// Validate the provided checksum
		sum := hashRdr.Sum()
		switch hashRdr.Type() {
		case utils.HashTypeCRC32:
			//checksum.CRC32 = &sum
			res.ChecksumCRC32 = &sum
		case utils.HashTypeCRC32C:
			//checksum.CRC32C = &sum
			res.ChecksumCRC32C = &sum
		case utils.HashTypeSha1:
			//checksum.SHA1 = &sum
			res.ChecksumSHA1 = &sum
		case utils.HashTypeSha256:
			//checksum.SHA256 = &sum
			res.ChecksumSHA256 = &sum
		case utils.HashTypeCRC64NVME:
			//checksum.CRC64NVME = &sum
			res.ChecksumCRC64NVME = &sum
		}
	}

	err = f.link()
	if err != nil {
		return nil, fmt.Errorf("link object in namespace: %w", err)
	}

	return res, nil
}

func (p *Posix) UploadPartCopy(ctx context.Context, upi *s3.UploadPartCopyInput) (s3response.CopyPartResult, error) {
	acct, ok := ctx.Value("account").(Account)
	if !ok {
		acct = Account{}
	}

	if upi.Bucket == nil {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if upi.Key == nil {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	bucketPath, err := p.bucketCache.getBucketPath(*upi.Bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.CopyPartResult{}, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("stat bucket: %w", err)
	}

	sum := sha256.Sum256([]byte(*upi.Key))
	objdir := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", sum))

	_, err = os.Stat(filepath.Join(bucketPath, objdir, *upi.UploadId))
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchUpload)
	}
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("stat uploadid: %w", err)
	}

	partPath := filepath.Join(objdir, *upi.UploadId, fmt.Sprintf("%v", *upi.PartNumber))

	srcBucket, srcObject, srcVersionId, err := ParseCopySource(*upi.CopySource)
	if err != nil {
		return s3response.CopyPartResult{}, err
	}
	srcBucketPath, err := p.bucketCache.getBucketPath(srcBucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.CopyPartResult{}, err
	}

	_, err = os.Stat(srcBucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("stat bucket: %w", err)
	}

	objPath := filepath.Join(srcBucketPath, srcObject)
	fi, err := os.Stat(objPath)
	if errors.Is(err, fs.ErrNotExist) {
		// if p.versioningEnabled() && vEnabled {
		// 	return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchVersion)
		// }
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("stat object: %w", err)
	}

	startOffset, length, err := ParseCopySourceRange(fi.Size(), *upi.CopySourceRange)
	if err != nil {
		return s3response.CopyPartResult{}, err
	}

	f, err := p.openTmpFile(filepath.Join(bucketPath, objdir),
		bucketPath, partPath, length, acct, doFalloc, p.forceNoTmpFile)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return s3response.CopyPartResult{}, fmt.Errorf("open temp file: %w", err)
	}
	defer f.cleanup()

	srcf, err := os.Open(objPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("open object: %w", err)
	}
	defer srcf.Close()

	rdr := io.NewSectionReader(srcf, startOffset, length)
	hash := md5.New()
	tr := io.TeeReader(rdr, hash)

	mpChecksums, err := p.retrieveChecksums(nil, *upi.Bucket, filepath.Join(objdir, *upi.UploadId))
	if err != nil && !errors.Is(err, ErrNoSuchKey) {
		return s3response.CopyPartResult{}, fmt.Errorf("retreive mp checksums: %w", err)
	}

	checksums, err := p.retrieveChecksums(nil, objPath, "")
	if err != nil && !errors.Is(err, ErrNoSuchKey) {
		return s3response.CopyPartResult{}, fmt.Errorf("retreive object part checksums: %w", err)
	}

	// TODO: Should the checksum be recalculated or just copied ?
	var hashRdr *utils.HashReader
	if mpChecksums.Algorithm != "" {
		if checksums.Algorithm == "" || mpChecksums.Algorithm != checksums.Algorithm {
			hashRdr, err = utils.NewHashReader(tr, "", utils.HashType(strings.ToLower(string(mpChecksums.Algorithm))))
			if err != nil {
				return s3response.CopyPartResult{}, fmt.Errorf("initialize hash reader: %w", err)
			}

			tr = hashRdr
		}
	}

	_, err = io.Copy(f, tr)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return s3response.CopyPartResult{}, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return s3response.CopyPartResult{}, fmt.Errorf("copy part data: %w", err)
	}

	if checksums.Algorithm != "" {
		if mpChecksums.Algorithm == "" {
			checksums = s3response.Checksum{}
		} else {
			if hashRdr == nil {
				// err := p.storeChecksums(f.File(), objPath, "", checksums)
				// if err != nil {
				// 	return s3response.CopyPartResult{}, fmt.Errorf("store part checksum: %w", err)
				// }
			}
		}
	}
	if hashRdr != nil {
		algo := types.ChecksumAlgorithm(strings.ToUpper(string(hashRdr.Type())))
		checksums = s3response.Checksum{
			Algorithm: algo,
		}

		sum := hashRdr.Sum()
		switch algo {
		case types.ChecksumAlgorithmCrc32:
			checksums.CRC32 = &sum
		case types.ChecksumAlgorithmCrc32c:
			checksums.CRC32C = &sum
		case types.ChecksumAlgorithmSha1:
			checksums.SHA1 = &sum
		case types.ChecksumAlgorithmSha256:
			checksums.SHA256 = &sum
		case types.ChecksumAlgorithmCrc64nvme:
			checksums.CRC64NVME = &sum
		}
	}

	etag := GenerateEtag(hash)

	err = f.link()
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("link object in namespace: %w", err)
	}

	fi, err = os.Stat(filepath.Join(bucketPath, partPath))
	if err != nil {
		return s3response.CopyPartResult{}, fmt.Errorf("stat part path: %w", err)
	}

	return s3response.CopyPartResult{
		ETag:                &etag,
		LastModified:        fi.ModTime(),
		CopySourceVersionId: srcVersionId,
		ChecksumCRC32:       checksums.CRC32,
		ChecksumCRC32C:      checksums.CRC32C,
		ChecksumSHA1:        checksums.SHA1,
		ChecksumSHA256:      checksums.SHA256,
		ChecksumCRC64NVME:   checksums.CRC64NVME,
	}, nil
}

func (p *Posix) PutObject(ctx context.Context, po s3response.PutObjectInput) (s3response.PutObjectOutput, error) {
	acct, ok := ctx.Value("account").(Account)
	if !ok {
		acct = Account{}
	}

	if po.Bucket == nil {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if po.Key == nil {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	// Override the checksum algorithm with default: CRC64NVME
	if po.ChecksumAlgorithm == "" {
		po.ChecksumAlgorithm = types.ChecksumAlgorithmCrc64nvme
	}

	bucketPath, err := p.bucketCache.getBucketPath(*po.Bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.PutObjectOutput{}, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.PutObjectOutput{}, fmt.Errorf("stat bucket: %w", err)
	}

	tags, err := ParseObjectTags(getString(po.Tagging))
	if err != nil {
		return s3response.PutObjectOutput{}, err
	}

	name := filepath.Join(bucketPath, *po.Key)

	uid, gid, doChown := p.getChownIDs(acct)

	contentLength := int64(0)
	if po.ContentLength != nil {
		contentLength = *po.ContentLength
	}
	if strings.HasSuffix(*po.Key, "/") {
		// object is directory
		if contentLength != 0 {
			// posix directories can't contain data, send error
			// if reuests has a data payload associated with a
			// directory object
			return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrDirectoryObjectContainsData)
		}

		err = MkdirAll(name, uid, gid, doChown, p.newDirPerm)
		if err != nil {
			if errors.Is(err, syscall.EDQUOT) {
				return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrQuotaExceeded)
			}
			return s3response.PutObjectOutput{}, err
		}
		// for directory object no version is created
		return s3response.PutObjectOutput{
			ETag: emptyMD5,
		}, nil
	}
	// object is file
	d, err := os.Stat(name)
	if err == nil && d.IsDir() {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrExistingObjectIsDirectory)
	}
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if errors.Is(err, syscall.ENOTDIR) {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrObjectParentIsFile)
	}
	if err != nil && !errors.Is(err, fs.ErrNotExist) {
		return s3response.PutObjectOutput{}, fmt.Errorf("stat object: %w", err)
	}

	f, err := p.openTmpFile(filepath.Join(bucketPath, metaTmpDir),
		bucketPath, *po.Key, contentLength, acct, doFalloc, p.forceNoTmpFile)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return s3response.PutObjectOutput{}, fmt.Errorf("open temp file: %w", err)
	}
	defer f.cleanup()

	hash := md5.New()
	rdr := io.TeeReader(po.Body, hash)

	hashConfigs := []hashConfig{
		{po.ChecksumCRC32, utils.HashTypeCRC32},
		{po.ChecksumCRC32C, utils.HashTypeCRC32C},
		{po.ChecksumSHA1, utils.HashTypeSha1},
		{po.ChecksumSHA256, utils.HashTypeSha256},
		{po.ChecksumCRC64NVME, utils.HashTypeCRC64NVME},
	}
	var hashRdr *utils.HashReader

	for _, config := range hashConfigs {
		if config.value != nil {
			hashRdr, err = utils.NewHashReader(rdr, *config.value, config.hashType)
			if err != nil {
				return s3response.PutObjectOutput{}, fmt.Errorf("initialize hash reader: %w", err)
			}

			rdr = hashRdr
		}
	}

	// If only the checksum algorithm is provided register
	// a new HashReader to calculate the object checksum
	if hashRdr == nil && po.ChecksumAlgorithm != "" {
		hashRdr, err = utils.NewHashReader(rdr, "", utils.HashType(strings.ToLower(string(po.ChecksumAlgorithm))))
		if err != nil {
			return s3response.PutObjectOutput{}, fmt.Errorf("initialize hash reader: %w", err)
		}

		rdr = hashRdr
	}

	_, err = io.Copy(f, rdr)
	if err != nil {
		if errors.Is(err, syscall.EDQUOT) {
			return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrQuotaExceeded)
		}
		return s3response.PutObjectOutput{}, fmt.Errorf("write object data: %w", err)
	}

	dir := filepath.Dir(name)
	if dir != "" {
		err = MkdirAll(dir, uid, gid, doChown, p.newDirPerm)
		if err != nil {
			return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrExistingObjectIsDirectory)
		}
	}

	etag := GenerateEtag(hash)

	// if the versioning is enabled, generate a new versionID for the object
	var versionID string

	checksum := s3response.Checksum{}

	// Store the calculated checksum in the object metadata
	if hashRdr != nil {
		// The checksum type is always FULL_OBJECT for PutObject
		checksum.Type = types.ChecksumTypeFullObject

		sum := hashRdr.Sum()
		switch hashRdr.Type() {
		case utils.HashTypeCRC32:
			checksum.CRC32 = &sum
			checksum.Algorithm = types.ChecksumAlgorithmCrc32
		case utils.HashTypeCRC32C:
			checksum.CRC32C = &sum
			checksum.Algorithm = types.ChecksumAlgorithmCrc32c
		case utils.HashTypeSha1:
			checksum.SHA1 = &sum
			checksum.Algorithm = types.ChecksumAlgorithmSha1
		case utils.HashTypeSha256:
			checksum.SHA256 = &sum
			checksum.Algorithm = types.ChecksumAlgorithmSha256
		case utils.HashTypeCRC64NVME:
			checksum.CRC64NVME = &sum
			checksum.Algorithm = types.ChecksumAlgorithmCrc64nvme
		}
	}

	err = f.link()
	if errors.Is(err, syscall.EEXIST) {
		return s3response.PutObjectOutput{
			ETag:      etag,
			VersionID: versionID,
		}, nil
	}
	if err != nil {
		return s3response.PutObjectOutput{}, s3err.GetAPIError(s3err.ErrExistingObjectIsDirectory)
	}

	// Set object tagging
	if tags != nil {
		err := p.PutObjectTagging(ctx, *po.Bucket, *po.Key, tags)
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.PutObjectOutput{
				ETag:      etag,
				VersionID: versionID,
			}, nil
		}
		if err != nil {
			return s3response.PutObjectOutput{}, err
		}
	}

	// Set object legal hold
	if po.ObjectLockLegalHoldStatus == types.ObjectLockLegalHoldStatusOn {
		err := p.PutObjectLegalHold(ctx, *po.Bucket, *po.Key, "", true)
		if err != nil {
			return s3response.PutObjectOutput{}, err
		}
	}

	// Set object retention
	if po.ObjectLockMode != "" {
		retention := types.ObjectLockRetention{
			Mode:            types.ObjectLockRetentionMode(po.ObjectLockMode),
			RetainUntilDate: po.ObjectLockRetainUntilDate,
		}
		retParsed, err := json.Marshal(retention)
		if err != nil {
			return s3response.PutObjectOutput{}, fmt.Errorf("parse object lock retention: %w", err)
		}
		err = p.PutObjectRetention(ctx, *po.Bucket, *po.Key, "", true, retParsed)
		if err != nil {
			return s3response.PutObjectOutput{}, err
		}
	}

	return s3response.PutObjectOutput{
		ETag:              etag,
		VersionID:         versionID,
		ChecksumCRC32:     checksum.CRC32,
		ChecksumCRC32C:    checksum.CRC32C,
		ChecksumSHA1:      checksum.SHA1,
		ChecksumSHA256:    checksum.SHA256,
		ChecksumCRC64NVME: checksum.CRC64NVME,
		ChecksumType:      checksum.Type,
	}, nil
}

func (p *Posix) DeleteObject(ctx context.Context, input *s3.DeleteObjectInput) (*s3.DeleteObjectOutput, error) {
	if input.Bucket == nil {
		return nil, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	bucket := *input.Bucket
	object := *input.Key
	//isDir := strings.HasSuffix(object, "/")

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return nil, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return nil, fmt.Errorf("stat bucket: %w", err)
	}

	objpath := filepath.Join(bucketPath, object)

	fi, err := os.Stat(objpath)
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return nil, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if errors.Is(err, fs.ErrNotExist) || errors.Is(err, syscall.ENOTDIR) {
		// AWS returns success if the object does not exist
		return &s3.DeleteObjectOutput{}, nil
	}
	if err != nil {
		return &s3.DeleteObjectOutput{}, fmt.Errorf("stat object: %w", err)
	}
	if strings.HasSuffix(object, "/") && !fi.IsDir() {
		// requested object is expecting a directory with a trailing
		// slash, but the object is not a directory. treat this as
		// a non-existent object.
		// AWS returns success if the object does not exist
		return &s3.DeleteObjectOutput{}, nil
	}
	if !strings.HasSuffix(object, "/") && fi.IsDir() {
		// requested object is expecting a file, but the object is a
		// directory. treat this as a non-existent object.
		// AWS returns success if the object does not exist
		return &s3.DeleteObjectOutput{}, nil
	}

	err = os.Remove(objpath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if errors.Is(err, syscall.ENOTEMPTY) {

		return &s3.DeleteObjectOutput{}, nil
	}
	if err != nil {
		return nil, fmt.Errorf("delete object: %w", err)
	}

	// err = p.meta.DeleteAttributes(bucket, object)
	// if err != nil {
	// 	return nil, fmt.Errorf("delete object attributes: %w", err)
	// }

	p.removeParents(bucketPath, object)

	return &s3.DeleteObjectOutput{}, nil
}

func (p *Posix) removeParents(bucketPath, object string) {
	// this will remove all parent directories that were not
	// specifically uploaded with a put object. we detect
	// this with a special attribute to indicate these. stop
	// at either the bucket or the first parent we encounter
	// with the attribute, whichever comes first.

	// Remove the last path separator for the directory objects
	// to correctly detect the parent in the loop
	objPath := strings.TrimSuffix(object, "/")
	for {
		parent := filepath.Dir(objPath)
		if parent == string(filepath.Separator) || parent == "." {
			// stop removing parents if we hit the bucket directory.
			break
		}

		err := os.Remove(filepath.Join(bucketPath, parent))
		if err != nil {
			break
		}

		objPath = parent
	}
}

func (p *Posix) DeleteObjects(ctx context.Context, input *s3.DeleteObjectsInput) (s3response.DeleteResult, error) {
	// delete object already checks bucket
	delResult, errs := []types.DeletedObject{}, []types.Error{}
	for _, obj := range input.Delete.Objects {
		//TODO: Make the delete operation concurrent
		res, err := p.DeleteObject(ctx, &s3.DeleteObjectInput{
			Bucket:    input.Bucket,
			Key:       obj.Key,
			VersionId: obj.VersionId,
		})
		if err == nil {
			delEntity := types.DeletedObject{
				Key:          obj.Key,
				DeleteMarker: res.DeleteMarker,
				VersionId:    obj.VersionId,
			}
			if delEntity.DeleteMarker != nil && *delEntity.DeleteMarker {
				delEntity.DeleteMarkerVersionId = res.VersionId
			}

			delResult = append(delResult, delEntity)
		} else {
			serr, ok := err.(s3err.APIError)
			if ok {
				errs = append(errs, types.Error{
					Key:     obj.Key,
					Code:    &serr.Code,
					Message: &serr.Description,
				})
			} else {
				errs = append(errs, types.Error{
					Key:     obj.Key,
					Code:    GetPtrFromString("InternalError"),
					Message: GetPtrFromString(err.Error()),
				})
			}
		}
	}

	return s3response.DeleteResult{
		Deleted: delResult,
		Error:   errs,
	}, nil
}

func (p *Posix) GetObject(_ context.Context, input *s3.GetObjectInput) (*s3.GetObjectOutput, error) {
	if input.Bucket == nil {
		return nil, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	var versionId string
	if input.VersionId != nil {
		versionId = *input.VersionId
	}
	bucket := *input.Bucket
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return nil, err
	}
	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return nil, fmt.Errorf("stat bucket: %w", err)
	}

	object := *input.Key
	objPath := filepath.Join(bucketPath, object)

	fi, err := os.Stat(objPath)
	if errors.Is(err, fs.ErrNotExist) || errors.Is(err, syscall.ENOTDIR) {
		if versionId != "" {
			return nil, s3err.GetAPIError(s3err.ErrInvalidVersionId)
		}
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return nil, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if err != nil {
		return nil, fmt.Errorf("stat object: %w", err)
	}

	if strings.HasSuffix(object, "/") && !fi.IsDir() {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if !strings.HasSuffix(object, "/") && fi.IsDir() {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	objSize := fi.Size()
	startOffset, length, isValid, err := ParseObjectRange(objSize, *input.Range)
	if err != nil {
		return nil, err
	}

	if fi.IsDir() {
		// directory objects are always 0 len
		objSize = 0
		length = 0
	}

	var contentRange string
	if isValid {
		contentRange = fmt.Sprintf("bytes %v-%v/%v",
			startOffset, startOffset+length-1, objSize)
	}

	if fi.IsDir() {
		userMetaData := make(map[string]string)
		var tagCount *int32
		tags, err := p.getAttrTags(bucket, object)
		if err != nil && !errors.Is(err, s3err.GetAPIError(s3err.ErrBucketTaggingNotFound)) {
			return nil, err
		}
		if tags != nil {
			tgCount := int32(len(tags))
			tagCount = &tgCount
		}

		return &s3.GetObjectOutput{
			AcceptRanges:       GetPtrFromString("bytes"),
			ContentLength:      &length,
			ContentEncoding:    ValueToPtr(""),
			ContentType:        ValueToPtr(""),
			ContentLanguage:    ValueToPtr(""),
			ContentDisposition: ValueToPtr(""),
			CacheControl:       ValueToPtr(""),
			ExpiresString:      ValueToPtr(""),
			ETag:               ValueToPtr(""),
			LastModified:       GetTimePtr(fi.ModTime()),
			Metadata:           userMetaData,
			TagCount:           tagCount,
			ContentRange:       &contentRange,
			StorageClass:       types.StorageClassStandard,
			VersionId:          &versionId,
		}, nil
	}

	userMetaData := make(map[string]string)
	var tagCount *int32
	tags, err := p.getAttrTags(bucket, object)
	if err != nil && !errors.Is(err, s3err.GetAPIError(s3err.ErrBucketTaggingNotFound)) {
		return nil, err
	}
	if tags != nil {
		tgCount := int32(len(tags))
		tagCount = &tgCount
	}

	f, err := os.Open(objPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if err != nil {
		return nil, fmt.Errorf("open object: %w", err)
	}

	var checksums s3response.Checksum
	var cType types.ChecksumType
	// Skip the checksums retreival if object isn't requested fully
	if input.ChecksumMode == types.ChecksumModeEnabled && length-startOffset == objSize {
		checksums, err = p.retrieveChecksums(f, bucket, object)
		if err != nil && !errors.Is(err, ErrNoSuchKey) {
			return nil, fmt.Errorf("get object checksums: %w", err)
		}
		if checksums.Type != "" {
			cType = checksums.Type
		}
	}

	// using an os.File allows zero-copy sendfile via io.Copy(os.File, net.Conn)
	var body io.ReadCloser = f
	if startOffset != 0 || length != objSize {
		rdr := io.NewSectionReader(f, startOffset, length)
		body = &FileSectionReadCloser{R: rdr, F: f}
	}

	return &s3.GetObjectOutput{
		AcceptRanges:       GetPtrFromString("bytes"),
		ContentLength:      &length,
		ContentEncoding:    ValueToPtr(""),
		ContentType:        ValueToPtr(""),
		ContentDisposition: ValueToPtr(""),
		ContentLanguage:    ValueToPtr(""),
		CacheControl:       ValueToPtr(""),
		ExpiresString:      ValueToPtr(""),
		ETag:               ValueToPtr(""),
		LastModified:       GetTimePtr(fi.ModTime()),
		Metadata:           userMetaData,
		TagCount:           tagCount,
		ContentRange:       &contentRange,
		StorageClass:       types.StorageClassStandard,
		VersionId:          &versionId,
		Body:               body,
		ChecksumCRC32:      checksums.CRC32,
		ChecksumCRC32C:     checksums.CRC32C,
		ChecksumSHA1:       checksums.SHA1,
		ChecksumSHA256:     checksums.SHA256,
		ChecksumCRC64NVME:  checksums.CRC64NVME,
		ChecksumType:       cType,
	}, nil
}

func (p *Posix) HeadObject(ctx context.Context, input *s3.HeadObjectInput) (*s3.HeadObjectOutput, error) {
	if input.Bucket == nil {
		return nil, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	if input.Key == nil {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	versionId := GetStringFromPtr(input.VersionId)
	bucket := *input.Bucket
	object := *input.Key

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return nil, err
	}

	if input.PartNumber != nil {
		uploadId, sum, err := p.retrieveUploadId(bucket, object)
		if err != nil {
			return nil, err
		}

		ents, err := os.ReadDir(filepath.Join(bucketPath, metaTmpMultipartDir, fmt.Sprintf("%x", sum), uploadId))
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
		}
		if err != nil {
			return nil, fmt.Errorf("read parts: %w", err)
		}

		partPath := filepath.Join(metaTmpMultipartDir, fmt.Sprintf("%x", sum), uploadId, fmt.Sprintf("%v", *input.PartNumber))

		part, err := os.Stat(filepath.Join(bucketPath, partPath))
		if errors.Is(err, fs.ErrNotExist) {
			return nil, s3err.GetAPIError(s3err.ErrInvalidPart)
		}
		if errors.Is(err, syscall.ENAMETOOLONG) {
			return nil, s3err.GetAPIError(s3err.ErrKeyTooLong)
		}
		if err != nil {
			return nil, fmt.Errorf("stat part: %w", err)
		}

		size := part.Size()

		startOffset, length, isValid, err := ParseObjectRange(size, getString(input.Range))
		if err != nil {
			return nil, err
		}

		var contentRange string
		if isValid {
			contentRange = fmt.Sprintf("bytes %v-%v/%v",
				startOffset, startOffset+length-1, size)
		}

		// b, err := p.meta.RetrieveAttribute(nil, bucket, partPath, etagkey)
		// etag := string(b)
		// if err != nil {
		// 	etag = ""
		// }
		partsCount := int32(len(ents))

		return &s3.HeadObjectOutput{
			AcceptRanges:  GetPtrFromString("bytes"),
			LastModified:  GetTimePtr(part.ModTime()),
			ETag:          ValueToPtr(""),
			PartsCount:    &partsCount,
			ContentLength: &length,
			StorageClass:  types.StorageClassStandard,
			ContentRange:  &contentRange,
		}, nil
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return nil, fmt.Errorf("stat bucket: %w", err)
	}

	objPath := filepath.Join(bucketPath, object)

	fi, err := os.Stat(objPath)
	if errors.Is(err, fs.ErrNotExist) || errors.Is(err, syscall.ENOTDIR) {
		if versionId != "" {
			return nil, s3err.GetAPIError(s3err.ErrInvalidVersionId)
		}
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if errors.Is(err, syscall.ENAMETOOLONG) {
		return nil, s3err.GetAPIError(s3err.ErrKeyTooLong)
	}
	if err != nil {
		return nil, fmt.Errorf("stat object: %w", err)
	}
	if strings.HasSuffix(object, "/") && !fi.IsDir() {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}
	if !strings.HasSuffix(object, "/") && fi.IsDir() {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchKey)
	}

	userMetaData := make(map[string]string)

	size := fi.Size()

	startOffset, length, isValid, err := ParseObjectRange(size, getString(input.Range))
	if err != nil {
		return nil, err
	}

	var contentRange string
	if isValid {
		contentRange = fmt.Sprintf("bytes %v-%v/%v",
			startOffset, startOffset+length-1, size)
	}

	var objectLockLegalHoldStatus types.ObjectLockLegalHoldStatus
	status, err := p.GetObjectLegalHold(ctx, bucket, object, versionId)
	if err == nil {
		if *status {
			objectLockLegalHoldStatus = types.ObjectLockLegalHoldStatusOn
		} else {
			objectLockLegalHoldStatus = types.ObjectLockLegalHoldStatusOff
		}
	}

	var objectLockMode types.ObjectLockMode
	var objectLockRetainUntilDate *time.Time
	retention, err := p.GetObjectRetention(ctx, bucket, object, versionId)
	if err == nil {
		var config types.ObjectLockRetention
		if err := json.Unmarshal(retention, &config); err == nil {
			objectLockMode = types.ObjectLockMode(config.Mode)
			objectLockRetainUntilDate = config.RetainUntilDate
		}
	}

	var checksums s3response.Checksum
	var cType types.ChecksumType
	if input.ChecksumMode == types.ChecksumModeEnabled {
		checksums, err = p.retrieveChecksums(nil, bucket, object)
		if err != nil && !errors.Is(err, ErrNoSuchKey) {
			return nil, fmt.Errorf("get object checksums: %w", err)
		}
		if checksums.Type != "" {
			cType = checksums.Type
		}
	}

	return &s3.HeadObjectOutput{
		ContentLength:             &length,
		AcceptRanges:              GetPtrFromString("bytes"),
		ContentRange:              &contentRange,
		ContentType:               ValueToPtr(""),
		ContentEncoding:           ValueToPtr(""),
		ContentDisposition:        ValueToPtr(""),
		ContentLanguage:           ValueToPtr(""),
		CacheControl:              ValueToPtr(""),
		ExpiresString:             ValueToPtr(""),
		ETag:                      ValueToPtr(""),
		LastModified:              GetTimePtr(fi.ModTime()),
		Metadata:                  userMetaData,
		ObjectLockLegalHoldStatus: objectLockLegalHoldStatus,
		ObjectLockMode:            objectLockMode,
		ObjectLockRetainUntilDate: objectLockRetainUntilDate,
		StorageClass:              types.StorageClassStandard,
		VersionId:                 &versionId,
		ChecksumCRC32:             checksums.CRC32,
		ChecksumCRC32C:            checksums.CRC32C,
		ChecksumSHA1:              checksums.SHA1,
		ChecksumSHA256:            checksums.SHA256,
		ChecksumCRC64NVME:         checksums.CRC64NVME,
		ChecksumType:              cType,
	}, nil
}

func (p *Posix) GetObjectAttributes(ctx context.Context, input *s3.GetObjectAttributesInput) (s3response.GetObjectAttributesResponse, error) {
	data, err := p.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket:       input.Bucket,
		Key:          input.Key,
		VersionId:    input.VersionId,
		ChecksumMode: types.ChecksumModeEnabled,
	})
	if err != nil {
		if errors.Is(err, s3err.GetAPIError(s3err.ErrMethodNotAllowed)) && data != nil {
			return s3response.GetObjectAttributesResponse{
				DeleteMarker: data.DeleteMarker,
				VersionId:    data.VersionId,
			}, s3err.GetAPIError(s3err.ErrNoSuchKey)
		}

		return s3response.GetObjectAttributesResponse{}, err
	}

	return s3response.GetObjectAttributesResponse{
		ETag:         TrimEtag(data.ETag),
		ObjectSize:   data.ContentLength,
		StorageClass: data.StorageClass,
		LastModified: data.LastModified,
		VersionId:    data.VersionId,
		DeleteMarker: data.DeleteMarker,
		Checksum: &types.Checksum{
			ChecksumCRC32:     data.ChecksumCRC32,
			ChecksumCRC32C:    data.ChecksumCRC32C,
			ChecksumSHA1:      data.ChecksumSHA1,
			ChecksumSHA256:    data.ChecksumSHA256,
			ChecksumCRC64NVME: data.ChecksumCRC64NVME,
			ChecksumType:      data.ChecksumType,
		},
	}, nil
}

func (p *Posix) CopyObject(ctx context.Context, input s3response.CopyObjectInput) (s3response.CopyObjectOutput, error) {
	return s3response.CopyObjectOutput{}, s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) ListObjects(ctx context.Context, input *s3.ListObjectsInput) (s3response.ListObjectsResult, error) {
	if input.Bucket == nil {
		return s3response.ListObjectsResult{}, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	bucket := *input.Bucket
	prefix := ""
	if input.Prefix != nil {
		prefix = *input.Prefix
	}
	marker := ""
	if input.Marker != nil {
		marker = *input.Marker
	}
	delim := ""
	if input.Delimiter != nil {
		delim = *input.Delimiter
	}
	maxkeys := int32(0)
	if input.MaxKeys != nil {
		maxkeys = *input.MaxKeys
	}
	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.ListObjectsResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.ListObjectsResult{}, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.ListObjectsResult{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.ListObjectsResult{}, fmt.Errorf("stat bucket: %w", err)
	}

	fileSystem := os.DirFS(bucketPath)
	results, err := Walk(ctx, fileSystem, prefix, delim, marker, maxkeys,
		p.fileToObj(bucket), []string{metaTmpDir})
	if err != nil {
		return s3response.ListObjectsResult{}, fmt.Errorf("walk %v: %w", bucket, err)
	}

	return s3response.ListObjectsResult{
		CommonPrefixes: results.CommonPrefixes,
		Contents:       results.Objects,
		Delimiter:      GetPtrFromString(delim),
		Marker:         GetPtrFromString(marker),
		NextMarker:     GetPtrFromString(results.NextMarker),
		Prefix:         GetPtrFromString(prefix),
		IsTruncated:    &results.Truncated,
		MaxKeys:        &maxkeys,
		Name:           &bucket,
	}, nil
}

func (p *Posix) fileToObj(bucket string) GetObjFunc {
	return func(path string, d fs.DirEntry) (s3response.Object, error) {
		if d.IsDir() {
			return s3response.Object{}, ErrSkipObj

		}

		// Retreive the object checksum algorithm
		checksums, err := p.retrieveChecksums(nil, bucket, path)
		if err != nil && !errors.Is(err, ErrNoSuchKey) {
			return s3response.Object{}, ErrSkipObj
		}

		fi, err := d.Info()
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.Object{}, ErrSkipObj
		}
		if err != nil {
			return s3response.Object{}, fmt.Errorf("get fileinfo: %w", err)
		}

		size := fi.Size()
		mtime := fi.ModTime()

		return s3response.Object{
			ETag:              ValueToPtr(""),
			Key:               &path,
			LastModified:      &mtime,
			Size:              &size,
			StorageClass:      types.ObjectStorageClassStandard,
			ChecksumAlgorithm: []types.ChecksumAlgorithm{checksums.Algorithm},
			ChecksumType:      checksums.Type,
			Owner:             nil,
		}, nil
	}
}

func (p *Posix) ListObjectsV2(ctx context.Context, input *s3.ListObjectsV2Input) (s3response.ListObjectsV2Result, error) {
	if input.Bucket == nil {
		return s3response.ListObjectsV2Result{}, s3err.GetAPIError(s3err.ErrInvalidBucketName)
	}
	bucket := *input.Bucket
	prefix := ""
	if input.Prefix != nil {
		prefix = *input.Prefix
	}
	marker := ""
	if input.ContinuationToken != nil {
		if input.StartAfter != nil {
			marker = max(*input.StartAfter, *input.ContinuationToken)
		} else {
			marker = *input.ContinuationToken
		}
	}
	delim := ""
	if input.Delimiter != nil {
		delim = *input.Delimiter
	}
	maxkeys := int32(0)
	if input.MaxKeys != nil {
		maxkeys = *input.MaxKeys
	}
	//var fetchOwner bool
	// if input.FetchOwner != nil {
	// 	fetchOwner = *input.FetchOwner
	// }

	bucketPath, err := p.bucketCache.getBucketPath(bucket)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			return s3response.ListObjectsV2Result{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
		}
		return s3response.ListObjectsV2Result{}, err
	}

	_, err = os.Stat(bucketPath)
	if errors.Is(err, fs.ErrNotExist) {
		return s3response.ListObjectsV2Result{}, s3err.GetAPIError(s3err.ErrNoSuchBucket)
	}
	if err != nil {
		return s3response.ListObjectsV2Result{}, fmt.Errorf("stat bucket: %w", err)
	}

	fileSystem := os.DirFS(bucketPath)
	results, err := Walk(ctx, fileSystem, prefix, delim, marker, maxkeys,
		p.fileToObj(bucket), []string{metaTmpDir})
	if err != nil {
		return s3response.ListObjectsV2Result{}, fmt.Errorf("walk %v: %w", bucket, err)
	}

	count := int32(len(results.Objects))

	return s3response.ListObjectsV2Result{
		CommonPrefixes:        results.CommonPrefixes,
		Contents:              results.Objects,
		IsTruncated:           &results.Truncated,
		MaxKeys:               &maxkeys,
		Name:                  &bucket,
		KeyCount:              &count,
		Delimiter:             GetPtrFromString(delim),
		ContinuationToken:     GetPtrFromString(marker),
		NextContinuationToken: GetPtrFromString(results.NextMarker),
		Prefix:                GetPtrFromString(prefix),
		StartAfter:            GetPtrFromString(*input.StartAfter),
	}, nil
}

func (p *Posix) PutBucketAcl(_ context.Context, bucket string, data []byte) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetBucketAcl(_ context.Context, input *s3.GetBucketAclInput) ([]byte, error) {
	return nil, nil
}

func (p *Posix) PutBucketTagging(_ context.Context, bucket string, tags map[string]string) error {
	return nil
}

func (p *Posix) GetBucketTagging(_ context.Context, bucket string) (map[string]string, error) {
	return nil, nil
}

func (p *Posix) DeleteBucketTagging(ctx context.Context, bucket string) error {
	return p.PutBucketTagging(ctx, bucket, nil)
}

func (p *Posix) GetObjectTagging(_ context.Context, bucket, object string) (map[string]string, error) {
	return nil, nil
}

func (p *Posix) getAttrTags(bucket, object string) (map[string]string, error) {
	return nil, nil
}

func (p *Posix) PutObjectTagging(_ context.Context, bucket, object string, tags map[string]string) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) DeleteObjectTagging(ctx context.Context, bucket, object string) error {
	return p.PutObjectTagging(ctx, bucket, object, nil)
}

func (p *Posix) PutBucketPolicy(ctx context.Context, bucket string, policy []byte) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetBucketPolicy(_ context.Context, bucket string) ([]byte, error) {
	policy, err := p.bucketCache.getBucketPloicy(bucket)
	if errors.Is(err, fs.ErrNotExist) {
		return nil, s3err.GetAPIError(s3err.ErrNoSuchBucketPolicy)
	}
	return policy, nil
}

func (p *Posix) DeleteBucketPolicy(ctx context.Context, bucket string) error {
	return p.PutBucketPolicy(ctx, bucket, nil)
}

func (p *Posix) PutObjectLockConfiguration(ctx context.Context, bucket string, config []byte) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetObjectLockConfiguration(_ context.Context, bucket string) ([]byte, error) {
	return nil, s3err.GetAPIError(s3err.ErrObjectLockConfigurationNotFound)
}

func (p *Posix) PutObjectLegalHold(_ context.Context, bucket, object, versionId string, status bool) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetObjectLegalHold(_ context.Context, bucket, object, versionId string) (*bool, error) {
	return ValueToPtr(false), s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) PutObjectRetention(_ context.Context, bucket, object, versionId string, bypass bool, retention []byte) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) GetObjectRetention(_ context.Context, bucket, object, versionId string) ([]byte, error) {
	return nil, s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (p *Posix) ChangeBucketOwner(ctx context.Context, bucket string, acl []byte) error {
	return p.PutBucketAcl(ctx, bucket, acl)
}

func (p *Posix) listBucketFileInfos() map[string]fs.FileInfo {
	allBuckets := p.bucketCache.listBucketsPath()
	var fis = make(map[string]fs.FileInfo)
	for bucket, bucketPath := range allBuckets {
		fi, err := os.Stat(bucketPath)
		if err != nil {
			continue
		}
		if !fi.IsDir() {
			// buckets must be a directory
			continue
		}
		fis[bucket] = fi
	}
	return fis
}

func (p *Posix) ListBucketsAndOwners(ctx context.Context) (buckets []s3response.Bucket, err error) {
	fis := p.listBucketFileInfos()

	for key := range fis {
		buckets = append(buckets, s3response.Bucket{
			Name:  key,
			Owner: "",
		})
	}

	sort.SliceStable(buckets, func(i, j int) bool {
		return buckets[i].Name < buckets[j].Name
	})

	return buckets, nil
}

func (p *Posix) retrieveChecksums(f *os.File, bucket, object string) (checksums s3response.Checksum, err error) {
	return s3response.Checksum{}, ErrNoSuchKey
}

func getString(str *string) string {
	if str == nil {
		return ""
	}
	return *str
}
