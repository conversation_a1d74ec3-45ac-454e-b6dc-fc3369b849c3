package s3server

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v4/disk"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/pai/storage"
)

const (
	mountPointPath     = "mountpoint"
	mountPointWritable = ".test_write"
	rootPath           = "/s3gateway"
)

var _ FilesystemAdapter = &Filesystem{}

type Filesystem struct {
	*Posix
	cluster    storage.StorageCluster
	parentPath string // 挂载点有固定规则 /s3gateway/${storagecluster.provider}/${storagecluster.name}
}

func newFilesystemFromStorageCluster(cluster storage.StorageCluster, meta *StorageSetCache) (FilesystemAdapter, error) {
	// 先挂载
	var err error
	parent := path.Join(rootPath, string(cluster.Provider), cluster.Name)
	mountPath := path.Join(parent, mountPointPath)
	err = os.MkdirAll(mountPath, 0755)
	if err != nil {
		return nil, err
	}
	opts := PosixOpts{
		NewDirPerm: fs.FileMode(0755),
	}
	fsAdapter := &Filesystem{
		cluster:    cluster,
		parentPath: parent,
	}
	if fsAdapter.IsMounted() {
		fsAdapter.Posix, err = NewPosix(mountPath, meta, opts)
		if err != nil {
			return nil, err
		}
		return fsAdapter, nil
	}
	err = fsAdapter.Mount(defaultMountTimeout)
	if err != nil {
		return nil, err
	}
	fsAdapter.Posix, err = NewPosix(mountPath, meta, opts)
	if err != nil {
		return nil, err
	}
	return fsAdapter, nil
}

func (fs *Filesystem) GetMountPoint() string {
	return path.Join(fs.parentPath, mountPointPath)
}
func (fs *Filesystem) GetName() string {
	return fs.cluster.Name
}
func (fs *Filesystem) GetType() storage.StorageClusterProvider {
	return fs.cluster.Provider
}

// 挂载点是否可写
func (fs *Filesystem) testMountpointWritable() bool {
	tmpFile := filepath.Join(path.Join(fs.parentPath, mountPointPath), mountPointWritable)
	err := os.WriteFile(tmpFile, []byte("test write"), 0644)
	if err != nil {
		return false
	}
	os.RemoveAll(tmpFile)
	return true
}

func (fs *Filesystem) IsMounted() bool {
	partitions, err := disk.Partitions(true)
	if err != nil {
		log.FromContext(context.Background()).Error(err, "get disk partitions")
		return false
	}
	for _, par := range partitions {
		if par.Mountpoint == path.Join(fs.parentPath, mountPointPath) {
			if strings.Contains(strings.ToLower(par.Fstype), string(fs.cluster.Provider)) {
				return fs.testMountpointWritable()
			}
		}
	}
	return false
}

func (fs *Filesystem) Mount(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	if err := fs.Format(ctx); err != nil {
		return err
	}
	mountCmd := fs.cluster.MountOption.MountCmd
	args := makeMountArgWithMountFlags(fs.cluster.MountOption.Source, path.Join(fs.parentPath, mountPointPath), string(fs.cluster.Provider), fs.cluster.MountOption.Options, fs.cluster.MountOption.MountFlags)
	command := exec.CommandContext(ctx, mountCmd, args...)
	output, err := command.CombinedOutput()
	if err != nil {
		if err.Error() == "wait: no child processes" {
			if command.ProcessState.Success() {
				// We don't consider errNoChildProcesses an error if the process itself succeeded (see - k/k issue #103753).
				if fs.testMountpointWritable() {
					return nil
				}
				return fmt.Errorf("mount point can not writable")
			}
			// Rewrite err with the actual exit error of the process.
			err = &exec.ExitError{ProcessState: command.ProcessState}
		}
		return fmt.Errorf("mount failed: %v\nMounting arguments: %s\nOutput: %s",
			err, args, string(output))
	}
	if fs.testMountpointWritable() {
		return nil
	}
	return fmt.Errorf("mount point can not writable")
}
func (fs *Filesystem) Unmount(timeout time.Duration, force bool) error {
	mountPath := path.Join(fs.parentPath, mountPointPath)
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	command := exec.CommandContext(ctx, "umount", mountPath)
	if force {
		command = exec.CommandContext(ctx, "umount", "-f", mountPath)
	}
	output, err := command.CombinedOutput()
	if ctx.Err() != nil {
		return ctx.Err()
	}
	if err != nil {
		return checkUmountError(mountPath, command, output, err, true)
	}
	return nil
}
func checkUmountError(target string, command *exec.Cmd, output []byte, err error, ignore bool) error {
	if err.Error() == "wait: no child processes" {
		if command.ProcessState.Success() {
			return nil
		}
		err = &exec.ExitError{ProcessState: command.ProcessState}
	}
	if ignore && strings.Contains(string(output), "not mounted") {
		return nil
	}
	return fmt.Errorf("unmount failed: %v\nUnmounting arguments: %s\nOutput: %s", err, target, string(output))
}

func makeMountArgWithMountFlags(source, target, fstype string, options []string, mountFlags []string) []string {
	// Build mount command as follows:
	//   mount [$mountFlags] [-t $fstype] [-o $options] [$source] $target
	mountArgs := []string{}
	mountArgs = append(mountArgs, mountFlags...)
	if len(fstype) > 0 {
		mountArgs = append(mountArgs, "-t", fstype)
	}
	if len(options) > 0 {
		combinedOptions := []string{}
		combinedOptions = append(combinedOptions, options...)
		mountArgs = append(mountArgs, "-o", strings.Join(combinedOptions, ","))
	}
	if len(source) > 0 {
		mountArgs = append(mountArgs, source)
	}
	mountArgs = append(mountArgs, target)
	return mountArgs
}
