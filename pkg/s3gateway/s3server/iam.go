// Copyright 2023 Versity Software
// This file is licensed under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

package s3server

import (
	"errors"

	"xiaoshiai.cn/rune/pkg/s3gateway/s3err"
)

type Role string

const (
	RoleManager  Role = "manager"
	RoleReadOnly Role = "readonly"
	RoleAdmin    Role = "admin"
)

func (r Role) IsValid() bool {
	switch r {
	case RoleAdmin, RoleManager, RoleReadOnly:
		return true
	default:
		return false
	}
}

// Account is a gateway IAM account
type Account struct {
	Access  string `json:"access"`
	Secret  string `json:"secret"`
	Role    Role   `json:"role"`
	UserID  int    `json:"userID"`
	GroupID int    `json:"groupID"`
}

type ListUserAccountsResult struct {
	Accounts []Account
}

// Mutable props, which could be changed when updating an IAM account
type MutableProps struct {
	Secret  *string `json:"secret"`
	Role    Role    `json:"role"`
	UserID  *int    `json:"userID"`
	GroupID *int    `json:"groupID"`
}

func (m MutableProps) Validate() error {
	if m.Role != "" && !m.Role.IsValid() {
		return s3err.GetAPIError(s3err.ErrAdminInvalidUserRole)
	}

	return nil
}

var (
	// ErrUserExists is returned when the user already exists
	ErrUserExists = errors.New("user already exists")
	// ErrNoSuchUser is returned when the user does not exist
	ErrNoSuchUser = errors.New("user not found")
)
