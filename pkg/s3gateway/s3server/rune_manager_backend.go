package s3server

import (
	"bufio"
	"context"
	"sync"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3err"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3response"
)

func (fm *FilesystemManager) GetBucketPolicy(ctx context.Context, bucket string) ([]byte, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetBucketPolicy(ctx, bucket)
}
func (fm *FilesystemManager) GetBucketAcl(ctx context.Context, acl *s3.GetBucketAclInput) ([]byte, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*acl.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetBucketAcl(ctx, acl)
}

func (fm *FilesystemManager) ChangeBucketOwner(ctx context.Context, bucket string, acl []byte) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.ChangeBucketOwner(ctx, bucket, acl)
}

func (fm *FilesystemManager) ListBucketsAndOwners(ctx context.Context) ([]s3response.Bucket, error) {
	var (
		lock      = sync.Mutex{}
		allBucket = []s3response.Bucket{}
	)
	adapters := fm.listAdapters()
	g, ctx := errgroup.WithContext(ctx)
	for index := range adapters {
		index := index
		g.Go(func() error {
			adapter := adapters[index]
			buckets, err := adapter.ListBucketsAndOwners(ctx)
			if err != nil {
				return err
			}
			lock.Lock()
			allBucket = append(allBucket, buckets...)
			lock.Unlock()
			return nil
		})
	}
	return allBucket, g.Wait()
}

func (fm *FilesystemManager) ListBuckets(ctx context.Context, input s3response.ListBucketsInput) (s3response.ListAllMyBucketsResult, error) {
	var (
		lock      = sync.Mutex{}
		allBucket = s3response.ListAllMyBucketsResult{
			Owner: s3response.CanonicalUser{
				ID: input.Owner,
			},
			Prefix: input.Prefix,
		}
	)

	adapters := fm.listAdapters()
	g, ctx := errgroup.WithContext(ctx)
	for index := range adapters {
		index := index
		g.Go(func() error {
			adapter := adapters[index]
			buckets, err := adapter.ListBuckets(ctx, input)
			if err != nil {
				return err
			}
			lock.Lock()
			allBucket.Buckets.Bucket = append(allBucket.Buckets.Bucket, buckets.Buckets.Bucket...)
			lock.Unlock()
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return s3response.ListAllMyBucketsResult{}, err
	}
	if len(allBucket.Buckets.Bucket) == int(input.MaxBuckets) {
		allBucket.ContinuationToken = allBucket.Buckets.Bucket[len(allBucket.Buckets.Bucket)-1].Name
	}
	return allBucket, nil
}

// todo
/*
创建存储桶通过监听etcd中storageset的创建，创建存储桶的接口对外屏蔽
*/
func (fm *FilesystemManager) CreateBucket(ctx context.Context, in *s3.CreateBucketInput, defaultACL []byte) error {
	//return nil
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (fm *FilesystemManager) GetObjectTagging(ctx context.Context, bucket, object string) (map[string]string, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObjectTagging(ctx, bucket, object)
}

func (fm *FilesystemManager) GetObjectRetention(ctx context.Context, bucket, object, versionId string) ([]byte, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObjectRetention(ctx, bucket, object, versionId)
}

func (fm *FilesystemManager) GetObjectLegalHold(ctx context.Context, bucket, object, versionId string) (*bool, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObjectLegalHold(ctx, bucket, object, versionId)
}

func (fm *FilesystemManager) ListParts(ctx context.Context, in *s3.ListPartsInput) (s3response.ListPartsResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.ListPartsResult{}, err
	}
	return adapter.ListParts(ctx, in)
}

func (fm *FilesystemManager) GetObjectAcl(ctx context.Context, in *s3.GetObjectAclInput) (*s3.GetObjectAclOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObjectAcl(ctx, in)
}

func (fm *FilesystemManager) GetObjectAttributes(ctx context.Context, in *s3.GetObjectAttributesInput) (s3response.GetObjectAttributesResponse, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.GetObjectAttributesResponse{}, err
	}
	return adapter.GetObjectAttributes(ctx, in)
}

func (fm *FilesystemManager) GetObject(ctx context.Context, in *s3.GetObjectInput) (*s3.GetObjectOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObject(ctx, in)
}

func (fm *FilesystemManager) GetBucketTagging(ctx context.Context, bucket string) (map[string]string, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetBucketTagging(ctx, bucket)
}

func (fm *FilesystemManager) GetBucketOwnershipControls(ctx context.Context, bucket string) (types.ObjectOwnership, error) {
	var ownship types.ObjectOwnership
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return ownship, err
	}
	return adapter.GetBucketOwnershipControls(ctx, bucket)
}

func (fm *FilesystemManager) GetBucketVersioning(ctx context.Context, bucket string) (s3response.GetBucketVersioningOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return s3response.GetBucketVersioningOutput{}, err
	}
	return adapter.GetBucketVersioning(ctx, bucket)
}

func (fm *FilesystemManager) GetBucketCors(ctx context.Context, bucket string) ([]byte, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetBucketCors(ctx, bucket)
}
func (fm *FilesystemManager) ListObjectVersions(ctx context.Context, in *s3.ListObjectVersionsInput) (s3response.ListVersionsResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.ListVersionsResult{}, err
	}
	return adapter.ListObjectVersions(ctx, in)
}
func (fm *FilesystemManager) GetObjectLockConfiguration(ctx context.Context, bucket string) ([]byte, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return nil, err
	}
	return adapter.GetObjectLockConfiguration(ctx, bucket)
}

func (fm *FilesystemManager) ListMultipartUploads(ctx context.Context, in *s3.ListMultipartUploadsInput) (s3response.ListMultipartUploadsResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.ListMultipartUploadsResult{}, err
	}
	return adapter.ListMultipartUploads(ctx, in)
}

func (fm *FilesystemManager) ListObjectsV2(ctx context.Context, in *s3.ListObjectsV2Input) (s3response.ListObjectsV2Result, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.ListObjectsV2Result{}, err
	}
	return adapter.ListObjectsV2(ctx, in)
}

func (fm *FilesystemManager) ListObjects(ctx context.Context, in *s3.ListObjectsInput) (s3response.ListObjectsResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.ListObjectsResult{}, err
	}
	return adapter.ListObjects(ctx, in)
}
func (fm *FilesystemManager) PutBucketTagging(ctx context.Context, bucket string, tags map[string]string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutBucketTagging(ctx, bucket, tags)
}

func (fm *FilesystemManager) PutBucketOwnershipControls(ctx context.Context, bucket string, ownership types.ObjectOwnership) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutBucketOwnershipControls(ctx, bucket, ownership)
}
func (fm *FilesystemManager) PutBucketVersioning(ctx context.Context, bucket string, status types.BucketVersioningStatus) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutBucketVersioning(ctx, bucket, status)
}

func (fm *FilesystemManager) PutObjectLockConfiguration(ctx context.Context, bucket string, config []byte) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutObjectLockConfiguration(ctx, bucket, config)
}

func (fm *FilesystemManager) PutBucketPolicy(ctx context.Context, bucket string, policy []byte) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutBucketPolicy(ctx, bucket, policy)
}
func (fm *FilesystemManager) PutBucketAcl(ctx context.Context, bucket string, data []byte) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutBucketAcl(ctx, bucket, data)
}

func (fm *FilesystemManager) PutObjectTagging(ctx context.Context, bucket, object string, tags map[string]string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutObjectTagging(ctx, bucket, object, tags)
}

func (fm *FilesystemManager) PutObjectRetention(ctx context.Context, bucket, object, versionId string, bypass bool, retention []byte) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutObjectRetention(ctx, bucket, object, versionId, bypass, retention)
}

func (fm *FilesystemManager) PutObjectLegalHold(ctx context.Context, bucket, object, versionId string, status bool) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.PutObjectLegalHold(ctx, bucket, object, versionId, status)
}

func (fm *FilesystemManager) UploadPartCopy(ctx context.Context, in *s3.UploadPartCopyInput) (s3response.CopyPartResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.CopyPartResult{}, err
	}
	return adapter.UploadPartCopy(ctx, in)
}

func (fm *FilesystemManager) UploadPart(ctx context.Context, in *s3.UploadPartInput) (*s3.UploadPartOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.UploadPart(ctx, in)
}
func (fm *FilesystemManager) PutObjectAcl(ctx context.Context, in *s3.PutObjectAclInput) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return err
	}
	return adapter.PutObjectAcl(ctx, in)
}

func (fm *FilesystemManager) CopyObject(ctx context.Context, in s3response.CopyObjectInput) (s3response.CopyObjectOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.CopyObjectOutput{}, err
	}
	return adapter.CopyObject(ctx, in)
}

func (fm *FilesystemManager) PutObject(ctx context.Context, in s3response.PutObjectInput) (s3response.PutObjectOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.PutObjectOutput{}, err
	}
	return adapter.PutObject(ctx, in)
}

func (fm *FilesystemManager) DeleteBucketTagging(ctx context.Context, bucket string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.DeleteBucketTagging(ctx, bucket)
}

func (fm *FilesystemManager) DeleteBucketOwnershipControls(ctx context.Context, bucket string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.DeleteBucketOwnershipControls(ctx, bucket)
}

func (fm *FilesystemManager) DeleteBucketPolicy(ctx context.Context, bucket string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.DeleteBucketPolicy(ctx, bucket)
}
func (fm *FilesystemManager) DeleteBucketCors(ctx context.Context, bucket string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.DeleteBucketCors(ctx, bucket)
}

// 对外提供的S3屏蔽删除存储桶
func (fm *FilesystemManager) DeleteBucket(ctx context.Context, bucket string) error {
	return s3err.GetAPIError(s3err.ErrNotImplemented)
}

func (fm *FilesystemManager) DeleteObjects(ctx context.Context, in *s3.DeleteObjectsInput) (s3response.DeleteResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.DeleteResult{}, err
	}
	return adapter.DeleteObjects(ctx, in)
}

func (fm *FilesystemManager) DeleteObjectTagging(ctx context.Context, bucket, object string) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(bucket)
	if err != nil {
		return err
	}
	return adapter.DeleteObjectTagging(ctx, bucket, object)
}

func (fm *FilesystemManager) AbortMultipartUpload(ctx context.Context, in *s3.AbortMultipartUploadInput) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return err
	}
	return adapter.AbortMultipartUpload(ctx, in)
}

func (fm *FilesystemManager) DeleteObject(ctx context.Context, in *s3.DeleteObjectInput) (*s3.DeleteObjectOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.DeleteObject(ctx, in)
}
func (fm *FilesystemManager) HeadBucket(ctx context.Context, in *s3.HeadBucketInput) (*s3.HeadBucketOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.HeadBucket(ctx, in)
}

func (fm *FilesystemManager) HeadObject(ctx context.Context, in *s3.HeadObjectInput) (*s3.HeadObjectOutput, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return nil, err
	}
	return adapter.HeadObject(ctx, in)
}

func (fm *FilesystemManager) RestoreObject(ctx context.Context, in *s3.RestoreObjectInput) error {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return err
	}
	return adapter.RestoreObject(ctx, in)
}

func (fm *FilesystemManager) SelectObjectContent(ctx context.Context, input *s3.SelectObjectContentInput) func(w *bufio.Writer) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*input.Bucket)
	if err != nil {
		return nil
	}
	return adapter.SelectObjectContent(ctx, input)
}
func (fm *FilesystemManager) CompleteMultipartUpload(ctx context.Context, in *s3.CompleteMultipartUploadInput) (s3response.CompleteMultipartUploadResult, string, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.CompleteMultipartUploadResult{}, "", err
	}
	return adapter.CompleteMultipartUpload(ctx, in)
}

func (fm *FilesystemManager) CreateMultipartUpload(ctx context.Context, in s3response.CreateMultipartUploadInput) (s3response.InitiateMultipartUploadResult, error) {
	adapter, err := fm.locateFilesystemAdapterByBucket(*in.Bucket)
	if err != nil {
		return s3response.InitiateMultipartUploadResult{}, err
	}
	return adapter.CreateMultipartUpload(ctx, in)
}

func (fm *FilesystemManager) Shutdown() {

}

func (fm *FilesystemManager) StatBucket(ctx context.Context, bucket string) error {
	return nil
}
