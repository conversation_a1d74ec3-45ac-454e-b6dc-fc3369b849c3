package s3server

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path"

	"xiaoshiai.cn/rune/pkg/pai/storage"
)

var cephfsKeyring = `
[client.admin]
    key = %s
    caps mds = "allow *"
    caps mgr = "allow *"
    caps mon = "allow *"
    caps osd = "allow *"
`

func (fs *Filesystem) Format(ctx context.Context) error {
	switch fs.cluster.Provider {
	case storage.StorageClusterProviderCephfs:
		key := fs.cluster.Config["key"]
		if key == "" {
			return fmt.Errorf("cephfs key is empty")
		}
		hosts := fs.cluster.Config["mon-hosts"]
		if hosts == "" {
			return fmt.Errorf("cephfs mon-hosts is empty")
		}
		keyring := path.Join(fs.parentPath, "ceph.client.admin.keyring")
		file, err := os.OpenFile(keyring, os.O_CREATE, 0644)
		if err != nil {
			return err
		}
		defer file.Close()
		_, err = file.WriteString(fmt.Sprintf(cephfsKeyring, key))
		return err
	case storage.StorageClusterProviderJuicefs:
		bucket := fs.cluster.Config["bucket"]
		if bucket == "" {
			return fmt.Errorf("s3 bucket name is empty")
		}
		accessKey := fs.cluster.Config["access-key"]
		if accessKey == "" {
			return fmt.Errorf("s3 access-key is empty")
		}
		secretKey := fs.cluster.Config["secret-key"]
		if secretKey == "" {
			return fmt.Errorf("s3 secret-key is empty")
		}
		meta := fs.cluster.Config["meta"]
		if meta == "" {
			return fmt.Errorf("meta address is empty")
		}
		name := fs.cluster.Config["name"]
		if name == "" {
			return fmt.Errorf("name address is empty")
		}
		format := fs.cluster.MountOption.MountCmd
		args := []string{
			"format",
			"--storage", "s3",
			"--bucket", bucket,
			"--access-key", accessKey,
			"--secret-key", secretKey,
			meta, name,
		}
		command := exec.CommandContext(ctx, format, args...)
		output, err := command.CombinedOutput()
		if err != nil {
			if err.Error() == "wait: no child processes" {
				if command.ProcessState.Success() {
					// We don't consider errNoChildProcesses an error if the process itself succeeded (see - k/k issue #103753).
					return nil
				}
				// Rewrite err with the actual exit error of the process.
				err = &exec.ExitError{ProcessState: command.ProcessState}
			}
			return fmt.Errorf("format failed: %v\nMounting arguments: %s\nOutput: %s",
				err, args, string(output))
		}
		return nil
	case storage.StorageClusterProviderNFS:
		return nil
	default:
		return fmt.Errorf("not support format storage type:%s", fs.cluster.Provider)
	}
}
