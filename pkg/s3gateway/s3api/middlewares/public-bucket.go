// Copyright 2023 Versity Software
// This file is licensed under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

package middlewares

import (
	"io"
	"strings"

	"github.com/gofiber/fiber/v2"
	"xiaoshiai.cn/rune/pkg/s3gateway/metrics"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3api/utils"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3err"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3log"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3server"
)

func AuthorizePublicBucketAccess(be s3server.Backend, l s3log.AuditLogger, mm *metrics.Manager) fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		// skip for auhtneicated requests
		if ctx.Query("X-Amz-Algorithm") != "" || ctx.Get("Authorization") != "" {
			return ctx.Next()
		}
		// 公开存储桶的请求处理逻辑
		bucket, object := parsePath(ctx.Path())

		action, permission, err := detectS3Action(ctx, object == "")
		if err != nil {
			return sendResponse(ctx, err, l, mm)
		}

		err = s3server.VerifyPublicAccess(ctx.Context(), be, action, permission, bucket, object)
		if err != nil {
			return sendResponse(ctx, err, l, mm)
		}

		if utils.IsBigDataAction(ctx) {
			payloadType := ctx.Get("X-Amz-Content-Sha256")
			if utils.IsUnsignedStreamingPayload(payloadType) {
				checksumType, err := utils.ExtractChecksumType(ctx)
				if err != nil {
					return sendResponse(ctx, err, l, mm)
				}

				wrapBodyReader(ctx, func(r io.Reader) io.Reader {
					var cr io.Reader
					cr, err = utils.NewUnsignedChunkReader(r, checksumType)
					return cr
				})
				if err != nil {
					return sendResponse(ctx, err, l, mm)
				}
			}
		}

		utils.ContextKeyPublicBucket.Set(ctx, true)

		return ctx.Next()
	}
}

func detectS3Action(ctx *fiber.Ctx, isBucketAction bool) (s3server.Action, s3server.Permission, error) {
	path := ctx.Path()
	// ListBuckets is not publically available
	if path == "/" {
		//TODO: Still not clear what kind of error should be returned in this case(ListBuckets)
		return "", s3server.PermissionRead, s3err.GetAPIError(s3err.ErrAccessDenied)
	}

	queryArgs := ctx.Context().QueryArgs()

	switch ctx.Method() {
	case fiber.MethodPatch:
		// Admin apis should always be protected
		return "", "", s3err.GetAPIError(s3err.ErrAccessDenied)
	case fiber.MethodHead:
		// HeadBucket
		if isBucketAction {
			return s3server.ListBucketAction, s3server.PermissionRead, nil
		}

		// HeadObject
		return s3server.GetObjectAction, s3server.PermissionRead, nil
	case fiber.MethodGet:
		if isBucketAction {
			if queryArgs.Has("tagging") {
				// GetBucketTagging
				return s3server.GetBucketTaggingAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("ownershipControls") {
				// GetBucketOwnershipControls
				return s3server.GetBucketOwnershipControlsAction, s3server.PermissionRead, s3err.GetAPIError(s3err.ErrAnonymousGetBucketOwnership)
			} else if queryArgs.Has("versioning") {
				// GetBucketVersioning
				return s3server.GetBucketVersioningAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("policy") {
				// GetBucketPolicy
				return s3server.GetBucketPolicyAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("cors") {
				// GetBucketCors
				return s3server.GetBucketCorsAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("versions") {
				// ListObjectVersions
				return s3server.ListBucketVersionsAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("object-lock") {
				// GetObjectLockConfiguration
				return s3server.GetBucketObjectLockConfigurationAction, s3server.PermissionReadAcp, nil
			} else if queryArgs.Has("acl") {
				// GetBucketAcl
				return s3server.GetBucketAclAction, s3server.PermissionRead, nil
			} else if queryArgs.Has("uploads") {
				// ListMultipartUploads
				return s3server.ListBucketMultipartUploadsAction, s3server.PermissionRead, nil
			} else if queryArgs.GetUintOrZero("list-type") == 2 {
				// ListObjectsV2
				return s3server.ListBucketAction, s3server.PermissionRead, nil
			}
			// All the other requests are considerd as ListObjects in the router
			// no matter what kind of query arguments are provided apart from the ones above

			return s3server.ListBucketAction, s3server.PermissionRead, nil
		}

		if queryArgs.Has("tagging") {
			// GetObjectTagging
			return s3server.GetObjectTaggingAction, s3server.PermissionRead, nil
		} else if queryArgs.Has("retention") {
			// GetObjectRetention
			return s3server.GetObjectRetentionAction, s3server.PermissionRead, nil
		} else if queryArgs.Has("legal-hold") {
			// GetObjectLegalHold
			return s3server.GetObjectLegalHoldAction, s3server.PermissionReadAcp, nil
		} else if queryArgs.Has("acl") {
			// GetObjectAcl
			return s3server.GetObjectAclAction, s3server.PermissionRead, nil
		} else if queryArgs.Has("attributes") {
			// GetObjectAttributes
			return s3server.GetObjectAttributesAction, s3server.PermissionRead, nil
		} else if queryArgs.Has("uploadId") {
			// ListParts
			return s3server.ListMultipartUploadPartsAction, s3server.PermissionRead, nil
		}

		// All the other requests are considerd as GetObject in the router
		// no matter what kind of query arguments are provided apart from the ones above
		if queryArgs.Has("versionId") {
			return s3server.GetObjectVersionAction, s3server.PermissionRead, nil
		}
		return s3server.GetObjectAction, s3server.PermissionRead, nil
	case fiber.MethodPut:
		if isBucketAction {
			if queryArgs.Has("tagging") {
				// PutBucketTagging
				return s3server.PutBucketTaggingAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("ownershipControls") {
				// PutBucketOwnershipControls
				return s3server.PutBucketOwnershipControlsAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAnonymousPutBucketOwnership)
			}
			if queryArgs.Has("versioning") {
				// PutBucketVersioning
				return s3server.PutBucketVersioningAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("object-lock") {
				// PutObjectLockConfiguration
				return s3server.PutBucketObjectLockConfigurationAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("cors") {
				// PutBucketCors
				return s3server.PutBucketCorsAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("policy") {
				// PutBucketPolicy
				return s3server.PutBucketPolicyAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("acl") {
				// PutBucketAcl
				return s3server.PutBucketAclAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAnonymousRequest)
			}

			// All the other rquestes are considered as 'CreateBucket' in the router
			return "", "", s3err.GetAPIError(s3err.ErrAnonymousRequest)
		}

		if queryArgs.Has("tagging") {
			// PutObjectTagging
			return s3server.PutObjectTaggingAction, s3server.PermissionWrite, nil
		}
		if queryArgs.Has("retention") {
			// PutObjectRetention
			return s3server.PutObjectRetentionAction, s3server.PermissionWrite, nil
		}
		if queryArgs.Has("legal-hold") {
			// PutObjectLegalHold
			return s3server.PutObjectLegalHoldAction, s3server.PermissionWrite, nil
		}
		if queryArgs.Has("acl") {
			// PutObjectAcl
			return s3server.PutObjectAclAction, s3server.PermissionWriteAcp, s3err.GetAPIError(s3err.ErrAnonymousRequest)
		}
		if queryArgs.Has("uploadId") && queryArgs.Has("partNumber") {
			if ctx.Get("X-Amz-Copy-Source") != "" {
				// UploadPartCopy
				//TODO: Add public access check for copy-source
				// Return AccessDenied for now
				return s3server.PutObjectAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAccessDenied)
			}

			utils.ContextKeyBodyReader.Set(ctx, ctx.Request().BodyStream())
			// UploadPart
			return s3server.PutObjectAction, s3server.PermissionWrite, nil
		}
		if ctx.Get("X-Amz-Copy-Source") != "" {
			return s3server.PutObjectAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAnonymousCopyObject)
		}

		utils.ContextKeyBodyReader.Set(ctx, ctx.Request().BodyStream())
		// All the other requests are considered as 'PutObject' in the router
		return s3server.PutObjectAction, s3server.PermissionWrite, nil
	case fiber.MethodPost:
		if isBucketAction {
			// DeleteObjects
			// Return AccessDenied for now
			return s3server.DeleteObjectAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAccessDenied)
		}

		if queryArgs.Has("restore") {
			return s3server.RestoreObjectAction, s3server.PermissionWrite, nil
		}
		if queryArgs.Has("select") && ctx.Query("select-type") == "2" {
			// SelectObjectContent
			return s3server.GetObjectAction, s3server.PermissionRead, s3err.GetAPIError(s3err.ErrAnonymousRequest)
		}
		if queryArgs.Has("uploadId") {
			// CompleteMultipartUpload
			return s3server.PutObjectAction, s3server.PermissionWrite, nil
		}

		// All the other requests are considered as 'CreateMultipartUpload' in the router
		return "", "", s3err.GetAPIError(s3err.ErrAnonymousCreateMp)
	case fiber.MethodDelete:
		if isBucketAction {
			if queryArgs.Has("tagging") {
				// DeleteBucketTagging
				return s3server.PutBucketTaggingAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("ownershipControls") {
				// DeleteBucketOwnershipControls
				return s3server.PutBucketOwnershipControlsAction, s3server.PermissionWrite, s3err.GetAPIError(s3err.ErrAnonymousPutBucketOwnership)
			}
			if queryArgs.Has("policy") {
				// DeleteBucketPolicy
				return s3server.PutBucketPolicyAction, s3server.PermissionWrite, nil
			}
			if queryArgs.Has("cors") {
				// DeleteBucketCors
				return s3server.PutBucketCorsAction, s3server.PermissionWrite, nil
			}

			// All the other requests are considered as 'DeleteBucket' in the router
			return s3server.DeleteBucketAction, s3server.PermissionWrite, nil
		}

		if queryArgs.Has("tagging") {
			// DeleteObjectTagging
			return s3server.PutObjectTaggingAction, s3server.PermissionWrite, nil
		}
		if queryArgs.Has("uploadId") {
			// AbortMultipartUpload
			return s3server.AbortMultipartUploadAction, s3server.PermissionWrite, nil
		}
		// All the other requests are considered as 'DeleteObject' in the router
		return s3server.DeleteObjectAction, s3server.PermissionWrite, nil
	default:
		// In no action is detected, return AccessDenied ?
		return "", "", s3err.GetAPIError(s3err.ErrAccessDenied)
	}
}

// parsePath extracts the bucket and object names from the path
func parsePath(path string) (string, string) {
	p := strings.TrimPrefix(path, "/")
	bucket, object, _ := strings.Cut(p, "/")

	return bucket, object
}
