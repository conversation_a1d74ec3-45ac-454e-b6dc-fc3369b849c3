s3gateway是中心侧的服务
相当于监听storageset->拿到storagecluster的信息
通过用户的ak/sk路由到不同的存储的


tenant+storage作为桶名称，能够做到全局唯一

storage-cluster  key/value结构

/storage-cluster/test-cephfs
{
    "fs_type":"cephfs",
    "option":{
        "source":"192.168.0.49:6789:/",
        "options":["name=admin", "secretfile=/etc/ceph/ceph.client.admin.keyring"]
    },
    "name":"test-cephfs",
    "mount_path":"/s3gateway/test-cephfs"
}

/storage-cluster/test-local
{
    "fs_type":"local",
    "name":"test-local",
    "mount_path":"/s3gateway/test-local"
}