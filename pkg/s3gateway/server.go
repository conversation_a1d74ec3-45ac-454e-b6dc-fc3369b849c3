package s3gateway

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"net"
	"os"
	"strings"

	"github.com/gofiber/fiber/v2"
	"golang.org/x/sync/errgroup"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"xiaoshiai.cn/common/pprof"
	v1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/s3gateway/metrics"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3api"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3api/middlewares"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3event"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3log"
	"xiaoshiai.cn/rune/pkg/s3gateway/s3server"
)

type Options struct {
	Listen             string
	StorageClusterName string
	RootAccessKey      string
	RootSecretKey      string
	Debug              bool
}

func NewDefaultOptions() *Options {
	return &Options{
		Listen:        ":8080",
		RootAccessKey: "rune-user",
		RootSecretKey: "rune-password",
	}
}

func Run(ctx context.Context, opts *Options) error {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return runS3Gateway(ctx, opts)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

func runS3Gateway(ctx context.Context, options *Options) error {
	scheme := runtime.NewScheme()
	utilruntime.Must(v1.AddToScheme(scheme))
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme: scheme,
	})
	if err != nil {
		return err
	}
	// use iamnone
	root := s3server.Account{
		Access: options.RootAccessKey,
		Secret: options.RootSecretKey,
		Role:   s3server.RoleAdmin,
	}
	cache, err := s3server.NewStorageVolumeCache(ctx, mgr.GetClient(), root, options.StorageClusterName)
	if err != nil {
		return fmt.Errorf("failed to create StorageVolumeCache: %w", err)
	}
	if err := ctrl.NewControllerManagedBy(mgr).
		For(&v1.StorageVolume{}).
		Complete(cache); err != nil {
		return fmt.Errorf("failed to start StorageVolume Controller: %w", err)
	}
	managerCtx, cancelManager := context.WithCancel(ctx)
	defer cancelManager()
	go func() {
		if err := mgr.Start(managerCtx); err != nil && !errors.Is(err, context.Canceled) {
			fmt.Fprintf(os.Stderr, "manager failed: %v\n", err)
			os.Exit(1)
		}
	}()

	posix := s3server.NewPosix(cache, s3server.PosixOpts{
		NewDirPerm: fs.FileMode(0755),
	})
	app := fiber.New(fiber.Config{
		AppName:               "s3gateway",
		ServerHeader:          "RUNE",
		StreamRequestBody:     true,
		DisableKeepalive:      true,
		Network:               fiber.NetworkTCP,
		DisableStartupMessage: true,
	})
	var opts []s3api.Option
	if options.Debug {
		opts = append(opts, s3api.WithDebug())
	}
	loggers, err := s3log.InitLogger(&s3log.LogConfig{})
	if err != nil {
		return fmt.Errorf("setup logger: %w", err)
	}
	metricsManager, err := metrics.NewManager(ctx, metrics.Config{})
	if err != nil {
		return fmt.Errorf("init metrics manager: %w", err)
	}
	evSender, err := s3event.InitEventSender(&s3event.EventConfig{})
	if err != nil {
		return fmt.Errorf("init bucket event notifications: %w", err)
	}
	srv, err := s3api.New(app, posix, middlewares.RootUserConfig{
		Access: options.RootAccessKey,
		Secret: options.RootSecretKey,
	}, options.Listen, "us-east-1", loggers.S3Logger, evSender, metricsManager, opts...)
	if err != nil {
		return fmt.Errorf("init gateway: %v", err)
	}

	printBanner(options.Listen, "", false, false)
	c := make(chan error, 1)
	go func() {
		c <- srv.Serve()
	}()
Loop:
	for {
		select {
		case err = <-c:
			break Loop
		case <-ctx.Done():
			if loggers.S3Logger != nil {
				err = loggers.S3Logger.HangUp()
				if err != nil {
					err = fmt.Errorf("HUP s3 logger: %w", err)
					break Loop
				}
			}
		}
	}
	saveErr := err

	posix.Shutdown()
	if saveErr != nil {
		fmt.Fprintf(os.Stderr, "shutdown posix: %v\n", saveErr)
	}

	if loggers.S3Logger != nil {
		err := loggers.S3Logger.Shutdown()
		if err != nil {
			if saveErr == nil {
				saveErr = err
			}
			fmt.Fprintf(os.Stderr, "shutdown s3 logger: %v\n", err)
		}
	}
	if evSender != nil {
		err := evSender.Close()
		if err != nil {
			if saveErr == nil {
				saveErr = err
			}
			fmt.Fprintf(os.Stderr, "close event sender: %v\n", err)
		}
	}
	if metricsManager != nil {
		metricsManager.Close()
	}
	return saveErr
}

func printBanner(port, admPort string, ssl, admSsl bool) {
	interfaces, err := getMatchingIPs(port)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to match local IP addresses: %v\n", err)
		return
	}

	var admInterfaces []string
	if admPort != "" {
		admInterfaces, err = getMatchingIPs(admPort)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to match admin port local IP addresses: %v\n", err)
			return
		}
	}

	title := "s3gateway"
	version := fmt.Sprintf("Version:v0.0.1, Build:standby...")
	urls := []string{}

	hst, prt, err := net.SplitHostPort(port)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to parse port: %v\n", err)
		return
	}

	for _, ip := range interfaces {
		url := fmt.Sprintf("http://%s:%s", ip, prt)
		if ssl {
			url = fmt.Sprintf("https://%s:%s", ip, prt)
		}
		urls = append(urls, url)
	}

	if hst == "" {
		hst = "0.0.0.0"
	}

	boundHost := fmt.Sprintf("(bound on host %s and port %s)", hst, prt)

	lines := []string{
		centerText(title),
		centerText(version),
		centerText(boundHost),
		centerText(""),
	}

	if len(admInterfaces) > 0 {
		lines = append(lines,
			leftText("S3 service listening on:"),
		)
	} else {
		lines = append(lines,
			leftText("Admin/S3 service listening on:"),
		)
	}

	for _, url := range urls {
		lines = append(lines, leftText("  "+url))
	}

	if len(admInterfaces) > 0 {
		lines = append(lines,
			centerText(""),
			leftText("Admin service listening on:"),
		)

		_, prt, err := net.SplitHostPort(admPort)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to parse port: %v\n", err)
			return
		}

		for _, ip := range admInterfaces {
			url := fmt.Sprintf("http://%s:%s", ip, prt)
			if admSsl {
				url = fmt.Sprintf("https://%s:%s", ip, prt)
			}
			lines = append(lines, leftText("  "+url))
		}
	}

	// Print the top border
	fmt.Println("┌" + strings.Repeat("─", columnWidth-2) + "┐")

	// Print each line
	for _, line := range lines {
		fmt.Printf("│%-*s│\n", columnWidth-2, line)
	}

	// Print the bottom border
	fmt.Println("└" + strings.Repeat("─", columnWidth-2) + "┘")
}
func getMatchingIPs(spec string) ([]string, error) {
	// Split the input spec into IP and port
	host, _, err := net.SplitHostPort(spec)
	if err != nil {
		return nil, fmt.Errorf("parse address/port: %v", err)
	}

	// Handle cases where IP is omitted (e.g., ":1234")
	if host == "" {
		host = "0.0.0.0"
	}

	ipaddr, err := net.ResolveIPAddr("ip", host)
	if err != nil {
		return nil, err
	}

	parsedInputIP := ipaddr.IP

	var result []string

	// Get all network interfaces
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}

	for _, iface := range interfaces {
		// Get all addresses associated with the interface
		addrs, err := iface.Addrs()
		if err != nil {
			return nil, err
		}

		for _, addr := range addrs {
			// Parse the address to get the IP part
			ipAddr, _, err := net.ParseCIDR(addr.String())
			if err != nil {
				return nil, err
			}

			if ipAddr.IsLinkLocalUnicast() {
				continue
			}
			if ipAddr.IsInterfaceLocalMulticast() {
				continue
			}
			if ipAddr.IsLinkLocalMulticast() {
				continue
			}

			// Check if the IP matches the input specification
			if parsedInputIP.Equal(net.IPv4(0, 0, 0, 0)) || parsedInputIP.Equal(ipAddr) {
				result = append(result, ipAddr.String())
			}
		}
	}

	return result, nil
}

const columnWidth = 70

func centerText(text string) string {
	padding := (columnWidth - 2 - len(text)) / 2
	if padding < 0 {
		padding = 0
	}
	return strings.Repeat(" ", padding) + text
}

func leftText(text string) string {
	if len(text) > columnWidth-2 {
		return text
	}
	return text + strings.Repeat(" ", columnWidth-2-len(text))
}
