NFS:
    mkdir -p /s3gateway/nfs
    rpcbind -f &
    mount -t "nfs4" -o "nfsvers=4" 192.168.0.49:/nfs-export /s3gateway/nfs



同一种文件系统有多个挂载点的时候，这里就以cephfs为例,分别是cephfs1和cephfs2
mkdir -p /s3gateway/cephfs/cephfs1/mountpoint
touch /s3gateway/cephfs/cephfs1/ceph.client.admin.keyring
[client.admin]
    key = xxxxxxxxxxxxxxxxx
    caps mds = "allow *"
    caps mgr = "allow *"
    caps mon = "allow *"
    caps osd = "allow *"

这样的情况下，默认挂载根目录
ceph-fuse -n client.admin -m 192.168.0.47:6789,192.168.0.48:6789,192.168.0.49:6789  -k ceph.client.admin.keyring /s3gateway/cephfs/cephfs1/mountpoint

挂载子目录
ceph-fuse -n client.admin -m 192.168.0.47:6789,192.168.0.48:6789,192.168.0.49:6789  -k keyring /s3gateway/cephfs/cephfs1/mountpoint -r /test-mount

