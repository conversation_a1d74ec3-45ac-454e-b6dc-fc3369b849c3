package bootstrap

import (
	"context"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/iam/authz/rbac"
)

func InitStorage(ctx context.Context, store store.Store, mongo *mongo.MongoStorage) error {
	if err := initUserRoles(ctx, store, []string{"admin"}); err != nil {
		return err
	}
	return nil
}

func initUserRoles(ctx context.Context, s store.Store, users []string) error {
	for _, user := range users {
		userRole := &rbac.UserRole{
			ObjectMeta: store.ObjectMeta{Name: user},
			Roles:      []string{rbac.RoleAdmin},
		}
		if err := store.CreateIfNotExists(ctx, s, userRole); err != nil {
			return err
		}
	}
	return nil
}
