package authn

import (
	"context"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

type AuthenticationOptions struct {
	Anonymous        bool             `json:"anonymous,omitempty"`
	TokenExpiration  time.Duration    `json:"tokenExpiration,omitempty"`
	RememberDuration time.Duration    `json:"rememberDuration,omitempty"`
	RefreshThreshold time.Duration    `json:"refreshThreshold,omitempty"`
	OIDC             *api.OIDCOptions `json:"oidc,omitempty"`
	CacheDuration    time.Duration    `json:"cacheDuration,omitempty" description:"cache duration for an valid token"`
	CacheSize        int              `json:"cacheSize,omitempty" description:"cache size for an valid token"`
	Session          *SessionOptions  `json:"session,omitempty" description:"options for session cookie, default is empty"`
}

type SessionOptions struct {
	Domain     string `json:"domain,omitempty" description:"domain for session cookie, default is empty"`
	Insecure   bool   `json:"insecure,omitempty" description:"whether to set secure flag for session cookie, default is false"`
	CookieName string `json:"cookieName,omitempty" description:"name of the session cookie'"`
}

func DefaultAuthenticationOptions() *AuthenticationOptions {
	return &AuthenticationOptions{
		Anonymous:       false,
		OIDC:            api.NewDefaultOIDCOptions(),
		TokenExpiration: 7 * 24 * time.Hour,
		Session: &SessionOptions{
			CookieName: "user_session",
			Insecure: true, // set to true for development, should be false in production
		},
	}
}

func BuildAuthn(ctx context.Context, storage store.Store,
	options *AuthenticationOptions,
	tokenautn []api.TokenAuthenticator,
	basic []api.BasicAuthenticator,
) (api.TokenAuthenticator, api.BasicAuthenticator, error) {
	tokenAuthnChain := api.TokenAuthenticatorChain{}
	tokenAuthnChain = append(tokenAuthnChain, tokenautn...)
	if options.OIDC.Issuer != "" {
		// https://kubernetes.io/docs/reference/access-authn-authz/authentication/#openid-connect-tokens
		// same with k8s, use id_token(not access_token) as bearer token
		// we only need to know who the user is, not what the user can do
		oidcauthenticator, err := api.NewOIDCAuthenticator(ctx, options.OIDC)
		if err != nil {
			return nil, nil, err
		}
		tokenAuthnChain = append(tokenAuthnChain, oidcauthenticator)
	}
	tokenauthn := api.TokenAuthenticator(tokenAuthnChain)
	// if cahce enabled, cache token
	if options.CacheDuration > 0 {
		cachesize := options.CacheSize
		if cachesize <= 0 {
			cachesize = 64
		}
		tokenauthn = api.NewCacheTokenAuthenticator(tokenauthn, cachesize, options.CacheDuration)
	}
	basicauthn := api.BasicAuthenticatorChain(basic)
	// authnlist := api.DelegateAuthenticator{
	// 	api.BasicAuthenticatorWrap(basicauthn),
	// 	api.SessionAuthenticatorWrap(tokenauthn, SessionCookieKey),
	// 	api.BearerTokenAuthenticatorWrap(tokenauthn),
	// }
	// if options.Anonymous {
	// 	log.FromContext(ctx).Info("anonymous authentication enabled")
	// 	// anonymous cames at last in the chain, so that it users use token can be authenticated
	// 	authnlist = append(authnlist, api.NewAnonymousAuthenticator())
	// }
	return tokenauthn, basicauthn, nil
}
