// Copyright (C) 2023 The Kubegems Authors
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU Affero General Public License as published
// by the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU Affero General Public License for more details.
//
// You should have received a copy of the GNU Affero General Public License
// along with this program.  If not, see <https://www.gnu.org/licenses/>.

package pai

import (
	"golang.org/x/exp/maps"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common"
)

// GroupName is the group name used in this package
const GroupName = "pai." + common.GroupPrefix

const (
	LabelJobName  = GroupName + "/job-name"
	LabelRoleName = GroupName + "/role-name"
)

const (
	// 人为手段预先设置的cpu model name，再自动获取不到cpu型号的情况下，使用这个值
	LabelFeatureCPUPreModelName = "feature.node.pai.kubegems.io/cpu-model.predefined-name"

	LabelFeatureCPUModelName         = "feature.node.pai.kubegems.io/cpu-model.name"
	AnnotationFeatureCPUModelNameRaw = "feature.node.pai.kubegems.io/cpu-model.raw-name"

	LabelFeatureCacheMountStatus = "feature.node.pai.kubegems.io/cache-mount.status"
	LabelFeatureCacheMountSize   = "feature.node.pai.kubegems.io/cache-mount.size"

	LabelStorageSetKind          = "pai.kubegems.io/storageset-kind"
	LabelStorageSetKindDataSet   = "dataset"
	LabelStorageSetKindModelSet  = "modelset"
	LabelStorageSetKindOutput    = "output"
	LabelStorageSetKindWorkspace = "workspace"
	LabelStorageSetKindCodeSet   = "codeset"
	LabelStorageSetKindConda     = "conda"
	LableStorageCluster          = "pai.kubegems.io/storage-cluster"
	LabelStorageSetProvider      = "pai.kubegems.io/storageset-provider"
	LabelTenant                  = "pai.kubegems.io/tenant"
	LabelStorageSetsource        = "pai.kubegems.io/storageset-source"

	LabelGeneration      = "pai.kubegems.io/generation"
	LabelShared          = "pai.kubegems.io/shared"
	LabelStorageReadOnly = "pai.kubegems.io/storageset-readonly"
	LabelNodeRole        = "pai.kubegems.io/node-role"
	LabelJobKind         = "pai.kubegems.io/job-kind"
	LabelMountSource     = "pai.kubegems.io/mount-source"
	LabelEnabled         = "pai.kubegems.io/enabled"

	LabelIsLabeling = "pai.kubegems.io/labeling"

	LabelIsSystem = "pai.kubegems.io/is-system"

	// see [DatasetType]
	LabelDatasetType = "pai.kubegems.io/dataset-type"

	// AnnotationDatasetInfo is used to store dataset-info.json
	AnnotationDatasetInfo = "pai.kubegems.io/dataset-info-json"

	// 设计了四个节点角色，分别是compute, storage, manager, app, 方便为后续更细粒度的资源调度做准备
	// 1. compute节点角色，用于运行计算任务，如训练任务，推理任务等
	// 2. storage节点角色，用于存储数据，如数据集，模型集等
	// 3. manager节点角色，用于管理集群，如kubelet，kube-proxy等
	// 4. app节点角色，用于运行应用，推理服务等
	LabelNodeRoleCompute = "pai.kubegems.io/node-role.compute"
	LabelNodeRoleStorage = "pai.kubegems.io/node-role.storage"
	LabelNodeRoleManager = "pai.kubegems.io/node-role.manager"
	LabelNodeRoleApp     = "pai.kubegems.io/node-role.app"

	AnnotationCreator                = "pai.kubegems.io/creator"
	AnnotationSKUName                = "pai.kubegems.io/sku-name" // sku name
	AnnotationDescription            = "pai.kubegems.io/description"
	AnnotationDescriptionMarkdownURL = "pai.kubegems.io/description-markdown-url"
	AnnotationDescriptionMarkdown    = `pai.kubegems.io/description-markdown`
	AnnotationIcon                   = `pai.kubegems.io/icon`
	AnnotationOriginalName           = "pai.kubegems.io/original-name"
	AnnotationUpdateTime             = "pai.kubegems.io/update-time" // last update time
	AnnotationProductName            = "pai.kubegems.io/product-name"
	AnnotationSkuConfig              = "pai.kubegems.io/sku-config"
	AnnotationIngressClassName       = "kubernetes.io/ingress.class"

	// AnnotationCreateOutPut is create a output storage set for the job
	AnnotationCreateOutPut = "pai.kubegems.io/create-output"

	// LabelDisplayName is used to display the name of the resource
	// and it can be changed by user
	AnnotationDisplayName = "pai.kubegems.io/display-name"

	AnnotationNetworkAttachmentName = "k8s.v1.cni.cncf.io/networks"

	// is virtual tenant which is not create related resources
	AnnotationTenanIsVirtual = "tenant.pai.kubegems.io/is-virtual"

	// image auth annotation is used to store image auth info
	// the value is a base64 encoded basic auth string
	// eg.  echo -n "username:password" | base64
	AnnotationImageAuth = "pai.kubegems.io/image-auth"

	JobKindModeling  = "modeling" // modeling
	JobKindTraining  = "training" // job
	JobKindInference = "inference"
	JobsKindLLM      = "llm"      // large language model
	JobsKindLabeling = "labeling" // labeling

	LabelStorageSetWorkspaceCatalog              = "pai.kubegems.io/workspace-catalog"
	LabelStorageSetWorkspaceCatalogCondaTemplate = "condatemplate"

	AnnotationJobTensorboardLogdir = "pai.kubegems.io/job-tensorboard-logdir"

	LabelJobTemplateName        = "pai.kubegems.io/job-template-name"
	AnnotationJobTemplateValues = "pai.kubegems.io/job-template-values"
)

type DatasetType string

const (
	DatasetTypeText                   DatasetType = "text"                      // 纯文本
	DatasetTypePrompt                 DatasetType = "prompt"                    // Prompt
	DatasetTypePromptResponse         DatasetType = "prompt_response"           // Prompt + Response
	DatasetTypeUserAssistant          DatasetType = "user_assistant"            // User Assistant
	DatasetTypePromptMultiResponse    DatasetType = "prompt_multi_response"     // Prompt + Multi Response
	DatasetTypePromptChoiceRejected   DatasetType = "prompt_choice_rejected"    // Prompt + Choice + Rejected
	DatasetTypePromptChoiceOrRejected DatasetType = "prompt_choice_or_rejected" // Prompt + Choice/Rejected
)

func MergeLabelsAnnotations(obj client.Object, labels, annotations map[string]string) {
	obj.SetLabels(MergeMap(obj.GetLabels(), labels))
	obj.SetAnnotations(MergeMap(obj.GetAnnotations(), annotations))
}

func MergeMap[M ~map[K]V, K comparable, V any](dest, src M) M {
	if len(src) == 0 {
		return dest
	}
	if dest == nil {
		dest = make(M)
	}
	maps.Copy(dest, src)
	return dest
}

func InjectArray[T any](dest *[]T, v T, keyFunc func(T) string) {
	for _, d := range *dest {
		if keyFunc(d) == keyFunc(v) {
			return
		}
	}
	*dest = append(*dest, v)
}

func MergeArray[T any](dest, src *[]T, keyFunc func(T) string) {
	for _, v := range *src {
		InjectArray(dest, v, keyFunc)
	}
}
