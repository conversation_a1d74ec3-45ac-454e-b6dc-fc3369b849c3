package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
type StorageCluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              StorageClusterSpec   `json:"spec,omitempty"`
	Status            StorageClusterStatus `json:"status,omitempty"`
}
type StorageClusterSpec struct {
	//Provisioner StorageClusterProvisioner `json:"provisioner"`
	Provider   StorageClusterProvider `json:"provider,omitempty"`
	Values     runtime.RawExtension   `json:"values,omitempty"`
	Repository string                 `json:"repository,omitempty"`
	Chart      string                 `json:"chart,omitempty"`
	Version    string                 `json:"version,omitempty"`
}

type StorageClusterStatus struct {
	Phase                 StorageClusterPhase    `json:"phase,omitempty"`
	StorageClassReference corev1.ObjectReference `json:"clusterReference,omitempty"`
	// Message is the status message of the storage cluster
	Message string `json:"message,omitempty"`
	// +optional
	// +listType=atomic
	Objects []corev1.ObjectReference `json:"objects,omitempty"`
}

type StorageClusterPhase string

const (
	StorageClusterPhaseUnknown StorageClusterPhase = "Unknown"
	StorageClusterPhaseSuccess StorageClusterPhase = "Success"
	StorageClusterPhaseFailed  StorageClusterPhase = "Failed"
)

type StorageClusterProvider string

const (
	StorageClusterProviderNFS     StorageClusterProvider = "NFS"
	StorageClusterProviderCephFS  StorageClusterProvider = "CephFS"
	StorageClusterProviderJuiceFS StorageClusterProvider = "JuiceFS"
)

// type StorageClusterProvisioner string

// const (
// 	StorageClusterProvisionerNFS     StorageClusterProvisioner = "nfs.csi.k8s.io"
// 	StorageClusterProvisionerCephfs  StorageClusterProvisioner = "cephfs.csi.ceph.com"
// 	StorageClusterProvisionerJuicefs StorageClusterProvisioner = "csi.juicefs.com"
// )

// +kubebuilder:object:root=true
type StorageClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []StorageCluster `json:"items"`
}
