package v1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +kubebuilder:object:root=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:subresource:status
type StorageVolume struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              StorageVolumeSpec   `json:"spec,omitempty"`
	Status            StorageVolumeStatus `json:"status,omitempty"`
}

type StorageVolumeSpec struct {
	// ClusterReference is the reference to the storage cluster
	ClusterReference corev1.ObjectReference `json:"clusterReference,omitempty"`
	// Capability is the storage capacity of the volume
	Capability resource.Quantity `json:"capability,omitempty"`
	// Accounts is the list of accounts that can access the volume
	Accounts []S3Account `json:"accounts,omitempty"`
}

type StorageVolumeStatus struct {
	StoragePath string `json:"storagePath,omitempty"`

	// Phase represents the current phase of the storage volume
	// +optional
	Phase StorageVolumePhase `json:"phase,omitempty"`
	// Message provides additional information about the current state
	// +optional
	Message string `json:"message,omitempty"`

	// PVCName is the name of the associated PersistentVolumeClaim
	// +optional
	PVCName string `json:"pvcName,omitempty"`

	// PVName is the name of the bound PersistentVolume
	// +optional
	PVName string `json:"pvName,omitempty"`

	// Conditions represent the latest available observations of the storage volume's state
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`
}

// StorageVolumePhase represents the current phase of a StorageVolume
type StorageVolumePhase string

const (
	// StorageVolumePhasePending means the PVC is created but not yet bound to a PV
	StorageVolumePhasePending StorageVolumePhase = "Pending"

	// StorageVolumePhaseBound means the PVC is successfully bound to a PV
	StorageVolumePhaseBound StorageVolumePhase = "Bound"

	// StorageVolumePhaseLost means the PVC is lost
	StorageVolumePhaseLost StorageVolumePhase = "Lost"

	// StorageVolumePhaseUnknown means the PVC is in an unknown state
	StorageVolumePhaseUnknown StorageVolumePhase = "Unknown"
)

type S3Account struct {
	AccessKey string        `json:"accessKey,omitempty"`
	SecretKey string        `json:"secretKey,omitempty"`
	Role      S3AccountRole `json:"role,omitempty"`
}

type S3AccountRole string

const (
	S3AccountRoleRW S3AccountRole = "rw"
	S3AccountRoleRO S3AccountRole = "ro"
)

// +kubebuilder:object:root=true
type StorageVolumeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []StorageVolume `json:"items"`
}
