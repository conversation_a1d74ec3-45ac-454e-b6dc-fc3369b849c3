package v1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +kubebuilder:object:root=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:subresource:status
type StorageVolume struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              StorageVolumeSpec   `json:"spec,omitempty"`
	Status            StorageVolumeStatus `json:"status,omitempty"`
}

type StorageVolumeSpec struct {
	ClusterReference corev1.ObjectReference `json:"clusterReference,omitempty"`
	Capability       resource.Quantity      `json:"capability,omitempty"`
}

type StorageVolumeStatus struct {
	StoragePath string      `json:"storagePath,omitempty"`
	Accounts    []S3Account `json:"accounts,omitempty"`
}

type S3Account struct {
	AccessKey string        `json:"accessKey,omitempty"`
	SecretKey string        `json:"secretKey,omitempty"`
	Role      S3AccountRole `json:"role,omitempty"`
}

type S3AccountRole string

const (
	S3AccountRoleRW S3AccountRole = "rw"
	S3AccountRoleRO S3AccountRole = "ro"
)

// +kubebuilder:object:root=true
type StorageVolumeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []StorageVolume `json:"items"`
}
