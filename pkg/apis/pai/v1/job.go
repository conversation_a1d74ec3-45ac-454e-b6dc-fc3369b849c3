package v1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +kubebuilder:object:root=true
// +kubebuilder:resource:scope=Namespaced
// +kubebuilder:subresource:status
// +kubebuilder:subresource:scale:specpath=.spec.replicas,statuspath=.status.replicas,selectorpath=.status.selector
// +kubebuilder:printcolumn:name="Phase",type="string",JSONPath=".status.state.phase",description="Job phase"
// +kubebuilder:printcolumn:name="Replicas",type="integer",JSONPath=".status.replicas",description="Job replicas"
type Job struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   JobSpec   `json:"spec,omitempty"`
	Status JobStatus `json:"status,omitempty"`
}

type JobSpec struct {
	// replicas is the number of desired replicas of the last role if it is specified.
	// the design proposal is to support HPA on the last role.
	Replicas     *int32           `json:"replicas,omitempty"`
	Suspend      bool             `json:"suspend,omitempty"`
	Paused       bool             `json:"paused,omitempty"`
	TTL          *metav1.Duration `json:"ttl,omitempty"` // max time to live
	PriorityName string           `json:"priorityName,omitempty"`
	QueueName    string           `json:"queueName,omitempty"`
	Roles        []JobRole        `json:"roles,omitempty"`
	HistoryLimit int              `json:"historyLimit,omitempty"`
	Extensions   []JobExtension   `json:"extensions,omitempty"`
}

type JobExtension struct {
	Kind   string            `json:"kind,omitempty"`
	Config map[string]string `json:"config,omitempty"`
}

type JobRole struct {
	Name             string                        `json:"name,omitempty"`
	Annotations      map[string]string             `json:"annotations,omitempty"`
	Labels           map[string]string             `json:"labels,omitempty"`
	Image            string                        `json:"image,omitempty"`
	ImagePullPolicy  corev1.PullPolicy             `json:"imagePullPolicy,omitempty"`
	ImagePullSecrets []corev1.LocalObjectReference `json:"imagePullSecrets,omitempty"`
	RestartPolicy    corev1.RestartPolicy          `json:"restartPolicy,omitempty"`
	Replicas         int32                         `json:"replicas,omitempty"`
	ResourceFlavor   string                        `json:"resourceFlavor,omitempty"`
	Resources        corev1.ResourceRequirements   `json:"resources,omitempty"`
	NodeSelector     map[string]string             `json:"nodeSelector,omitempty"`
	Affinity         *corev1.Affinity              `json:"affinity,omitempty"`
	SHMSize          resource.Quantity             `json:"shmSize,omitempty"`
	Command          []string                      `json:"command,omitempty"`
	Args             []string                      `json:"args,omitempty"`
	Privileged       bool                          `json:"privileged,omitempty"`
	WorkingDir       string                        `json:"workingDir,omitempty"`
	Configs          []ConfigFile                  `json:"configs,omitempty"`
	Mounts           []JobMount                    `json:"mounts,omitempty"`
	Env              []corev1.EnvVar               `json:"env,omitempty"`
	Ports            []JobPort                     `json:"ports,omitempty"`
	Lifecycle        *corev1.Lifecycle             `json:"lifecycle,omitempty"`
	HostNetwork      bool                          `json:"hostNetwork,omitempty"`
	PodSpec          *corev1.PodSpec               `json:"podSpec,omitempty"` // remove validation
}

type ConfigFile struct {
	// +kubebuilder:validation:Required
	Path  string `json:"path,omitempty"`
	Value string `json:"value,omitempty"`
}

// JobPort defines a port for a job role.
// It a limited version of [corev1.ContainerPort]
type JobPort struct {
	Name     string          `json:"name,omitempty"`
	Port     int32           `json:"port,omitempty"`
	Protocol corev1.Protocol `json:"protocol,omitempty"`
}

type JobAccessType string

const (
	JobAccessTypeNone  JobAccessType = ""
	JobAccessTypeHTTP  JobAccessType = "HTTP"
	JobAccessTypeHTTPS JobAccessType = "HTTPS"
	JobAccessTypeSSH   JobAccessType = "SSH"
)

type JobSpecPortAccess struct {
	Type     JobAccessType `json:"type,omitempty"`
	Host     string        `json:"host,omitempty"`
	Username string        `json:"username,omitempty"`
	Password string        `json:"password,omitempty"`
}

type JobMount struct {
	Annotations map[string]string `json:"annotations,omitempty"`
	// Name is the name of pvc
	Name     string `json:"name,omitempty"`
	Path     string `json:"path,omitempty"`
	Readonly bool   `json:"readonly,omitempty"`
}

type JobStatus struct {
	Replicas           int32                `json:"replicas,omitempty"`
	Selector           string               `json:"selector,omitempty"` // scale subresource selector
	Conditions         []JobStatusCondition `json:"conditions,omitempty"`
	Roles              []JobRoleStatus      `json:"roles,omitempty"`
	State              JobStatusState       `json:"state,omitempty"`
	History            []JobStatusState     `json:"history,omitempty"`
	Message            string               `json:"message,omitempty"`
	ObservedGeneration int64                `json:"observedGeneration,omitempty"`
}

type JobConditionType string

const (
	JobSummited  JobConditionType = "Summited"
	JobQueued    JobConditionType = "Queued"
	JobScheduled JobConditionType = "Scheduled"
	JobRunning   JobConditionType = "Running"
	JobSucceeded JobConditionType = "Succeeded"
)

type JobStatusCondition struct {
	Type               JobConditionType       `json:"type,omitempty"`
	Status             corev1.ConditionStatus `json:"status,omitempty"`
	LastTransitionTime metav1.Time            `json:"lastTransitionTime,omitempty"`
	Reason             string                 `json:"reason,omitempty"`
	Message            string                 `json:"message,omitempty"`
}

type JobPhase string

const (
	JobPhasePaused    JobPhase = "Paused"
	JobPhasePending   JobPhase = "Pending"
	JobPhaseQueued    JobPhase = "Queued"
	JobPhaseScheduled JobPhase = "Scheduled"
	JobPhaseRunning   JobPhase = "Running"
	JobPhaseFailed    JobPhase = "Failed"
	JobPhaseCompleted JobPhase = "Completed"
	JobPhaseUnknown   JobPhase = "Unknown"
)

type JobStatusState struct {
	LastTransitionTime metav1.Time  `json:"lastTransitionTime,omitempty"`
	StartTimestamp     *metav1.Time `json:"startTimestamp,omitempty"`
	RunningTimestamp   *metav1.Time `json:"runningTimestamp,omitempty"` // job is come into running state
	FinishTimestamp    *metav1.Time `json:"finishTimestamp,omitempty"`
	Phase              JobPhase     `json:"phase,omitempty"`
	Reason             string       `json:"reason,omitempty"`
}

type ReplicasStatus struct {
	Desired int32 `json:"desired,omitempty"`
	Current int32 `json:"current,omitempty"`
	Running int32 `json:"running,omitempty"`
}

type JobRoleStatus struct {
	Name      string                      `json:"name,omitempty"`
	Resources corev1.ResourceRequirements `json:"resources,omitempty"`
	Ports     []JobRolPortStatus          `json:"ports,omitempty"`
	Replicas  ReplicasStatus              `json:"replicas,omitempty"`
}

type JobRolPortStatus struct {
	Name   string             `json:"name,omitempty"`
	Port   int32              `json:"port,omitempty"`
	Access *JobSpecPortAccess `json:"access,omitempty"`
}

// +kubebuilder:object:root=true
type JobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	Items []Job `json:"items"`
}
