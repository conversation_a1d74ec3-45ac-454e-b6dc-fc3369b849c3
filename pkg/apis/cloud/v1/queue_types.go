package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
type Queue struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   QueueSpec   `json:"spec,omitempty"`
	Status QueueStatus `json:"status,omitempty"`
}

type QueueSpec struct {
	Request       corev1.ResourceList `json:"request,omitempty"`
	Limit         corev1.ResourceList `json:"limit,omitempty"`
	Guaranteed    corev1.ResourceList `json:"guaranteed,omitempty"`
	ReclaimPolicy ReclaimPolicy       `json:"reclaimPolicy,omitempty"`
}

type ReclaimPolicy string

const (
	ReclaimPolicyDeny  ReclaimPolicy = "Deny"
	ReclaimPolicyAllow ReclaimPolicy = "Allow"
)

type QueueStatus struct {
	Allocated corev1.ResourceList `json:"allocated,omitempty"`
}

// +kubebuilder:object:root=true
type QueueList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	Items []Queue `json:"items"`
}
