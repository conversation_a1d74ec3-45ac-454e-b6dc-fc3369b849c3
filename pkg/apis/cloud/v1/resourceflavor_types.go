package v1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:storageversion
// +kubebuilder:resource:scope=Cluster,shortName={flavor,flavors}
// +kubebuilder:printcolumn:name="Ready",type="string",JSONPath=".status.ready",description="Indicates whether the ResourceFlavor is ready to be used"
// +kubebuilder:subresource:status

// ResourceFlavor is a custom resource that defines a set of resources
// that can be used by workloads in a Kubernetes cluster.
type ResourceFlavor struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ResourceFlavorSpec   `json:"spec,omitempty"`
	Status ResourceFlavorStatus `json:"status,omitempty"`
}

type ResourceFlavorSpec struct {
	Tolerations []corev1.Toleration      `json:"tolerations,omitempty"`
	Resources   []ResourceFlavorResource `json:"resources,omitempty"`
}

type ResourceFlavorResource struct {
	Name         corev1.ResourceName `json:"name,omitempty"`
	Request      resource.Quantity   `json:"request,omitempty"`
	Limit        resource.Quantity   `json:"limit,omitempty"`
	NodeSelector map[string]string   `json:"nodeSelector,omitempty"`
	Annotations  map[string]string   `json:"annotations,omitempty"`
}

type ResourceFlavorStatus struct {
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// Ready indicates whether the ResourceFlavor is ready to be used.
	// If there are no free resources available, the ResourceFlavor is not ready.
	// +optional
	Ready bool `json:"ready,omitempty"`
}

// +kubebuilder:object:root=true
//
// ResourceFlavorList contains a list of ResourceFlavor
type ResourceFlavorList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ResourceFlavor `json:"items"`
}

func init() {
	SchemeBuilder.Register(
		&ResourceFlavor{}, &ResourceFlavorList{},
	)
}
