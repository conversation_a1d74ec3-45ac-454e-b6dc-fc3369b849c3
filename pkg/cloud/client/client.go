package client

import (
	"context"
	"path"
	"sync"

	"k8s.io/client-go/rest"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

// CloudClient is client of cloud service.
// other systems can use this interface to access cloud service.
type CloudClient interface {
	cluster.CloudInfoGetter
}

type CloudServiceOptions struct {
	Address string `json:"address" description:"Address of the cloud service"`
}

func NewDefaultCloudServiceOptions() *CloudServiceOptions {
	return &CloudServiceOptions{
		Address: "http://rune-cloud",
	}
}

func NewRemoteClient(ctx context.Context, options *CloudServiceOptions) (*RemoteCloudClient, error) {
	cli, err := httpclient.NewClientFromConfig(context.Background(), &httpclient.Config{Server: options.Address})
	if err != nil {
		return nil, err
	}
	return &RemoteCloudClient{cli: cli}, nil
}

var _ CloudClient = &RemoteCloudClient{}

type RemoteCloudClient struct {
	cli         *httpclient.Client
	clientcache map[string]cluster.Kubernetes
	mu          sync.RWMutex
}

// GetKubernetes implements CloudClient.
func (c *RemoteCloudClient) GetKubernetes(ctx context.Context, ref store.ObjectReference) (cluster.Kubernetes, error) {
	c.mu.RLock()
	cli, ok := c.clientcache[ref.Name]
	c.mu.RUnlock()
	if !ok {
		kubeconfig, err := c.GetKubeConfig(ctx, ref)
		if err != nil {
			return nil, err
		}
		kubeclis, err := cluster.NewKubernetesClients(ctx, kubeconfig)
		if err != nil {
			return nil, err
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		c.clientcache[ref.Name] = kubeclis
		cli = kubeclis
	}
	return cli, nil
}

func (c *RemoteCloudClient) GetKubeConfig(ctx context.Context, cluster store.ObjectReference) (*rest.Config, error) {
	proxypath := "/internal/clusters/" + cluster.Name + "/kubernetes"
	target := *c.cli.Server
	target.Path = path.Join(target.Path, proxypath)
	config := &rest.Config{
		Host:      target.String(),
		Transport: c.cli.RoundTripper,
	}
	return config, nil
}
