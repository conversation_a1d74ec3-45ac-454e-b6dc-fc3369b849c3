package client

import (
	"context"
	"fmt"
	"sync"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/watch"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/retry"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type WatchResource struct {
	Cluster         string
	GroupVersionKin schema.GroupVersionKind
}

var _ controller.Source[controller.ScopedKey] = &DynamicClusterResourceSource[controller.ScopedKey]{}

type KeyFunc[T comparable] func(cluster store.ObjectReference, obj client.Object) []T

func NewDynamicClusterResourceSource[T comparable](ctx context.Context, cloudInfo cluster.CloudInfoGetter, schema *runtime.Scheme, keyfunc KeyFunc[T]) *DynamicClusterResourceSource[T] {
	return &DynamicClusterResourceSource[T]{
		basectx:   ctx,
		cloudInfo: cloudInfo,
		schema:    schema,
		watches:   make(map[WatchResource]watchitem[T]),
		result:    make(chan T, 16), // Buffered channel to avoid blocking
		keyfunc:   keyfunc,
	}
}

type DynamicClusterResourceSource[T comparable] struct {
	basectx   context.Context
	cloudInfo cluster.CloudInfoGetter
	schema    *runtime.Scheme
	mu        sync.RWMutex
	watches   map[WatchResource]watchitem[T]
	result    chan T
	keyfunc   KeyFunc[T]
}

// Run implements controller.Source.
func (d *DynamicClusterResourceSource[T]) Run(ctx context.Context, queue controller.TypedQueue[T]) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case item := <-d.result:
			queue.Add(item)
		}
	}
}

// Run implements controller.Source.
func (d *DynamicClusterResourceSource[T]) StartWatch(ctx context.Context, cluster store.ObjectReference, obj client.ObjectList) error {
	gvk, err := apiutil.GVKForObject(obj, d.schema)
	if err != nil {
		return fmt.Errorf("failed to get GVK for object: %w", err)
	}
	resource := WatchResource{Cluster: cluster.String(), GroupVersionKin: gvk}
	d.mu.RLock()
	_, ok := d.watches[resource]
	d.mu.RUnlock()
	if ok {
		return nil
	}
	kubes, err := d.cloudInfo.GetKubernetes(ctx, cluster)
	if err != nil {
		return err
	}
	watch := watchitem[T]{
		client:  kubes.Client(),
		keyFunc: d.keyfunc,
		list:    obj,
	}
	go watch.runWithRetry(ctx, d.result)

	d.mu.Lock()
	defer d.mu.Unlock()

	d.watches[resource] = watch
	return nil
}

func (d *DynamicClusterResourceSource[T]) StopWatch(ctx context.Context, cluster store.ObjectReference, obj client.ObjectList) error {
	gvk, err := apiutil.GVKForObject(obj, d.schema)
	if err != nil {
		return fmt.Errorf("failed to get GVK for object: %w", err)
	}
	d.mu.Lock()
	defer d.mu.Unlock()

	resource := WatchResource{Cluster: cluster.String(), GroupVersionKin: gvk}
	watch, ok := d.watches[resource]
	if !ok {
		return nil
	}
	delete(d.watches, resource)
	watch.stop()
	return nil
}

type watchitem[T comparable] struct {
	cluster   store.ObjectReference
	client    client.WithWatch
	cancel    context.CancelFunc
	keyFunc   KeyFunc[T]
	list      client.ObjectList
	namespace string
}

func (w *watchitem[T]) runWithRetry(ctx context.Context, result chan T) error {
	return retry.OnError(ctx, func(ctx context.Context) error {
		return w.run(ctx, result)
	})
}

func (w *watchitem[T]) run(ctx context.Context, result chan T) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	w.cancel = cancel
	watcher, err := w.client.Watch(ctx, w.list, client.InNamespace(w.namespace))
	if err != nil {
		return err
	}
	for {
		select {
		case <-ctx.Done():
			return nil
		case event, ok := <-watcher.ResultChan():
			if !ok {
				return nil // Watch channel closed
			}
			if event.Type == watch.Error {
				return fmt.Errorf("watch error: %v", event.Object)
			}
			cliobj, ok := event.Object.(client.Object)
			if !ok {
				continue // Skip non-client.Object events
			}
			for _, key := range w.keyFunc(w.cluster, cliobj) {
				result <- key
			}
		}
	}
}

func (w *watchitem[T]) stop() {
	if w.cancel != nil {
		w.cancel()
	}
}
