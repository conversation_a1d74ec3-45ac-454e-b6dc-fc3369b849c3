package alert

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

func (a *API) ListTenantAlertRules(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertRule]{})
	})
}

func (a *API) GetTenantAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, rule string) (any, error) {
		return base.GenericGet(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) CreateTenantAlertRule(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		return base.GenericCreate(r, storage, &AlertRule{})
	})
}

func (a *API) UpdateTenantAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, rule string) (any, error) {
		return base.GenericUpdate(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) DeleteTenantAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, rule string) (any, error) {
		return base.GenericDelete(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) OnTenantAlertRule(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, rule string) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		return fn(ctx, storage, tenant, rule)
	})
}

func (a *API) tenantAlertRuleGroup() api.Group {
	return base.
		NewTenantGroup("/alertrules").
		Route(
			api.GET("").Operation("list alert rules").
				To(a.ListTenantAlertRules).
				Param(api.PageParams...).
				Response(store.List[AlertRule]{}),

			api.POST("").Operation("create alert rule").
				To(a.CreateTenantAlertRule).
				Param(api.BodyParam("rule", AlertRule{})).
				Response(AlertRule{}),

			api.GET("/{rule}").Operation("get alert rule").
				To(a.GetTenantAlertRule).
				Response(AlertRule{}),

			api.PUT("/{rule}").Operation("update alert rule").
				Param(api.BodyParam("rule", AlertRule{})).
				To(a.UpdateTenantAlertRule),

			api.DELETE("/{rule}").Operation("delete alert rule").
				To(a.DeleteTenantAlertRule),
		)
}

func (a *API) ListTenantAlertChannels(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertChannel]{})
	})
}

func (a *API) GetTenantAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, channel string) (any, error) {
		return base.GenericGet(r, storage, &AlertChannel{}, channel)
	})
}

func (a *API) CreateTenantAlertChannel(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		obj := &AlertChannel{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if obj.Name == "" {
			return nil, errors.NewBadRequest("name is required")
		}
		if err := storage.Create(ctx, obj); err != nil {
			return nil, err
		}
		return obj, nil
	})
}

func (a *API) UpdateTenantAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, channel string) (any, error) {
		obj := &AlertChannel{}
		if channel == "" {
			return nil, errors.NewBadRequest("channel name is required")
		}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if obj.Name == "" {
			obj.Name = channel
		}
		obj.SetName(channel)
		if err := storage.Update(ctx, obj); err != nil {
			return nil, err
		}
		return obj, nil
	})
}

func (a *API) DeleteTenantAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnTenantAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, name string) (any, error) {
		channel := &AlertChannel{
			ObjectMeta: store.ObjectMeta{
				Name: name,
			},
		}
		return nil, storage.Delete(ctx, channel)
	})
}

func (a *API) OnTenantAlertChannel(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, channel string) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant))
		channel := api.Path(r, "channel", "")
		if channel == "" {
			return nil, errors.NewBadRequest("channel name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("alertchannels")
		if err := storage.Get(ctx, channel, obj); err != nil {
			return nil, err
		}
		return fn(ctx, storage, tenant, channel)
	})
}

func (a *API) tenantAlertChannelGroup() api.Group {
	return base.
		NewTenantGroup("/alertchannels").
		Route(
			api.GET("").
				To(a.ListTenantAlertChannels).
				Operation("list alert channels").
				Param(api.PageParams...).
				Response(store.List[AlertChannel]{}),

			api.POST("").
				To(a.CreateTenantAlertChannel).
				Operation("Create an alert channel").
				Param(
					api.BodyParam("alertchannel", AlertChannel{}),
				),

			api.GET("/{channel}").
				To(a.GetTenantAlertChannel).
				Operation("Get an alert channel").
				Response(AlertChannel{}),

			api.PUT("/{channel}").
				Operation("Update an alert channel").
				To(a.UpdateTenantAlertChannel).
				Param(api.BodyParam("alertchannel", AlertChannel{})),

			api.DELETE("/{channel}").
				To(a.DeleteTenantAlertChannel).
				Operation("Delete an alert channel"),
		)
}
