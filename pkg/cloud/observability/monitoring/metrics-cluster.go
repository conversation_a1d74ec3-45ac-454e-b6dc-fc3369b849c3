package monitoring

import (
	"context"
	"net/http"
	"strings"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resources"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/dashboard"
)

var ContainerClusterMetricsMapper = map[string]string{
	"etcd_request_persecond":        "sum(irate(etcd_request_duration_seconds_count[5m]))",
	"etcd_latency_seconds_p95":      `histogram_quantile(0.95, sum(irate(etcd_request_duration_seconds_bucket[5m])) by (le, operation)) * 1000 * 1000`,
	"etcd_latency_seconds_p99":      `histogram_quantile(0.99, sum(irate(etcd_request_duration_seconds_bucket[5m])) by (le, operation)) * 1000 * 1000`,
	"apiserver_latency_seconds_p95": `histogram_quantile(0.95, sum(irate(apiserver_request_duration_seconds_bucket{verb!~"WATCH|CONNECT"}[5m])) by (le, verb)) * 1000 * 1000`,
	"apiserver_latency_seconds_p99": `histogram_quantile(0.99, sum(irate(apiserver_request_duration_seconds_bucket{verb!~"WATCH|CONNECT"}[5m])) by (le, verb)) * 1000 * 1000`,
	"apiserver_request_persecond":   `sum(irate(apiserver_request_total[5m]))by(code)`,
	"cpu_used_cores":                `round(sum(irate(node_cpu_seconds_total{mode!="idle"}[5m])), 0.001)`,
	"memory_used_bytes":             `sum(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes)`,
	"disk_used_bytes":               `max(node_filesystem_size_bytes{device=~"/dev/.*", device!~"/dev/loop\\d+"} - node_filesystem_avail_bytes{device=~"/dev/.*", device!~"/dev/loop\\d+"})by(node, device)`,
	"pod_running_count":             `count(kube_pod_status_phase{phase="Running"})`,
}

func (a *API) GetClusterMetrics(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, ref store.ObjectReference, op operation.ContainerOperation) (any, error) {
		meta := resources.ResourceMetaFromRequest(r)
		// cluster metrics
		if meta.GroupVersionResource.Empty() {
			return GetClusterMetrics(r, op)
		}
		// resource metrics
		return GetResourceMetrics(r, op, meta)
	})
}

func GetClusterMetrics(r *http.Request, op operation.ContainerOperation) (any, error) {
	return GetMetrics(r, op, ContainerClusterMetricsMapper, "")
}

func (a *API) GetClusterDashboard(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, ref store.ObjectReference, op operation.ContainerOperation) (any, error) {
		fullname := "cluster-" + api.Path(r, "dashboard", "basic")
		builtin, ok := a.BuiltInDashboards[fullname]
		if !ok {
			return nil, errors.NewNotFound("dashboard", fullname)
		}
		options := dashboard.GetRenderDashboardOptions(r, map[string]string{
			"cluster": api.Path(r, "cluster", ""),
		})
		return dashboard.RenderDashboardSimple(r, op, builtin, options)
	})
}

func (a *API) QueryClusterDashboard(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, ref store.ObjectReference, op operation.ContainerOperation) (any, error) {
		dashboardname := api.Path(r, "dashboard", "basic")
		storage := a.Store.Scope(ref.Scopes...).Scope(base.ScopeCluster(ref.Name))
		meta := resources.ResourceMetaFromRequest(r)

		config, err := a.getClusterDashboard(ctx, storage, meta.Resource, dashboardname)
		if err != nil {
			return nil, err
		}
		options := getClusterRenderDashboardOptions(r, meta)
		return dashboard.RenderDashboardSimple(r, op, config, options)
	})
}

func (a *API) ListClusterDashboardParams(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, ref store.ObjectReference, op operation.ContainerOperation) (any, error) {
		dashboardname := api.Path(r, "dashboard", "basic")
		storage := a.Store.Scope(ref.Scopes...).Scope(base.ScopeCluster(ref.Name))
		meta := resources.ResourceMetaFromRequest(r)

		config, err := a.getClusterDashboard(ctx, storage, "", dashboardname)
		if err != nil {
			return nil, err
		}
		options := getClusterRenderDashboardOptions(r, meta)
		return dashboard.ListDashboardParams(ctx, op, config, options)
	})
}

func getClusterRenderDashboardOptions(r *http.Request, meta resources.RequestMetadata) dashboard.RenderDashboardOptions {
	params := map[string]string{
		"namespace": meta.Namespace,
		// it sets the resource name as the key in the params
		// eg.  pods:abc 		-> pod=abc
		// eg.  services:abc 	-> service=abc
		simpleToSingular(meta.Resource): meta.Name,
	}
	return dashboard.GetRenderDashboardOptions(r, params)
}

func simpleToSingular(resource string) string {
	if strings.HasSuffix(resource, "s") {
		return strings.TrimSuffix(resource, "s")
	}
	return resource
}

func (a *API) getClusterDashboard(ctx context.Context, storage store.Store, resource, dashboardname string) (dashboard.DashboardConfiguration, error) {
	fullname := dashboard.GetBuiltClusterDashboardName(resource, dashboardname)
	config, ok := a.BuiltInDashboards[fullname]
	if !ok {
		// built in dashboards are prefixed to avoid conflict with other dashboards
		record := &MetricsDashboard{}
		if err := storage.Get(ctx, dashboardname, record); err != nil {
			return dashboard.DashboardConfiguration{}, err
		}
		config = record.Dashboard
	}
	return config, nil
}

func (a *API) onClusterPromethus(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, ref store.ObjectReference, op operation.ContainerOperation) (any, error)) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		kubes, err := a.CloudInfo.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)
		return fn(ctx, ref, op)
	})
}

var ClusterMetricsParams = []api.Param{
	api.QueryParam("filter", "filter expression,example: foo=bar,pod=abc ").Optional(),
	api.QueryParam("by", "sum/avg by labels").In("node", "pod", "instance").Optional(),
	api.QueryParam("aggregate", "aggregate function").In("sum", "avg", "max", "min").Optional(),
	api.PathParam("metrics", "metrics name").
		In(getMetricskeys(ContainerClusterMetricsMapper)...),
}

func getMetricskeys(mapper map[string]string) []any {
	keys := make([]any, 0, len(mapper))
	for k := range mapper {
		keys = append(keys, k)
	}
	return keys
}

func (a *API) GetClusterMetricsGroup() api.Group {
	return api.
		NewGroup("/metrics").
		Route(
			api.GET("/{metrics}").
				Doc("Get cluster metrics").
				Param(GetMetricsParams...).
				Param(ClusterMetricsParams...).
				To(a.GetClusterMetrics),
		)
}

func (a *API) ClusterDashboardQueryGroup() api.Group {
	return api.NewGroup("/metrics-dashboards").
		Route(
			api.GET("/{dashboard}/query").Operation("query monitor dashboard").
				To(a.QueryClusterDashboard).
				Param(MetricsDashboardParams...).
				Response(QueryResponse{}),

			api.GET("/{dashboard}/params").Operation("list available params").
				To(a.ListClusterDashboardParams).
				Response([]dashboard.DashboardParam{}),
		)
}

func (a *API) ClusterGroup() api.Group {
	return base.NewClusterGroup().
		SubGroup(
			api.NewGroup("").
				SubGroup(
					a.GetClusterMetricsGroup(),
					a.ClusterDashboardQueryGroup(),
				),
			api.NewGroup("/apis/{group}/{version}/{resource}/{name}").
				SubGroup(
					a.GetClusterMetricsGroup(),
					a.ClusterDashboardQueryGroup(),
				),
			api.NewGroup("/apis/{group}/{version}/namespaces/{namespace}/{resource}/{name}").
				SubGroup(
					a.GetClusterMetricsGroup(),
					a.ClusterDashboardQueryGroup(),
				),
		)
}
