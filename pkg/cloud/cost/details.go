package cost

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/base"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&ResourceDetails{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"from", "to"},
		},
	})
}

type ResourceDetails struct {
	store.ObjectMeta `json:",inline"`
	Tenant           string     `json:"tenant,omitempty"`
	Organization     string     `json:"organization,omitempty"`
	Application      string     `json:"application,omitempty"`
	From             time.Time  `json:"from,omitempty"`
	To               time.Time  `json:"to,omitempty"`
	Items            []CostItem `json:"items,omitempty"`
}

func NewDetailsAPI(mongo store.Store) *DetailsAPI {
	return &DetailsAPI{Store: mongo}
}

type DetailsAPI struct {
	Store store.Store
}

func (a *DetailsAPI) ListResourceDetails(w http.ResponseWriter, r *http.Request) {
	a.OnAny(w, r, func(ctx context.Context, reqs store.Requirements) (any, error) {
		from, to := api.Query(r, "from", time.Time{}), api.Query(r, "to", time.Time{})
		if !from.IsZero() {
			reqs = append(reqs, store.NewRequirement("from", store.GreaterThanOrEqual, from))
		}
		if !to.IsZero() {
			reqs = append(reqs, store.NewRequirement("to", store.LessThanOrEqual, to))
		}
		if tenant := api.Query(r, "tenant", ""); tenant != "" {
			reqs = append(reqs, store.RequirementEqual("tenant", tenant))
		}
		if org := api.Query(r, "organization", ""); org != "" {
			reqs = append(reqs, store.RequirementEqual("organization", org))
		}
		if app := api.Query(r, "application", ""); app != "" {
			reqs = append(reqs, store.RequirementEqual("application", app))
		}
		return base.GenericList(r, a.Store, &store.List[ResourceDetails]{}, store.WithFieldRequirements(reqs...))
	})
}

func (a *DetailsAPI) GetResourceDetails(w http.ResponseWriter, r *http.Request) {
	a.OnAny(w, r, func(ctx context.Context, reqs store.Requirements) (any, error) {
		name := api.Path(r, "id", "")
		if name == "" {
			return nil, errors.NewBadRequest("id is required")
		}
		return base.GenericGet(r, a.Store, &ResourceDetails{}, name, store.WithGetFieldRequirements(reqs...))
	})
}

func (a *DetailsAPI) DeleteResourceDetails(w http.ResponseWriter, r *http.Request) {
	a.OnAny(w, r, func(ctx context.Context, reqs store.Requirements) (any, error) {
		name := api.Path(r, "id", "")
		if name == "" {
			return nil, errors.NewBadRequest("id is required")
		}
		return base.GenericDelete(r, a.Store, &ResourceDetails{}, name, store.WithDeleteFieldRequirements(reqs...))
	})
}

func (a *DetailsAPI) OnAny(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, reqs store.Requirements) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		reqs := store.Requirements{}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			reqs = append(reqs, store.RequirementEqual("tenant", tenant))
		}
		if org := api.Path(r, "organization", ""); org != "" {
			reqs = append(reqs, store.RequirementEqual("organization", org))
		}
		if app := api.Path(r, "application", ""); app != "" {
			reqs = append(reqs, store.RequirementEqual("application", app))
		}
		return fn(ctx, reqs)
	})
}

func (a *DetailsAPI) Group() api.Group {
	listparams := []api.Param{
		api.QueryParam("from", "").Format("date-time").Optional(),
		api.QueryParam("to", "").Format("date-time").Optional(),
	}
	return api.NewGroup("").
		SubGroup(
			api.
				NewGroup("/resourcedetails").
				Route(
					api.GET("").
						To(a.ListResourceDetails).
						Param(api.PageParams...).
						Param(listparams...).
						Param(
							api.QueryParam("tenant", "").Optional(),
							api.QueryParam("organization", "").Optional(),
							api.QueryParam("application", "").Optional(),
						).
						Response(store.List[ResourceDetails]{}),

					api.GET("/{id}").
						To(a.GetResourceDetails).
						Response(ResourceDetails{}),

					api.DELETE("/{id}").
						To(a.DeleteResourceDetails),
				),
			base.NewTenantGroup("/resourcedetails").
				Route(
					api.GET("").
						To(a.ListResourceDetails).
						Param(
							api.QueryParam("organization", "").Optional(),
							api.QueryParam("application", "").Optional(),
						).
						Param(listparams...).
						Param(api.PageParams...).
						Response(store.List[ResourceDetails]{}),
					api.GET("/{id}").
						To(a.GetResourceDetails).
						Response(ResourceDetails{}),
				),
			base.NewTenantOrganizationGroup("/resourcedetails").
				Route(
					api.GET("").
						To(a.ListResourceDetails).
						Param(api.PageParams...).
						Response(store.List[ResourceDetails]{}),
					api.GET("/{id}").
						Param(
							api.QueryParam("organization", "").Optional(),
							api.QueryParam("application", "").Optional(),
						).
						Param(listparams...).
						To(a.GetResourceDetails).
						Response(ResourceDetails{}),
				),
			base.NewInstanceGroup().
				SubGroup(
					api.NewGroup("/resourcedetails").
						Route(
							api.GET("").
								To(a.ListResourceDetails).
								Param(api.PageParams...).
								Response(store.List[ResourceDetails]{}),
							api.GET("/{id}").
								To(a.GetResourceDetails).
								Response(ResourceDetails{}),
						),
				),
		)
}
