package cloud

import (
	"context"

	"github.com/go-openapi/spec"
	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/metadata"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/priority"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resourcequota"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resources"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/flavor"
	"xiaoshiai.cn/rune/pkg/cloud/observability"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/dashboard"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

type Options struct {
	Listen        string                                 `json:"listen" description:"Listen address for the OIDC CAS Shim service" default:":8080"`
	API           *APIOptions                            `json:"api,omitempty" description:"API options for the server"`
	Config        *config.DynamicConfigOptions           `json:"config,omitempty" description:"Dynamic configuration options for the server"`
	Etcd          *etcdcache.Options                     `json:"etcd,omitempty"`
	Mongodb       *mongo.MongoDBOptions                  `json:"mongodb,omitempty"`
	RequestHeader *api.RequestHeaderAuthenticatorOptions `json:"requestHeader,omitempty"`
}
type APIOptions struct {
	Prefix string `json:"prefix,omitempty" description:"API prefix for the server, default is '/v1'"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Listen:        ":8080",
		API:           &APIOptions{Prefix: "/v1"},
		Config:        config.NewDefaultDynamicConfigOptions("cloud"),
		Etcd:          etcdcache.NewDefaultOptions(),
		Mongodb:       mongo.NewDefaultMongoOptions("cloud"),
		RequestHeader: api.NewDefaultRequestHeaderAuthenticatorOptions(),
	}
}

func Run(ctx context.Context, options *Options) error {
	deps, err := BuildDependencies(ctx, options)
	if err != nil {
		return err
	}
	manager, err := BuildAPIServerController(ctx, deps)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return RunServer(ctx, deps)
	})
	eg.Go(func() error {
		return manager.Run(ctx)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type Dependencies struct {
	Options           *Options
	Storage           store.Store
	MongoStorage      store.Store
	Config            config.DynamicConfig
	Authn             api.Authenticator
	BuiltInDashboards map[string]dashboard.DashboardConfiguration
	CloudInfoHolder   cluster.CloudInfoHolder
}

func BuildDependencies(ctx context.Context, options *Options) (*Dependencies, error) {
	etcdstore, err := etcdcache.NewEtcdCacher(options.Etcd, etcdcache.ResourceFieldsMap{})
	if err != nil {
		return nil, err
	}
	mongodbstore, err := mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, options.Mongodb)
	if err != nil {
		return nil, err
	}
	authn := api.NewRequestHeaderAuthenticator(options.RequestHeader)

	cloudinfos := cluster.NewDefaultCloudInfoHolder()

	builtindashboards, err := dashboard.NewBuiltInDashboards()
	if err != nil {
		return nil, err
	}

	return &Dependencies{
		Options:           options,
		Storage:           etcdstore,
		MongoStorage:      mongodbstore,
		Authn:             authn,
		BuiltInDashboards: builtindashboards,
		CloudInfoHolder:   cloudinfos,
	}, nil
}

func BuildAPIServerController(ctx context.Context, deps *Dependencies) (*ApiserverControllerManager, error) {
	cm := controller.NewControllerManager()

	// clusterinfos controller manager connection info to clusterinfos
	clusterinfos, err := cluster.NewClusterInfoController(ctx, deps.Storage, deps.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	cm.AddController(clusterinfos)

	return &ApiserverControllerManager{manager: cm, deps: deps}, nil
}

type ApiserverControllerManager struct {
	manager *controller.ControllerManager
	deps    *Dependencies
}

func (c *ApiserverControllerManager) Run(ctx context.Context) error {
	if err := cluster.InitCloudInfoHolder(ctx, c.deps.Storage, c.deps.CloudInfoHolder); err != nil {
		return err
	}
	return c.manager.Run(ctx)
}

func RunServer(ctx context.Context, deps *Dependencies) error {
	clusterapi := cluster.NewAPI(deps.Storage, deps.CloudInfoHolder)
	clustermetadata := metadata.NewAPI(deps.Storage, deps.CloudInfoHolder)
	clusterresources := resources.NewClusterResourcesAPI(deps.CloudInfoHolder)
	clusterflavor := flavor.NewAPI(deps.Storage, deps.CloudInfoHolder)
	clusterpriority := priority.NewAPI(deps.CloudInfoHolder)
	resourcequota := resourcequota.NewAPI(deps.Storage, deps.CloudInfoHolder)
	observability := observability.NewAPI(deps.Storage, deps.MongoStorage, deps.CloudInfoHolder, deps.BuiltInDashboards)
	workspace := workspace.NewAPI(deps.Storage, deps.CloudInfoHolder)

	return api.New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", func(swagger *spec.Swagger) {
				base.UserGroupSecurityFunc(swagger)
			}),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			api.NewGroup("/internal").
				SubGroup(
					clusterapi.InternalGroup(),
				),
			api.NewGroup(deps.Options.API.Prefix).
				Filter(
					api.NewAuthenticateFilter(deps.Authn, nil),
				).
				SubGroup(
					clusterapi.Group(),
					clustermetadata.Group(),
					clusterresources.Group(),
					clusterflavor.Group(),
					clusterpriority.Group(),
					resourcequota.Group(),
					observability.Group(),
					workspace.Group(),
				),
		).
		Serve(ctx, deps.Options.Listen)
}
