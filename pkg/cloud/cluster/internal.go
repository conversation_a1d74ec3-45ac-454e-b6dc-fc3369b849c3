package cluster

import (
	"context"
	"net/http"
	"net/http/httputil"

	"xiaoshiai.cn/common/rest/api"
	libproxy "xiaoshiai.cn/common/rest/proxy"
)

func (a *API) InternalProxyKubernetes(w http.ResponseWriter, r *http.Request) {
	OnClusterInfo(w, r, a.CloudInfo, func(ctx context.Context, kubes Kubernetes) (any, error) {
		reqpath := "/" + api.Path(r, "path", "")
		cliconfig := kubes.GetKubernetesAddr()
		reverseProxy := &httputil.ReverseProxy{
			Transport: cliconfig.RoundTripper,
			Rewrite: func(pr *httputil.ProxyRequest) {
				pr.Out.URL.Path = reqpath
				pr.SetURL(cliconfig.Server)
			},
			FlushInterval: -1,
			ErrorHandler:  libproxy.ErrorResponser{}.Error,
		}
		reverseProxy.ServeHTTP(w, r)
		return nil, nil
	})
}

func (a *API) InternalGroup() api.Group {
	return api.
		NewGroup("").
		Tag("Internal").
		Route(
			api.Any("/clusters/{cluster}/kubernetes/{path}*").
				Operation("proxy to kubernetes apiserver").
				To(a.InternalProxyKubernetes),
		)
}
