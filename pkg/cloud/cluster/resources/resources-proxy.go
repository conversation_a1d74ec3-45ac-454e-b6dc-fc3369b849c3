package resources

import (
	"context"
	"net/http"

	"github.com/gorilla/websocket"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/rest/proxy"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func (s *ClusterResourcesAPI) ProxyGroup() api.Group {
	return api.NewGroup("/proxy/{service}").
		Route(
			api.GET("").To(Redirect).Doc("Redirect to service"),
			api.HEAD("").To(Redirect).Doc("Redirect to service"),
			api.Any("/{subresource}*").
				Doc("Proxy to service").
				To(s.ServiceProxy),
		)
}

func Redirect(w http.ResponseWriter, r *http.Request) {
	var queryPart string
	if len(r.URL.RawQuery) > 0 {
		queryPart = "?" + r.URL.RawQuery
	}
	http.Redirect(w, r, r.URL.Path+"/"+queryPart, http.StatusMovedPermanently)
}

func (s *ClusterResourcesAPI) ServiceProxy(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		svc := api.Path(r, "service", "")
		cliconfig, err := kubes.GetServiceProxyConfig(svc, IsWebsocketRequest(r))
		if err != nil {
			return nil, err
		}
		return ClusterProxyHandle(w, r, cliconfig, cliconfig.Server.Path, "/"+meta.Subresource)
	})
}

func IsWebsocketRequest(r *http.Request) bool {
	return websocket.IsWebSocketUpgrade(r) // Check if the request is a WebSocket upgrade request
}

func ClusterProxyHandle(w http.ResponseWriter, r *http.Request, clienconfig *httpclient.ClientConfig, removeprefix, reqpath string) (any, error) {
	p := proxy.Proxy{ClientConfig: clienconfig, RemovePrefix: removeprefix, RequestPath: reqpath, ProxyPrefix: "/proxy"}
	p.ServeHTTP(w, r)
	return nil, nil
}
