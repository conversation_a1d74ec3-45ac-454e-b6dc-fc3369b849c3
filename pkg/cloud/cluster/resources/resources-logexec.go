package resources

import (
	"context"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	corev1 "k8s.io/api/core/v1"
	httpstreamspdy "k8s.io/apimachinery/pkg/util/httpstream/spdy"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/remotecommand"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/ws"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

const DefaultExecCommand = "[ -x /bin/bash ] && exec /bin/bash || exec /bin/sh"

func (c ClusterResourcesAPI) PodLog(w http.ResponseWriter, r *http.Request) {
	c.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		logopt := corev1.PodLogOptions{
			Container:                    api.Query(r, "container", ""),
			Follow:                       api.Query(r, "follow", false),
			Previous:                     api.Query(r, "previous", false),
			SinceSeconds:                 api.Query(r, "sinceSeconds", (*int64)(nil)),
			TailLines:                    api.Query(r, "tailLines", (*int64)(nil)),
			InsecureSkipTLSVerifyBackend: api.Query(r, "insecureSkipTLSVerifyBackend", false),
		}
		stream, err := kubes.Kubernetes().CoreV1().Pods(meta.Namespace).GetLogs(meta.Name, &logopt).Stream(r.Context())
		if err != nil {
			return nil, err
		}
		defer stream.Close()

		upgrader := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
		ws, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			return nil, err
		}
		defer ws.Close()

		writer := WriterFunc(func(p []byte) (n int, err error) {
			if err := ws.WriteMessage(websocket.BinaryMessage, p); err != nil {
				return 0, err
			}
			return len(p), nil
		})
		_, _ = io.Copy(writer, stream)
		return nil, nil
	})
}

type WriterFunc func(p []byte) (n int, err error)

func (f WriterFunc) Write(p []byte) (n int, err error) {
	return f(p)
}

func (c ClusterResourcesAPI) PodExec(w http.ResponseWriter, r *http.Request) {
	c.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		log := log.FromContext(r.Context())
		commands := SplitCommand(api.Query(r, "command", ""))
		if len(commands) == 0 {
			commands = []string{"sh", "-c", DefaultExecCommand}
		}
		options := corev1.PodExecOptions{
			TTY:       api.Query(r, "tty", true),
			Stdin:     api.Query(r, "stdin", true),
			Stdout:    api.Query(r, "stdout", true),
			Stderr:    api.Query(r, "stderr", true),
			Container: api.Query(r, "container", ""),
			Command:   commands,
		}
		executor, err := NewPodExecutorWithTransport(kubes.Kubernetes(), kubes.RestConfig(), kubes.Transport(), meta.Namespace, meta.Name, &options)
		if err != nil {
			return nil, err
		}
		// prepare websocket
		upgrader := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
		wsc, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Error(err, "Failed to upgrade websocket")
			return nil, err
		}
		defer wsc.Close()

		stream := ws.NewExecStream(ctx, wsc)
		streamOptions := remotecommand.StreamOptions{
			Tty:               options.TTY,
			Stdin:             stream,
			Stdout:            stream,
			Stderr:            stream,
			TerminalSizeQueue: stream,
		}
		if err := executor.StreamWithContext(ctx, streamOptions); err != nil {
			log.Error(err, "Failed to execute command")
			// write error message to stream
			stream.Write([]byte(err.Error()))
			closeWsWithReason(wsc, websocket.CloseInternalServerErr, "")
		} else {
			closeWsWithReason(wsc, websocket.CloseNormalClosure, "")
		}
		return nil, nil
	})
}

func NewPodExecutorWithTransport(
	cs kubernetes.Interface, config *rest.Config,
	optionalTransport http.RoundTripper,
	namespace, name string, options *corev1.PodExecOptions,
) (remotecommand.Executor, error) {
	upgradeRoundTripper, err := httpstreamspdy.NewRoundTripperWithConfig(httpstreamspdy.RoundTripperConfig{
		PingPeriod:       time.Second * 5,
		UpgradeTransport: optionalTransport,
	})
	if err != nil {
		return nil, err
	}
	wrapper, err := rest.HTTPWrappersForConfig(config, upgradeRoundTripper)
	if err != nil {
		return nil, err
	}
	req := ws.NewPodExecutRequest(cs, namespace, name, options)
	return remotecommand.NewSPDYExecutorForTransports(wrapper, upgradeRoundTripper, "POST", req.URL())
}

func closeWsWithReason(ws *websocket.Conn, code int, reason string) {
	const maxControlFramePayloadSize = 125
	data := websocket.FormatCloseMessage(code, reason)
	if len(data) > maxControlFramePayloadSize {
		data = data[:maxControlFramePayloadSize]
	}
	ws.WriteControl(websocket.CloseMessage, data, time.Time{})
}

func SplitCommand(command string) []string {
	return slices.DeleteFunc(strings.Split(command, " "), func(s string) bool {
		return s == ""
	})
}
