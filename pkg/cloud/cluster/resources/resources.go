package resources

import (
	"context"
	"encoding/json"
	"io"
	"mime"
	"net/http"
	"path"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type ClusterResourcesOnClusterInfoFunc func(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error))

type ClusterResourcesAPI struct {
	OnClusterInfo ClusterResourcesOnClusterInfoFunc
}

func NewClusterResourcesAPI(cloudinfo cluster.CloudInfoGetter) *ClusterResourcesAPI {
	return &ClusterResourcesAPI{
		OnClusterInfo: DefaultOnClusterInfo(cloudinfo),
	}
}

func DefaultOnClusterInfo(infos cluster.CloudInfoGetter) ClusterResourcesOnClusterInfoFunc {
	return func(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error)) {
		cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
			metadata := ResourceMetaFromRequest(r)
			info, err := infos.GetKubernetes(ctx, ref)
			if err != nil {
				return nil, err
			}
			return fn(r.Context(), info, metadata)
		})
	}
}

func NewClusterResourcesAPIWithGetCluster(onInfo ClusterResourcesOnClusterInfoFunc) *ClusterResourcesAPI {
	return &ClusterResourcesAPI{OnClusterInfo: onInfo}
}

func (s *ClusterResourcesAPI) ResourcesGroup(namespaced bool) api.Group {
	namespacepath := ""
	if namespaced {
		namespacepath = "/namespaces/{namespace}"
	}
	return api.NewGroup("/apis").
		Route(
			api.GET("/{group}/{version}"+namespacepath+"/{resource}").
				Doc("List resources").
				To(s.List),
			api.POST("/{group}/{version}"+namespacepath+"/{resource}").
				Doc("Create resource").
				To(s.Create),
			api.GET("/{group}/{version}"+namespacepath+"/{resource}/{name}").
				Doc("Get resource").
				To(s.Get),
			api.PATCH("/{group}/{version}"+namespacepath+"/{resource}/{name}").
				Doc("Patch resource").
				To(s.Patch),
			api.PUT("/{group}/{version}"+namespacepath+"/{resource}/{name}").
				Doc("Update resource").
				To(s.Update),
			api.DELETE("/{group}/{version}"+namespacepath+"/{resource}/{name}").
				Doc("Delete resource").
				To(s.Delete),
			api.GET("/{group}/{version}"+namespacepath+"/{resource}/{name}/events").
				Doc("List events").
				To(s.Events),
			api.Any("/{group}/{version}"+namespacepath+"/{resource}/{name}/{subresource}*").
				Doc("Subresource").
				To(s.SubResource),
			api.POST("/{group}/{version}"+namespacepath+"/{resource}/{name}:set-metadata").
				Doc("set resource metadata").
				Param(
					api.BodyParam("metadata", &ResourceMetadata{}),
				).
				To(s.SetMetadata),
		)
}

func (s *ClusterResourcesAPI) Get(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		options := metav1.GetOptions{}
		if err := metav1.Convert_url_Values_To_v1_GetOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
			return nil, err
		}
		return kubes.Dynamic().
			Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).Get(r.Context(), meta.Name, options)
	})
}

func (s *ClusterResourcesAPI) List(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		listOptions := metav1.ListOptions{}
		if err := metav1.Convert_url_Values_To_v1_ListOptions(ptr.To(r.URL.Query()), &listOptions, nil); err != nil {
			return nil, err
		}
		list, err := listWithAddional(r, kubes, meta, listOptions)
		if err != nil {
			return nil, err
		}
		// page the list
		pageed := api.PageObjectFromRequest(r, list.Items)
		return pageed, nil
	})
}

func listWithAddional(r *http.Request, kubes cluster.Kubernetes, meta RequestMetadata, listOptions metav1.ListOptions) (*unstructured.UnstructuredList, error) {
	switch meta.GroupVersionResource {
	case corev1.SchemeGroupVersion.WithResource("persistentvolumeclaims"):
		ctx := r.Context()
		allpvc := &unstructured.UnstructuredList{}
		allpvc.SetGroupVersionKind(corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimList"))

		cli := kubes.Client()
		if err := cli.List(ctx, allpvc, &client.ListOptions{Namespace: meta.Namespace, Raw: &listOptions}); err != nil {
			return nil, err
		}
		allpods := &corev1.PodList{}
		if err := cli.List(ctx, allpods, client.InNamespace(meta.Namespace)); err != nil {
			return nil, err
		}
		pvcPods := make(map[string][]string)
		for _, pod := range allpods.Items {
			for _, vol := range pod.Spec.Volumes {
				if vol.PersistentVolumeClaim != nil {
					pvcPods[vol.PersistentVolumeClaim.ClaimName] = append(pvcPods[vol.PersistentVolumeClaim.ClaimName], pod.Name)
				}
			}
		}
		for i := range allpvc.Items {
			if pods, ok := pvcPods[allpvc.Items[i].GetName()]; ok {
				unstructured.SetNestedStringSlice(allpvc.Items[i].Object, pods, "status", "mountedByPods")
			}
		}
		return allpvc, nil
	default:
		return kubes.Dynamic().Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).List(r.Context(), listOptions)
	}
}

func (s *ClusterResourcesAPI) Create(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		options := metav1.CreateOptions{}
		if err := metav1.Convert_url_Values_To_v1_CreateOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
			return nil, err
		}
		obj, err := ReadObject(meta, r.Body)
		if err != nil {
			return nil, err
		}
		return kubes.Dynamic().Resource(meta.GroupVersionResource).Namespace(meta.Namespace).Create(r.Context(), obj, options)
	})
}

func ReadObject(meta RequestMetadata, reader io.Reader) (*unstructured.Unstructured, error) {
	obj := &unstructured.Unstructured{}
	if err := json.NewDecoder(reader).Decode(obj); err != nil {
		return nil, err
	}
	// override name/namespace in body if set in url
	obj.SetNamespace(meta.Namespace)
	if meta.Name != "" {
		obj.SetName(meta.Name)
	}
	return obj, nil
}

func (s *ClusterResourcesAPI) Patch(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		options := metav1.PatchOptions{}
		if err := metav1.Convert_url_Values_To_v1_PatchOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
			return nil, err
		}
		patchType, _, err := mime.ParseMediaType(r.Header.Get("Content-Type"))
		if err != nil {
			return nil, err
		}
		if patchType == "application/json" {
			// default to merge patch
			patchType = string(types.MergePatchType)
		}
		patchData, err := io.ReadAll(r.Body)
		if err != nil {
			return nil, err
		}
		return kubes.Dynamic().Resource(meta.GroupVersionResource).Namespace(meta.Namespace).
			Patch(r.Context(), meta.Name, types.PatchType(patchType), patchData, options)
	})
}

func (s *ClusterResourcesAPI) Update(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		options := metav1.UpdateOptions{}
		if err := metav1.Convert_url_Values_To_v1_UpdateOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
			return nil, err
		}
		obj, err := ReadObject(meta, r.Body)
		if err != nil {
			return nil, err
		}
		// remove resourceVersion to avoid conflict
		obj.SetResourceVersion("")
		return kubes.Dynamic().Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).Update(r.Context(), obj, options)
	})
}

func (s *ClusterResourcesAPI) Delete(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		options := metav1.DeleteOptions{}
		if err := metav1.Convert_url_Values_To_v1_DeleteOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
			return nil, err
		}
		return nil, kubes.Dynamic().
			Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).Delete(r.Context(), meta.Name, options)
	})
}

func (s *ClusterResourcesAPI) SubResource(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error) {
		prefix := "/" + path.Join("apis", meta.ResourcePath())
		return ClusterProxyHandle(w, r, kubes.GetKubernetesAddr(), prefix, prefix+"/"+meta.Subresource)
	})
}

// func (a *ClusterResourcesAPI) OnClusterInfo(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes, meta RequestMetadata) (any, error)) {
// 	base.On(w, r, func(ctx context.Context) (any, error) {
// 		ref, err := a.GetCluster(r)
// 		if err != nil {
// 			return nil, err
// 		}
// 		info, err := a.CloudInfo.Get(ctx, ref)
// 		if err != nil {
// 			return nil, err
// 		}
// 		kubes, err := info.KubernetesConfig()
// 		if err != nil {
// 			return nil, err
// 		}
// 		metadata := ResourceMetaFromRequest(r)
// 		return fn(r.Context(), kubes, metadata)
// 	})
// }

func ResourceMetaFromRequest(r *http.Request) RequestMetadata {
	vars := api.PathVars(r).Map()
	return RequestMetadata{
		GroupVersionResource: schema.GroupVersionResource{
			Group: func() string {
				if group := vars["group"]; group != "core" {
					return group
				}
				return ""
			}(),
			Version:  vars["version"],
			Resource: vars["resource"],
		},
		Namespace:   vars["namespace"],
		Kind:        vars["kind"],
		Name:        vars["name"],
		Request:     r,
		Subresource: vars["subresource"],
	}
}

type RequestMetadata struct {
	schema.GroupVersionResource
	Namespace   string
	Kind        string
	Name        string
	Request     *http.Request
	Subresource string
}

func (r RequestMetadata) ResourcePath() string {
	result := "/" + r.GroupVersion().String()
	if r.Namespace != "" {
		result += "/namespaces/" + r.Namespace
	}
	result += "/" + r.Resource
	if r.Name != "" {
		result += "/" + r.Name
	}
	return result
}

func (r RequestMetadata) RequestPath() string {
	result := r.ResourcePath()
	if r.Subresource != "" {
		result += "/" + r.Subresource
	}
	return result
}

func (s *ClusterResourcesAPI) AllResourcesGroup() api.Group {
	return api.
		NewGroup("/resources").
		SubGroup(
			s.ResourcesGroup(false),
			s.ResourcesGroup(true),
			s.ResourceExtensionGroup(),
		)
}

func (s *ClusterResourcesAPI) Group() api.Group {
	return api.
		NewGroup("/clusters/{cluster}").
		Tag("Cluster Resources").
		SubGroup(
			s.AllResourcesGroup(),
			s.ResourceMetadataOnlyGroup(),
			s.ProxyGroup(),
		)
}
