package flavor

import (
	"context"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	cloudv1 "xiaoshiai.cn/rune/pkg/apis/cloud/v1"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type Flavor struct {
	store.ObjectMeta `json:",inline"`
	Resources        []FlavorResource `json:"resources"`
	Config           FlavorConfig     `json:"config"`
	Enabled          bool             `json:"enabled"`
	Status           FlavorStatus     `json:"status"`
}

type FlavorStatus struct {
	Available bool `json:"available"`
}

type FlavorConfig map[string]string

type FlavorResource struct {
	Name         string              `json:"name"`
	ResourceName corev1.ResourceName `json:"resourceName"` // cpu, memory, gpu
	Limit        resource.Quantity   `json:"limit"`
	Request      resource.Quantity   `json:"request"`
	Labels       map[string]string   `json:"labels"` // node selector labels
}

func FlavorFrom(flavor *cloudv1.ResourceFlavor) *Flavor {
	resources := make([]FlavorResource, len(flavor.Spec.Resources))
	for i, res := range flavor.Spec.Resources {
		resources[i] = FlavorResource{
			Name:         base.GetMap(res.Annotations, "name", string(res.Name)),
			ResourceName: res.Name,
			Limit:        res.Limit,
			Request:      res.Request,
			Labels:       res.NodeSelector,
		}
	}
	return &Flavor{
		ObjectMeta: base.ObjectMetaFrom(flavor),
		Resources:  resources,
		Enabled:    base.GetMap(flavor.Labels, "enabled", "false") == "true",
		Config:     FlavorConfig(flavor.Annotations),
		Status:     FlavorStatus{Available: flavor.Status.Ready},
	}
}

func FlavorTo(flavor *Flavor) *cloudv1.ResourceFlavor {
	resources := make([]cloudv1.ResourceFlavorResource, len(flavor.Resources))
	for i, res := range flavor.Resources {
		resources[i] = cloudv1.ResourceFlavorResource{
			Name:         corev1.ResourceName(res.ResourceName),
			Request:      res.Request,
			Limit:        res.Limit,
			NodeSelector: res.Labels,
			Annotations:  map[string]string{"name": res.Name},
		}
	}
	resourceflavor := &cloudv1.ResourceFlavor{
		ObjectMeta: base.ObjectMetaTo(flavor),
		Spec: cloudv1.ResourceFlavorSpec{
			Tolerations: []corev1.Toleration{}, // TODO: handle tolerations
			Resources:   resources,
		},
		Status: cloudv1.ResourceFlavorStatus{
			Ready: flavor.Status.Available,
		},
	}
	if flavor.Enabled {
		if resourceflavor.Labels == nil {
			resourceflavor.Labels = make(map[string]string)
		}
		resourceflavor.Labels["enabled"] = "true"
	} else {
		if resourceflavor.Labels != nil {
			delete(resourceflavor.Labels, "enabled")
		}
	}
	return resourceflavor
}

type API struct {
	Store     store.Store
	CloudInfo cluster.CloudInfoGetter
}

func NewAPI(store store.Store, cloudInfo cluster.CloudInfoGetter) *API {
	return &API{Store: store, CloudInfo: cloudInfo}
}

func (a *API) ListFlavors(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, cli client.Client) (any, error) {
		flavors, err := a.listFlavors(r, cli, false)
		if err != nil {
			return nil, err
		}
		return api.PageObjectFromRequest(r, flavors), nil
	})
}

func (a *API) ListEnabledFlavors(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, cli client.Client) (any, error) {
		flavors, err := a.listFlavors(r, cli, true)
		if err != nil {
			return nil, err
		}
		return api.PageObjectFromRequest(r, flavors), nil
	})
}

func (a *API) listFlavors(r *http.Request, cli client.Client, enabledOnly bool) ([]Flavor, error) {
	flavorlist := cloudv1.ResourceFlavorList{}
	opts := []client.ListOption{}
	if enabledOnly {
		opts = append(opts, client.MatchingLabels{"enabled": "true"})
	}
	if err := cli.List(r.Context(), &flavorlist, opts...); err != nil {
		return nil, err
	}
	flavors := make([]Flavor, 0, len(flavorlist.Items))
	for _, flavor := range flavorlist.Items {
		flavors = append(flavors, *FlavorFrom(&flavor))
	}
	return flavors, nil
}

func (a *API) GetFlavor(w http.ResponseWriter, r *http.Request) {
	a.onFlavor(w, r, func(ctx context.Context, cli client.Client, flavor string) (any, error) {
		resourceflavor := &cloudv1.ResourceFlavor{}
		if err := cli.Get(ctx, client.ObjectKey{Name: flavor}, resourceflavor); err != nil {
			return nil, err
		}
		return FlavorFrom(resourceflavor), nil
	})
}

func (a *API) CreateFlavor(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, cli client.Client) (any, error) {
		flavor := &Flavor{}
		if err := api.Body(r, flavor); err != nil {
			return nil, err
		}
		if err := validateFlavor(flavor); err != nil {
			return nil, err
		}
		resourceFlavor := FlavorTo(flavor)
		if err := cli.Create(ctx, resourceFlavor); err != nil {
			return nil, err
		}
		return FlavorFrom(resourceFlavor), nil
	})
}

func validateFlavor(flavor *Flavor) error {
	if flavor.ID == "" {
		flavor.ID = base.RandName("flavor-")
	}
	if flavor.Name == "" {
		return errors.NewBadRequest("flavor name is required")
	}
	// Add additional validations as needed
	return nil
}

func (a *API) UpdateFlavor(w http.ResponseWriter, r *http.Request) {
	a.onFlavor(w, r, func(ctx context.Context, cli client.Client, flavor string) (any, error) {
		flavorObj := &Flavor{}
		if err := api.Body(r, flavorObj); err != nil {
			return nil, err
		}
		if flavorObj.ID != flavor {
			return nil, errors.NewBadRequest("flavor ID mismatch")
		}
		if err := validateFlavor(flavorObj); err != nil {
			return nil, err
		}
		resourceFlavor := FlavorTo(flavorObj)
		if err := cli.Update(ctx, resourceFlavor); err != nil {
			return nil, err
		}
		return FlavorFrom(resourceFlavor), nil
	})
}

func (a *API) DeleteFlavor(w http.ResponseWriter, r *http.Request) {
	a.onFlavor(w, r, func(ctx context.Context, cli client.Client, flavor string) (any, error) {
		resourceFlavor := &cloudv1.ResourceFlavor{}
		if err := cli.Get(ctx, client.ObjectKey{Name: flavor}, resourceFlavor); err != nil {
			return nil, err
		}
		if err := cli.Delete(ctx, resourceFlavor); err != nil {
			return nil, err
		}
		return resourceFlavor, nil
	})
}

func (a *API) onFlavor(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, cli client.Client, flavor string) (any, error)) {
	a.onCluster(w, r, func(ctx context.Context, cli client.Client) (any, error) {
		flavor := api.Path(r, "flavor", "")
		if flavor == "" {
			return nil, errors.NewBadRequest("flavor name is required")
		}
		return fn(ctx, cli, flavor)
	})
}

func (a *API) onCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, cli client.Client) (any, error)) {
	base.OnCluster(w, r, func(ctx context.Context, clustername string) (any, error) {
		cli, err := a.CloudInfo.GetKubernetes(ctx, store.ObjectReference{Name: clustername})
		if err != nil {
			return nil, err
		}
		return fn(ctx, cli.Client())
	})
}

func (a *API) AdminGroup() api.Group {
	return base.NewClusterGroup().SubGroup(
		api.NewGroup("/flavors").
			Route(
				api.GET("").
					Operation("list flavors").
					Param(api.PageParams...).
					To(a.ListFlavors).
					Response(store.List[Flavor]{}),

				api.GET("/{flavor}").
					Operation("get flavor").
					To(a.GetFlavor).
					Response(Flavor{}),

				api.POST("").
					Operation("create flavor").
					To(a.CreateFlavor).
					Param(api.BodyParam("flavor", Flavor{})).
					Response(Flavor{}),

				api.PUT("/{flavor}").
					Operation("update flavor").
					To(a.UpdateFlavor).
					Param(api.BodyParam("flavor", Flavor{})).
					Response(Flavor{}),

				api.DELETE("/{flavor}").
					Operation("delete flavor").
					To(a.DeleteFlavor),
			),
	)
}

func (a *API) TenantGroup() api.Group {
	return api.
		NewGroup("/tenants/{tenant}/clusters/{cluster}").
		SubGroup(
			api.NewGroup("/flavors").
				Route(
					api.GET("").
						Operation("list flavors").
						Param(api.PageParams...).
						To(a.ListEnabledFlavors).
						Response(store.List[Flavor]{}),
				),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Flavor").
		SubGroup(
			a.AdminGroup(),
			a.TenantGroup(),
		)
}
