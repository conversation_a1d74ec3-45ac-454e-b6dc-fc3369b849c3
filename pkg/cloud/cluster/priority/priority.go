package priority

import (
	"context"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	schedulingv1 "k8s.io/api/scheduling/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type Priority struct {
	store.ObjectMeta
	Weight           int32                    `json:"weight" validate:"lt=2000000000,gt=0"`
	PreemptionPolicy *corev1.PreemptionPolicy `json:"preemptionPolicy,omitempty" protobuf:"bytes,5,opt,name=preemptionPolicy"`
}

func FromPriorityClass(pc *schedulingv1.PriorityClass) Priority {
	return Priority{
		ObjectMeta: store.ObjectMeta{
			Name:              pc.Name,
			Description:       pc.Description,
			CreationTimestamp: pc.CreationTimestamp,
			DeletionTimestamp: pc.DeletionTimestamp,
		},
		Weight: pc.Value,
	}
}

func (p Priority) To() *schedulingv1.PriorityClass {
	return &schedulingv1.PriorityClass{
		ObjectMeta: metav1.ObjectMeta{
			Name:        p.Name,
			Annotations: p.Annotations,
			Labels:      p.Labels,
		},
		Value:            p.Weight,
		GlobalDefault:    false, // GlobalDefault is not supported in this implementation
		Description:      p.Description,
		PreemptionPolicy: p.PreemptionPolicy,
	}
}

func NewAPI(cloudInfo cluster.CloudInfoGetter) *PriorityAPI {
	return &PriorityAPI{
		OnClusterInfo: DefaultOnClusterInfoFunc(cloudInfo),
	}
}

func DefaultOnClusterInfoFunc(info cluster.CloudInfoGetter) OnClusterInfoFunc {
	return func(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes) (any, error)) {
		cluster.OnClusterInfo(w, r, info, fn)
	}
}

type PriorityAPI struct {
	OnClusterInfo OnClusterInfoFunc
}

type OnClusterInfoFunc func(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes) (any, error))

func (a *PriorityAPI) ListPriorityClasses(w http.ResponseWriter, r *http.Request) {
	a.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes) (any, error) {
		list, err := kubes.Kubernetes().SchedulingV1().PriorityClasses().List(ctx, metav1.ListOptions{})
		if err != nil {
			return nil, err
		}
		priorities := make([]Priority, 0, len(list.Items))
		for _, pc := range list.Items {
			priorities = append(priorities, FromPriorityClass(&pc))
		}
		return api.PageObjectFromRequest(r, priorities), nil
	})
}

func (a *PriorityAPI) GetPriority(w http.ResponseWriter, r *http.Request) {
	a.onPriority(w, r, func(ctx context.Context, kubes cluster.Kubernetes, name string) (any, error) {
		pc := &schedulingv1.PriorityClass{}
		if err := kubes.Client().Get(ctx, client.ObjectKey{}, pc); err != nil {
			return nil, err
		}
		return FromPriorityClass(pc), nil
	})
}

func (a *PriorityAPI) CreatePriority(w http.ResponseWriter, r *http.Request) {
	a.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes) (any, error) {
		priority := Priority{}
		if err := api.Body(r, &priority); err != nil {
			return nil, err
		}
		pc := priority.To()
		if err := kubes.Client().Create(ctx, pc); err != nil {
			return nil, err
		}
		return FromPriorityClass(pc), nil
	})
}

func (a *PriorityAPI) UpdatePriority(w http.ResponseWriter, r *http.Request) {
	a.onPriority(w, r, func(ctx context.Context, kubes cluster.Kubernetes, name string) (any, error) {
		priority := Priority{}
		if err := api.Body(r, &priority); err != nil {
			return nil, err
		}
		pc := priority.To()
		pc.Name = name // Ensure the name is set correctly
		if err := kubes.Client().Update(ctx, pc); err != nil {
			return nil, err
		}
		return FromPriorityClass(pc), nil
	})
}

func (a *PriorityAPI) DeletePriority(w http.ResponseWriter, r *http.Request) {
	a.onPriority(w, r, func(ctx context.Context, kubes cluster.Kubernetes, name string) (any, error) {
		pc := &schedulingv1.PriorityClass{ObjectMeta: metav1.ObjectMeta{Name: name}}
		if err := kubes.Client().Delete(ctx, pc); err != nil {
			return nil, err
		}
		return FromPriorityClass(pc), nil
	})
}

func (a *PriorityAPI) onPriority(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes, name string) (any, error)) {
	a.OnClusterInfo(w, r, func(ctx context.Context, kubes cluster.Kubernetes) (any, error) {
		name := r.URL.Query().Get("priority")
		if name == "" {
			return nil, errors.NewBadRequest("priority name is required")
		}
		return fn(ctx, kubes, name)
	})
}

func (a *PriorityAPI) Group() api.Group {
	return base.NewClusterGroup().
		SubGroup(
			api.NewGroup("/priority").
				Route(
					api.GET("").
						Operation("list priority").
						Param(api.PageParams...,
						).
						Response(store.List[Priority]{}),

					api.GET("/{priority}").
						Operation("get priority").
						Response(Priority{}),

					api.POST("").
						Operation("create priority").
						Param(
							api.BodyParam("priority", Priority{}),
						).
						Response(Priority{}),

					api.PUT("/{priority}").
						Operation("update priority").
						Param(
							api.BodyParam("priority", Priority{}),
						).
						Response(Priority{}),

					api.DELETE("/{priority}").
						Operation("delete priority").
						Response(Priority{}),
				),
		)
}
