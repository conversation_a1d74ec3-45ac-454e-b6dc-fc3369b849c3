package cluster

import (
	"context"
	"fmt"
	"hash/fnv"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	ctrlcluster "sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
	apiserverscheme "xiaoshiai.cn/rune/pkg/cloud/kube"
)

type CloudInfoGetter interface {
	GetKubernetes(ctx context.Context, ref store.ObjectReference) (Kubernetes, error)
}

type CloudInfoHolder interface {
	CloudInfoGetter
	Sync(ctx context.Context, cluster *Cluster) (Kubernetes, error)
	Remove(ctx context.Context, ref store.ObjectReference) error
}

type Kubernetes interface {
	RestConfig() *rest.Config

	Client() client.WithWatch
	Kubernetes() kubernetes.Interface
	Dynamic() dynamic.Interface
	Transport() http.RoundTripper

	GetServiceConfig(svc string) (*httpclient.ClientConfig, error)
	GetServiceProxyConfig(svc string, websocket bool) (*httpclient.ClientConfig, error)
	GetServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig
	GetKubernetesAddr() *httpclient.ClientConfig
}

var _ CloudInfoHolder = &defaultCloudInfoHolder{}

func NewDefaultCloudInfoHolder() *defaultCloudInfoHolder {
	return &defaultCloudInfoHolder{
		infos: make(map[string]*defaultCloudInfo),
	}
}

type defaultCloudInfoHolder struct {
	mu    sync.RWMutex
	infos map[string]*defaultCloudInfo
}

// Get implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) GetKubernetes(ctx context.Context, ref store.ObjectReference) (Kubernetes, error) {
	if ref.Name == "" {
		return nil, errors.NewInvalid("cluster reference", ref.String(), fmt.Errorf(".name is required"))
	}
	d.mu.RLock()
	defer d.mu.RUnlock()
	info, ok := d.infos[ref.String()]
	if !ok {
		return nil, errors.NewNotFound("cluster info", ref.String())
	}
	if !info.ready {
		return nil, errors.NewInvalid("cloud info", ref.Name, fmt.Errorf("cloud info is not ready"))
	}
	return info.kubernetes, nil
}

// Sync implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Sync(ctx context.Context, cluster *Cluster) (Kubernetes, error) {
	hash := computeHash(cluster.Kube, cluster.Params)
	ref := store.ObjectReferenceFrom(cluster)

	d.mu.RLock()
	existsinfo, ok := d.infos[ref.String()]
	d.mu.RUnlock()
	if ok && existsinfo.confighash == hash {
		return existsinfo.kubernetes, nil
	}
	// cancel the old info
	if existsinfo != nil {
		existsinfo.Close()
	}
	d.mu.Lock()
	defer d.mu.Unlock()
	// update the info
	infoctx, cancel := context.WithCancel(ctx)

	newinfo := &defaultCloudInfo{
		confighash: hash,
		cancel:     cancel,
		ready:      false,
	}
	d.infos[ref.String()] = newinfo

	restconfig, err := d.GetRestConfig(cluster)
	if err != nil {
		return nil, err
	}
	newkubeclients, err := NewKubernetesClients(infoctx, restconfig)
	if err != nil {
		return nil, err
	}
	newkubeclients.kubeCluster = cluster.Kube
	newinfo.kubernetes = newkubeclients

	newinfo.ready = true

	return newinfo.kubernetes, nil
}

func (d *defaultCloudInfoHolder) GetCloudConfig(cluster *Cluster, kubeclients *KubernetesClients) (*httpclient.ClientConfig, error) {
	if kube := cluster.Kube; kube.Config != "" {
		clicoonfig := kubeclients.GetServiceProxyAddr(def(kube.Namespace, "ismc"), def(kube.Service, "ismc-agent"), def(kube.Port, 80))
		return clicoonfig, nil
	}
	return nil, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("cloud config is not supported"))
}

func (d *defaultCloudInfoHolder) GetRestConfig(cluster *Cluster) (*rest.Config, error) {
	if cluster.Kube.Config != "" {
		kubeconfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(cluster.Kube.Config))
		if err != nil {
			return nil, err
		}
		return kubeconfig, nil
	}
	return nil, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("rest config is not set"))
}

// Remove implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Remove(ctx context.Context, ref store.ObjectReference) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	key := ref.String()
	info, ok := d.infos[key]
	if !ok {
		return nil
	}
	info.Close()
	delete(d.infos, key)
	return nil
}

type defaultCloudInfo struct {
	kubernetes *KubernetesClients
	ready      bool
	cancel     context.CancelFunc
	confighash uint32
}

func (d *defaultCloudInfo) Close() {
	if d.cancel != nil {
		d.cancel()
	}
	if d.kubernetes != nil {
		d.kubernetes.Close()
	}
}

func computeHash(values ...any) uint32 {
	hasher := fnv.New32a()
	hasher.Reset()
	fmt.Fprintf(hasher, "%#v", values...)
	return hasher.Sum32()
}

var _ Kubernetes = &KubernetesClients{}

type KubernetesClients struct {
	restConfig    *rest.Config
	cancel        context.CancelFunc
	client        client.WithWatch
	cached        client.Client
	kubernetes    kubernetes.Interface
	dynamic       dynamic.Interface
	baseURL       url.URL
	transport     http.RoundTripper
	kubeCluster   KubeCluster
	proxyServices map[string]ServiceProxyTarget
}

// Client implements Kubernetes.
func (c *KubernetesClients) Client() client.WithWatch {
	return c.client
}

// Dynamic implements Kubernetes.
func (c *KubernetesClients) Dynamic() dynamic.Interface {
	return c.dynamic
}

// Kubernetes implements Kubernetes.
func (c *KubernetesClients) Kubernetes() kubernetes.Interface {
	return c.kubernetes
}

// RestConfig implements Kubernetes.
func (c *KubernetesClients) RestConfig() *rest.Config {
	return c.restConfig
}

func (c *KubernetesClients) Transport() http.RoundTripper {
	return c.transport
}

type ServiceProxyTarget struct {
	Namespace  string
	Service    string
	Port       int
	PathPrefix string
}

func NewKubernetesClients(ctx context.Context, restconfig *rest.Config) (*KubernetesClients, error) {
	restconfig = rest.CopyConfig(restconfig)
	// set default QPS
	restconfig.QPS = 1024
	restconfig.Burst = 4096

	tranport := restconfig.Transport
	if tranport == nil {
		newtranport, err := rest.TransportFor(restconfig)
		if err != nil {
			return nil, err
		}
		tranport = newtranport
	}
	httpcli := &http.Client{
		Transport: tranport,
		Timeout:   restconfig.Timeout,
	}
	baseURL, _, err := rest.DefaultServerUrlFor(restconfig)
	if err != nil {
		return nil, err
	}
	dynamiccli, err := dynamic.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	kubernetescs, err := kubernetes.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	ctrlc, err := ctrlcluster.New(restconfig, func(o *ctrlcluster.Options) {
		o.Scheme = apiserverscheme.Scheme
		o.HTTPClient = httpcli
		o.Client.Cache = &client.CacheOptions{Unstructured: true}
	})
	if err != nil {
		return nil, err
	}

	ctrlclient, err := client.NewWithWatch(restconfig, client.Options{
		HTTPClient: httpcli, Scheme: apiserverscheme.Scheme,
	})
	if err != nil {
		return nil, err
	}
	cli := VersionedClient{WithWatch: ctrlclient}

	ctx, cancel := context.WithCancel(ctx)
	go ctrlc.Start(ctx)

	newclients := &KubernetesClients{
		cancel:        cancel,
		client:        cli,
		cached:        ctrlc.GetClient(),
		kubernetes:    kubernetescs,
		dynamic:       dynamiccli,
		restConfig:    restconfig,
		baseURL:       *baseURL,
		transport:     tranport,
		proxyServices: map[string]ServiceProxyTarget{},
	}
	return newclients, nil
}

func (c *KubernetesClients) Close() {
	if c.cancel != nil {
		c.cancel()
	}
}

func (c *KubernetesClients) GetServiceConfig(svc string) (*httpclient.ClientConfig, error) {
	return c.GetServiceProxyConfig(svc, false)
}

func (c *KubernetesClients) GetServiceProxyConfig(svc string, websocket bool) (*httpclient.ClientConfig, error) {
	dest, ok := c.proxyServices[svc]
	if !ok {
		return nil, errors.NewUnsupported(fmt.Sprintf("service %s not defined", svc))
	}
	target := ptr.To(c.baseURL)
	// apiserver proxy not support websocket query
	// we proxy to agent service for websocket
	if websocket {
		target := ptr.To(c.baseURL)
		target.Path = fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/proxy/%s.%s:%d",
			def(c.kubeCluster.Namespace, "rune"), def(c.kubeCluster.Service, "rune-agent"), def(c.kubeCluster.Port, 80),
			dest.Service, dest.Namespace, dest.Port)
		return &httpclient.ClientConfig{Server: target, RoundTripper: c.transport, DialContext: c.restConfig.Dial}, nil
	} else {
		target.Path += fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/", dest.Namespace, dest.Service, dest.Port)
	}
	return &httpclient.ClientConfig{Server: target, RoundTripper: c.transport, DialContext: c.restConfig.Dial}, nil
}

func (c *KubernetesClients) GetServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	target := ptr.To(c.baseURL)
	target.Path += fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/", namespace, name, port)
	return &httpclient.ClientConfig{Server: target, RoundTripper: c.transport, DialContext: c.restConfig.Dial}
}

func (c *KubernetesClients) GetWebsocketServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	if kube := c.kubeCluster; kube.Config != "" {
		target := ptr.To(c.baseURL)
		target.Path = fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/proxy/%s.%s:%d",
			def(kube.Namespace, "rune"), def(kube.Service, "rune-agent"), def(kube.Port, 80),
			name, namespace, port)
		return &httpclient.ClientConfig{Server: target, RoundTripper: c.transport, DialContext: c.restConfig.Dial}
	}
	return c.GetServiceProxyAddr(namespace, name, port)
}

func def[T comparable](v, def T) T {
	var zero T
	if v == zero {
		return def
	}
	return v
}

func (c *KubernetesClients) GetKubernetesAddr() *httpclient.ClientConfig {
	return &httpclient.ClientConfig{
		Server:       &c.baseURL,
		RoundTripper: c.transport,
		DialContext:  c.restConfig.Dial,
	}
}

var _ client.Client = &VersionedClient{}

// VersionedClient is a client.Client that sets the GroupVersionKind on objects it reads.
// When use an no cache client, th returned object will not have the GroupVersionKind set.
// it cause by the client.Client use [serializer.WithoutConversionCodecFactory] as default codec factory.
type VersionedClient struct {
	client.WithWatch
}

// Get implements client.Client.
func (v VersionedClient) Get(ctx context.Context, key types.NamespacedName, obj client.Object, opts ...client.GetOption) error {
	defer v.setGroupVersionKind(obj)
	return v.WithWatch.Get(ctx, key, obj, opts...)
}

// List implements client.Client.
func (v VersionedClient) List(ctx context.Context, list client.ObjectList, opts ...client.ListOption) error {
	defer v.setListGroupVersionKind(list)
	return v.WithWatch.List(ctx, list, opts...)
}

func (v VersionedClient) setGroupVersionKind(obj client.Object) {
	gvk, err := apiutil.GVKForObject(obj, v.WithWatch.Scheme())
	if err != nil || gvk.Kind == "" {
		return
	}
	obj.GetObjectKind().SetGroupVersionKind(gvk)
}

func (v VersionedClient) setListGroupVersionKind(list client.ObjectList) {
	listgvk, err := apiutil.GVKForObject(list, v.WithWatch.Scheme())
	if err != nil || listgvk.Kind == "" {
		return
	}
	list.GetObjectKind().SetGroupVersionKind(listgvk)

	gvk := listgvk
	gvk.Kind = strings.TrimSuffix(gvk.Kind, "List")
	meta.EachListItem(list, func(obj runtime.Object) error {
		obj.GetObjectKind().SetGroupVersionKind(gvk)
		return nil
	})
}
