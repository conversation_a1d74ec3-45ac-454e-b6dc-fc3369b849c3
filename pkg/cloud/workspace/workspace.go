package workspace

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resources"
)

type Workspace struct {
	store.ObjectMeta `json:",inline"`
	Cluster          ClusterNamespaceReference `json:"cluster"`
}

type ClusterNamespaceReference struct {
	store.ObjectReference `json:",inline"`
	Namespace             string `json:"namespace"`
}

func GetWorkspaceCluster(ctx context.Context, infos cluster.CloudInfoGetter, storage store.Store, tenant, workspacename string) (cluster.Kubernetes, string, error) {
	workspaceobj := &Workspace{}
	if err := storage.Scope(base.ScopeTenant(tenant)).Get(ctx, workspacename, workspaceobj); err != nil {
		return nil, "", err
	}
	kubes, err := infos.GetKubernetes(ctx, workspaceobj.Cluster.ObjectReference)
	if err != nil {
		return nil, "", err
	}
	return kubes, workspaceobj.Cluster.Namespace, nil
}

func NewAPI(storage store.Store, infos cluster.CloudInfoGetter) *API {
	c := WorkspaceClusterInfoConvert{
		Infos:   infos,
		Storage: storage,
	}
	return &API{
		Storage:   storage,
		Infos:     infos,
		Resources: resources.NewClusterResourcesAPIWithGetCluster(c.OnClusterInfo),
	}
}

type WorkspaceClusterInfoConvert struct {
	Infos   cluster.CloudInfoGetter
	Storage store.Store
}

func (c WorkspaceClusterInfoConvert) OnClusterInfo(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, kubes cluster.Kubernetes, meta resources.RequestMetadata) (any, error)) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		kubes, namespace, err := GetWorkspaceCluster(ctx, c.Infos, c.Storage, tenant, workspace)
		if err != nil {
			return nil, err
		}
		meta := resources.ResourceMetaFromRequest(r)
		meta.Namespace = namespace
		return fn(ctx, kubes, meta)
	})
}

type API struct {
	Storage   store.Store
	Infos     cluster.CloudInfoGetter
	Resources *resources.ClusterResourcesAPI
}

func (a *API) ListWorkspaces(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		list := &store.List[Workspace]{}
		return base.GenericList(r, a.Storage.Scope(base.ScopeTenant(tenant)), list)
	})
}

func (a *API) GetWorkspace(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		workspaceobj := &Workspace{}
		if err := a.Storage.Scope(base.ScopeTenant(tenant)).Get(ctx, workspace, workspaceobj); err != nil {
			return nil, err
		}
		return workspaceobj, nil
	})
}

func (a *API) CreateWorkspace(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		workspaceobj := &Workspace{}
		if err := api.Body(r, workspaceobj); err != nil {
			return nil, err
		}
		if err := validateWorkspace(workspaceobj); err != nil {
			return nil, err
		}
		if err := a.Storage.Scope(base.ScopeTenant(tenant)).Create(ctx, workspaceobj); err != nil {
			return nil, err
		}
		return workspaceobj, nil
	})
}

func validateWorkspace(workspace *Workspace) error {
	if workspace.Cluster.ObjectReference.Name == "" {
		return errors.NewBadRequest("cluster name is required")
	}
	if workspace.Cluster.Namespace == "" {
		return errors.NewBadRequest("cluster namespace is required")
	}
	if workspace.Name == "" {
		return errors.NewBadRequest("workspace name is required")
	}
	return nil
}

func (a *API) UpdateWorkspace(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		workspaceobj := &Workspace{}
		if err := api.Body(r, workspaceobj); err != nil {
			return nil, err
		}
		if err := validateWorkspace(workspaceobj); err != nil {
			return nil, err
		}
		if err := a.Storage.Scope(base.ScopeTenant(tenant)).Update(ctx, workspaceobj); err != nil {
			return nil, err
		}
		return workspaceobj, nil
	})
}

func (a *API) DeleteWorkspace(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		obj := &Workspace{ObjectMeta: store.ObjectMeta{Name: workspace}}
		if err := a.Storage.Scope(base.ScopeTenant(tenant)).Delete(ctx, obj); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) WorkspaceGroup() api.Group {
	return base.NewWorkspaceGroup().
		Route(
			api.GET("").
				Operation("list workspaces").
				To(a.ListWorkspaces).
				Param(api.PageParams...).
				Response(store.List[Workspace]{}),

			api.POST("").
				Operation("create workspace").
				To(a.CreateWorkspace).
				Param(
					api.BodyParam("workspace", Workspace{}),
				).
				Response(Workspace{}),

			api.GET("/{workspace}").
				Operation("get workspace").
				To(a.GetWorkspace).
				Response(Workspace{}),

			api.PUT("/{workspace}").
				Operation("update workspace").
				To(a.UpdateWorkspace).
				Param(
					api.BodyParam("workspace", Workspace{}),
				).
				Response(Workspace{}),

			api.DELETE("/{workspace}").
				Operation("delete workspace").
				To(a.DeleteWorkspace),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Workspace").
		SubGroup(
			a.WorkspaceGroup(),
			base.NewWorkspaceGroup().
				SubGroup(
					a.ResourcesGroup(),
				),
		)
}
