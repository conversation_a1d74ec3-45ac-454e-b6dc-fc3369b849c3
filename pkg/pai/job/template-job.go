package job

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"sigs.k8s.io/yaml"
	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/openapi"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
	"xiaoshiai.cn/rune/pkg/pai/template"
)

const (
	LabelJobTemplateName        = "pai." + common.GroupPrefix + "/job-template-name"
	AnnotationJobTemplateValues = "pai." + common.GroupPrefix + "/job-template-values"
)

func (a *API) CreateTemplateJob(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		obj := &TemplateJob{}
		if err := api.Body(r, obj); err != nil {
			return nil, fmt.Errorf("parse request body error: %w", err)
		}
		obj.Tenant = tenant
		obj.Workspace = workspace
		return a.ApplyTemplateJob(ctx, obj)
	})
}

type TemplateJob struct {
	store.ObjectMeta `json:",inline"`
	Creator          string         `json:"creator"`
	Kind             string         `json:"kind"`
	Tenant           string         `json:"tenant"`
	Workspace        string         `json:"workspace"`
	TemplateName     string         `json:"templateName"`
	Values           map[string]any `json:"values"`
	Status           JobStatus      `json:"status"`
}

type TemplateJobValues struct {
	Release  TemplateRelease
	Template template.JobTemplate
	Values   map[string]any
	From     *Job
}

type TemplateRelease struct {
	Name        string
	Alias       string
	DisplayName string
	Tenant      string
	Kind        string
	Workspace   string
	Namespace   string // Namespace is the namespace where the job will be created
	Creator     string
}

func (a *API) ApplyTemplateJob(ctx context.Context, obj *TemplateJob) (*Job, error) {
	cli, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudClient, a.Store, obj.Tenant, obj.Workspace)
	if err != nil {
		return nil, fmt.Errorf("get workspace cluster error: %w", err)
	}
	templatekind, templatename := obj.Kind, obj.TemplateName
	template, err := a.getJobTemplate(ctx, templatekind, templatename)
	if err != nil {
		return nil, err
	}
	// render a job from template
	values := obj.Values
	if values == nil {
		values = make(map[string]any)
	}
	renderValues := TemplateJobValues{
		Release: TemplateRelease{
			Name:        obj.Name,
			DisplayName: obj.Alias,
			Alias:       obj.Alias,
			Tenant:      obj.Tenant,
			Kind:        templatekind,
			Workspace:   obj.Workspace,
			Namespace:   namespace,
		},
		Template: *template,
		Values:   values,
	}
	// validate values
	if err := ValidateSchema(ctx, template.Schema, values); err != nil {
		return nil, fmt.Errorf("validate values error: %w", err)
	}
	job, err := TemplateJobValuesToJob(ctx, template.Template, renderValues)
	if err != nil {
		return nil, fmt.Errorf("render job from template %s error: %w", template.Name, err)
	}
	if err := ApplyJob(ctx, cli.Client(), namespace, job); err != nil {
		return nil, fmt.Errorf("apply job %s error: %w", job.Name, err)
	}
	return job, nil
}

func ValidateSchema(ctx context.Context, schema openapi.Schema, values any) error {
	return openapi.ValidateSchema(&schema, values)
}

func TemplateJobValuesToJob(ctx context.Context, templatedata string, values TemplateJobValues) (*Job, error) {
	if templatedata == "" {
		return nil, errors.New("template is empty")
	}
	originalValues := template.DeepCopyMap(values.Values)

	// use online template
	rendered, err := template.GoTemplate(ctx, templatedata, values)
	if err != nil {
		return nil, err
	}
	job := &Job{}
	if err := yaml.Unmarshal([]byte(rendered), job); err != nil {
		return nil, err
	}
	// correct some fields
	job.Name = values.Release.Name
	if job.Alias == "" {
		job.Alias = values.Release.DisplayName
	}
	job.Tenant, job.Workspace = values.Release.Tenant, values.Release.Workspace
	job.Kind = values.Release.Kind
	if job.Labels == nil {
		job.Labels = map[string]string{}
	}
	job.Labels[LabelJobTemplateName] = values.Template.Name
	if job.Annotations == nil {
		job.Annotations = map[string]string{}
	}
	job.Annotations[pai.AnnotationJobTemplateValues] = tojson(originalValues)
	job.Creator = values.Release.Creator
	return job, nil
}

func tojson(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		return err.Error()
	}
	return string(data)
}

func (a *API) getJobTemplate(ctx context.Context, kind, name string) (*template.JobTemplate, error) {
	template := &template.JobTemplate{}
	if err := a.Store.Get(ctx, name, template, store.WithGetFieldRequirements(store.RequirementEqual("kind", kind))); err != nil {
		return nil, fmt.Errorf("get job template %s error: %w", name, err)
	}
	return template, nil
}
