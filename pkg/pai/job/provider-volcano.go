package job

import (
	"context"

	"sigs.k8s.io/controller-runtime/pkg/client"
	batchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
)

type VolcanoJobProvider struct{}

// PauseJob implements JobProvider.
func (v *VolcanoJobProvider) PauseJob(ctx context.Context, client client.Client, ns string, job *Job) error {
	panic("unimplemented")
}

// ResumeJob implements JobProvider.
func (v *VolcanoJobProvider) ResumeJob(ctx context.Context, client client.Client, ns string, job *Job) error {
	panic("unimplemented")
}

// Check<PERSON>ob implements JobProvider.
func (v *VolcanoJobProvider) CheckJob(ctx context.Context, client client.Client, ns string, job *Job) (string, *JobProviderStatus, error) {
	volcanoJob := &batchv1alpha1.Job{}
	_ = volcanoJob
	panic("unimplemented")
}

// RemoveJob implements JobProvider.
func (v *VolcanoJobProvider) RemoveJob(ctx context.Context, client client.Client, ns string, job *Job) error {
	panic("unimplemented")
}

// S<PERSON><PERSON><PERSON> implements JobProvider.
func (v *VolcanoJobProvider) SyncJob(ctx context.Context, client client.Client, ns string, job *Job) error {
	panic("unimplemented")
}

var _ JobProvider = &VolcanoJobProvider{}
