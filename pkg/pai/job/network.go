package job

import (
	"context"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

type NetworkProviderStatus struct {
	Message   string
	RolePorts map[string][]PortStatus
}

type NetworkProvider interface {
	SyncJobNetwork(ctx context.Context, client client.Client, namespace string, job *Job) (*NetworkProviderStatus, error)
	RemoveJobNetwork(ctx context.Context, client client.Client, namespace string, job *Job) error
}
