package job

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type JobProvider interface {
	// ExistsJob returns true if the job uuid if exists
	<PERSON><PERSON><PERSON>(ctx context.Context, client client.Client, ns string, job *Job) (string, *JobProviderStatus, error)
	SyncJob(ctx context.Context, client client.Client, ns string, job *Job) error
	Pause<PERSON>ob(ctx context.Context, client client.Client, ns string, job *Job) error
	ResumeJob(ctx context.Context, client client.Client, ns string, job *Job) error
	RemoveJob(ctx context.Context, client client.Client, ns string, job *Job) error
}

type JobProviderStatus struct {
	FinishTimestamp  *metav1.Time
	RunningTimestamp *metav1.Time
	StartTimestamp   *metav1.Time
	Phase            JobPhase
	Message          string
	Roles            map[string]JobRoleStatus
}
