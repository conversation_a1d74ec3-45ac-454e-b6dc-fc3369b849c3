package job

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	cloudclient "xiaoshiai.cn/rune/pkg/cloud/client"
)

type API struct {
	Store       store.Store
	CloudClient cloudclient.CloudClient
}

func NewAPI(store store.Store) *API {
	return &API{Store: store}
}

func (a *API) ListJob(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := store.List[Job]{}
		return base.GenericList(r, storage, &list)
	})
}

func (a *API) GetJob(w http.ResponseWriter, r *http.Request) {
	a.onJob(w, r, func(ctx context.Context, storage store.Store, job string) (any, error) {
		return base.GenericGet(r, storage, &Job{}, job)
	})
}

func (a *API) CreateJob(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		job := &Job{}
		if err := api.Body(r, job); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateJob(job); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, storage, job)
	})
}

func validateJob(job *Job) error {
	if job.Name == "" {
		return errors.NewBadRequest("job name is required")
	}
	// Add more validations as needed for Job struct
	return nil
}

func (a *API) UpdateJob(w http.ResponseWriter, r *http.Request) {
	a.onJob(w, r, func(ctx context.Context, storage store.Store, job string) (any, error) {
		jobObj := &Job{}
		if err := api.Body(r, jobObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateJob(jobObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, storage, jobObj, job)
	})
}

func (a *API) DeleteJob(w http.ResponseWriter, r *http.Request) {
	a.onJob(w, r, func(ctx context.Context, storage store.Store, job string) (any, error) {
		return base.GenericDelete(r, storage, &Job{}, job)
	})
}

func (a *API) onTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		return fn(ctx, a.Store.Scope(base.ScopeTenant(tenant)))
	})
}

func (a *API) onJob(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, job string) (any, error)) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		job := api.Path(r, "job", "")
		if job == "" {
			return nil, errors.NewBadRequest("job name is required")
		}
		return fn(ctx, storage, job)
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Job").
		SubGroup(
			base.NewTenantGroup("/jobs").
				Route(
					api.GET("").
						Operation("list jobs").
						Param(api.PageParams...).
						To(a.ListJob).
						Response(store.List[Job]{}),

					api.GET("/{job}").
						Operation("get job").
						To(a.GetJob).
						Response(Job{}),

					api.POST("").
						Operation("create job").
						To(a.CreateJob).
						Param(api.BodyParam("job", Job{})).
						Response(Job{}),

					api.PUT("/{job}").
						Operation("update job").
						To(a.UpdateJob).
						Param(api.BodyParam("job", Job{})).
						Response(Job{}),

					api.DELETE("/{job}").
						Operation("delete job").
						To(a.DeleteJob),
				),
		)
}
