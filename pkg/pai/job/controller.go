package job

import (
	"context"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	cloudclient "xiaoshiai.cn/rune/pkg/cloud/client"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/kube"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

const (
	JobFinalizer = "job." + common.GroupPrefix + "/finalizer"
)

func NewJobController(ctx context.Context, storage store.Store, cloudcli cloudclient.CloudClient) (*controller.Controller, error) {
	dynSource := cloudclient.NewDynamicClusterResourceSource(ctx, cloudcli, kube.Scheme, DynamicResourceKeyFunc)

	rec := &JobReconciler{
		Client: storage,
		Holder: cloudcli,
	}
	better := controller.NewBetterReconciler(rec,
		storage,
		controller.WithFinalizer(JobFinalizer),
		controller.WithAutosetStatus())
	c := controller.
		NewController("jobs", better).
		Watch(controller.NewStoreSource(storage, &Job{})).
		Watch(dynSource)
	return c, nil
}

func DynamicResourceKeyFunc(cluster store.ObjectReference, obj client.Object) []controller.ScopedKey {
	switch typed := obj.(type) {
	case *corev1.Pod:
		annotation := typed.GetAnnotations()
		if annotation == nil {
			return nil
		}
	}
	return nil
}

type JobReconciler struct {
	Client          store.Store
	Holder          cluster.CloudInfoGetter
	JobProvider     JobProvider
	NetworkProvider NetworkProvider
}

func (j *JobReconciler) Sync(ctx context.Context, job *Job) (controller.Result, error) {
	log := log.FromContext(ctx)
	log.Info("sync job", "job", job.Name)
	if err := j.sync(ctx, job); err != nil {
		log.Error(err, "failed to sync job", "job", job.Name)
		return controller.Result{}, err
	}
	log.Info("job synced successfully", "job", job.Name)
	return controller.Result{}, nil
}

func (j *JobReconciler) sync(ctx context.Context, job *Job) error {
	log := log.FromContext(ctx)
	cli, namespace, err := j.workspaceClient(ctx, job.Tenant, job.Workspace)
	if err != nil {
		log.Error(err, "get workspace client", "tenant", job.Tenant, "workspace", job.Workspace)
		return err
	}

	// if updated, err := j.setReplicasAdjust(ctx, job); err != nil {
	// 	return err
	// } else if updated {
	// 	// update replicas and wait for next reconcile
	// 	return j.Client.Update(ctx, job)
	// }
	if job.Paused {
		log.Info("job is paused, check job", "job", job.Name)
		return j.pause(ctx, cli, namespace, job)
	}
	// from paused to running
	if !job.Paused {
		log.Info("job is unpaused, start job", "job", job.Name)
		return j.start(ctx, cli, namespace, job)
	}
	// check exists job status
	jobuid, jobstatus, err := j.JobProvider.CheckJob(ctx, cli, namespace, job)
	if err != nil {
		return err
	}
	networkStatus, err := j.NetworkProvider.SyncJobNetwork(ctx, cli, namespace, job)
	if err != nil {
		return err
	}
	j.networkStatus(ctx, job, networkStatus)
	// create a new job
	if jobuid == "" {
		log.Info("job not exists, start job", "job", job.Name)
		return j.start(ctx, cli, namespace, job)
	}
	log.Info("job exists, sync job", "job", job.Name)
	// sync job and get status
	if err := j.JobProvider.SyncJob(ctx, cli, namespace, job); err != nil {
		return err
	}
	// job already reached a result
	if jobstatus.FinishTimestamp != nil {
		// we got an outdated job status from cache
		if job.Status.State.StartTimestamp != nil && jobstatus.FinishTimestamp.Before(job.Status.State.StartTimestamp) {
			log.Error(nil, "job status is outdated, ignore", "job", job.Name)
			return nil
		}
		return j.finished(ctx, cli, namespace, job, jobstatus)
	}
	// update status
	if err := j.jobStatus(ctx, job, jobstatus); err != nil {
		return err
	}
	return nil
}

func (r *JobReconciler) jobStatus(_ context.Context, job *Job, jobstatus *JobProviderStatus) error {
	// update status
	job.Status.State.Phase = jobstatus.Phase
	if job.Status.State.StartTimestamp == nil {
		job.Status.State.StartTimestamp = jobstatus.StartTimestamp
	}
	job.Status.State.RunningTimestamp = jobstatus.RunningTimestamp
	job.Status.State.FinishTimestamp = jobstatus.FinishTimestamp
	job.Status.State.Reason = jobstatus.Message
	job.Status.Message = jobstatus.Message
	// update role status
	updateRoleStatusFunc(job, func(role JobRole, stats *JobRoleStatus) {
		newstatus := jobstatus.Roles[stats.Name]
		stats.Resources = newstatus.Resources
		// stats.Replicas.Desired = newstatus.DesiredReplicas
		// stats.Replicas.Current = newstatus.CurrentReplicas
		// stats.Replicas.Running = newstatus.RunningReplicas
	})
	// hpa replicas status
	// hpaRole := getHPARole(job)
	// job.Status.Replicas = jobstatus.Roles[hpaRole.Name].CurrentReplicas
	// job.Status.Selector = labels.SelectorFromValidatedSet(JobRolePodLabel(job, *hpaRole)).String()
	return nil
}

func (r *JobReconciler) start(ctx context.Context, cli client.Client, ns string, job *Job) error {
	// reset status
	if isFinalPhase(job.Status.State.Phase) {
		// r.recordHistory(ctx, job)
		job.Status.Message = ""
		job.Status.State = JobStatusState{
			Phase:          JobPhasePending,
			StartTimestamp: &metav1.Time{Time: time.Now()},
		}
	}
	// // check sku limit
	// if err := r.checkSKULimit(ctx, job); err != nil {
	// 	job.Status.State.Phase = JobPhaseFailed
	// 	return fmt.Errorf("check sku limit: %w", err)
	// }
	// job image pull secret
	// if err := r.syncJobPullSecret(ctx, job); err != nil {
	// 	return err
	// }
	// if err := r.syncVisual(ctx, job); err != nil {
	// 	return err
	// }
	// if err := r.labelStorageset(ctx, job); err != nil {
	// 	return err
	// }
	if err := r.JobProvider.RemoveJob(ctx, cli, ns, job); err != nil {
		return err
	}
	if err := r.JobProvider.SyncJob(ctx, cli, ns, job); err != nil {
		return err
	}
	networkStatus, err := r.NetworkProvider.SyncJobNetwork(ctx, cli, ns, job)
	if err != nil {
		return err
	}
	r.networkStatus(ctx, job, networkStatus)
	return nil
}

func (j *JobReconciler) pause(ctx context.Context, cli client.Client, ns string, job *Job) error {
	log := log.FromContext(ctx)
	// ignore if job is already reached a final phase
	if isFinalPhase(job.Status.State.Phase) {
		return nil
	}
	job.Status.State.Phase = JobPhasePaused
	job.Status.State.Reason = "Paused"
	job.Status.State.FinishTimestamp = &metav1.Time{Time: time.Now()}
	// remove job when paused
	log.Info("pausing job", "job", job.Name)
	if err := j.JobProvider.RemoveJob(ctx, cli, ns, job); err != nil {
		return err
	}
	return nil
}

func (r *JobReconciler) finished(ctx context.Context, cli client.Client, ns string, job *Job, jobstatus *JobProviderStatus) error {
	job.Status.State.Phase = jobstatus.Phase
	job.Status.State.FinishTimestamp = jobstatus.FinishTimestamp
	if job.Status.State.StartTimestamp == nil {
		job.Status.State.StartTimestamp = jobstatus.StartTimestamp
	}
	job.Status.Message = jobstatus.Message

	// update status
	if err := r.Client.Status().Update(ctx, job); err != nil {
		return err
	}
	// set spec to paused
	job.Paused = true
	return r.Client.Update(ctx, job)
}

func (r *JobReconciler) networkStatus(_ context.Context, job *Job, status *NetworkProviderStatus) {
	updateRoleStatusFunc(job, func(role JobRole, stats *JobRoleStatus) {
		stats.Ports = status.RolePorts[stats.Name]
	})
}

func updateRoleStatusFunc(job *Job, fn func(role JobRole, stats *JobRoleStatus)) {
	updatedRolesStatus := make([]JobRoleStatus, 0, len(job.Roles))
	oldRolesStatus := make(map[string]JobRoleStatus, len(job.Status.Roles))
	for _, role := range job.Status.Roles {
		oldRolesStatus[role.Name] = role
	}
	for _, role := range job.Roles {
		rolestatus, ok := oldRolesStatus[role.Name]
		if !ok {
			// init emmpty role status
			rolestatus = JobRoleStatus{Name: role.Name}
		}
		// update role
		fn(role, &rolestatus)
		updatedRolesStatus = append(updatedRolesStatus, rolestatus)
	}
	job.Status.Roles = updatedRolesStatus
}

func isFinalPhase(phase JobPhase) bool {
	return phase == JobPhaseCompleted ||
		phase == JobPhaseFailed ||
		phase == JobPhasePaused ||
		phase == JobPhaseUnknown
}

func (c *JobReconciler) Remove(ctx context.Context, job *Job) (controller.Result, error) {
	log := log.FromContext(ctx)
	cli, namespace, err := c.workspaceClient(ctx, job.Tenant, job.Workspace)
	if err != nil {
		log.Error(err, "get workspace client", "tenant", job.Tenant, "workspace", job.Workspace)
		if !errors.IsNotFound(err) {
			return controller.Result{}, nil
		}
		return controller.Result{}, err
	}
	if err := c.JobProvider.RemoveJob(ctx, cli, namespace, job); err != nil {
		return controller.Result{}, err
	}
	if err := c.NetworkProvider.RemoveJobNetwork(ctx, cli, namespace, job); err != nil {
		return controller.Result{}, err
	}
	// if err := r.unlabelStorageset(ctx, job); err != nil {
	// 	return err
	// }
	return controller.Result{}, nil
}

func (j *JobReconciler) workspaceClient(ctx context.Context, tenant, workspacename string) (client.Client, string, error) {
	info, namespace, err := workspace.GetWorkspaceCluster(ctx, j.Holder, j.Client, tenant, workspacename)
	if err != nil {
		log.FromContext(ctx).Error(err, "failed to get workspace cluster", "tenant", tenant, "workspace", workspacename)
		return nil, "", err
	}
	return info.Client(), namespace, nil
}
