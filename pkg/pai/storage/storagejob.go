package storage

import (
	"context"
	"net/http"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

func (a *API) StorageJobGroup() api.Group {
	return api.NewGroup("/storagejobs").
		Tag("StorageJob").
		Route(
			api.GET("").
				Operation("list storage jobs").
				Param(api.PageParams...).
				To(a.ListStorageJob).
				Response(store.List[StorageJob]{}),

			api.GET("/{storagejob}").
				Operation("get storage job").
				To(a.GetStorageJob).
				Response(StorageJob{}),
			api.POST("").
				Operation("create storage job").
				To(a.CreateStorageJob).
				Param(api.BodyParam("storagejob", StorageJob{})).
				Response(StorageJob{}),

			api.PUT("/{storagejob}").
				Operation("update storage job").
				To(a.UpdateStorageJob).
				Param(api.BodyParam("storagejob", StorageJob{})).
				Response(StorageJob{}),

			api.DELETE("/{storagejob}").
				Operation("delete storage job").
				To(a.DeleteStorageJob),
		)
}

func (a *API) ListStorageJob(w http.ResponseWriter, r *http.Request) {
	a.onStorageVolume(w, r, func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error) {
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		var dest []*StorageJob
		if err := list(ctx, kube, &batchv1.JobList{}, dest, func() *StorageJob {
			return &StorageJob{}
		},
			client.InNamespace(space),
			client.MatchingFields{"metadata.ownerReferences.0.name": volume},
		); err != nil {
			return nil, err
		}
		listopts := api.GetListOptions(r)
		namefunc := func(item *StorageJob) string { return item.Name }
		timefunc := func(item *StorageJob) time.Time { return item.UpdatedAt }
		paged := api.PageFrom(dest, listopts.Page, listopts.Size, nil,
			api.SortByFunc(listopts.Sort, namefunc, timefunc))
		return paged, nil
	})
}

func (a *API) GetStorageJob(w http.ResponseWriter, r *http.Request) {
	a.onStorageVolume(w, r, func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error) {
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		storagejob := api.Path(r, "storagejob", "")
		if storagejob == "" {
			return nil, errors.NewBadRequest("storagejob name is required")
		}
		dest := &StorageJob{}
		if err := get(ctx, kube, &batchv1.Job{}, dest, storagejob, space); err != nil {
			return nil, err
		}
		return dest, nil
	})
}

func (a *API) CreateStorageJob(w http.ResponseWriter, r *http.Request) {
	a.onStorageVolume(w, r, func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error) {
		storagejob := &StorageJob{}
		if err := api.Body(r, storagejob); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, createWithOwnerReference(ctx, kube, storagejob, &batchv1.Job{}, &paiv1beta1.StorageVolume{
			ObjectMeta: metav1.ObjectMeta{
				Name: volume,
			},
		})
	})
}

func (a *API) UpdateStorageJob(w http.ResponseWriter, r *http.Request) {
	a.onStorageVolume(w, r, func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error) {
		storagejob := api.Path(r, "storagejob", "")
		if storagejob == "" {
			return nil, errors.NewBadRequest("storagejob name is required")
		}
		storagejobObj := &StorageJob{}
		if err := api.Body(r, storagejobObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, update(ctx, kube, storagejobObj, &batchv1.Job{}, storagejob, space)
	})
}

func (a *API) DeleteStorageJob(w http.ResponseWriter, r *http.Request) {
	a.onStorageVolume(w, r, func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error) {
		storagejob := api.Path(r, "storagejob", "")
		if storagejob == "" {
			return nil, errors.NewBadRequest("storagejob name is required")
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, delete(ctx, kube, &batchv1.Job{}, storagejob, space)
	})
}

func (a *API) onStorageVolume(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, space, volume string, ref store.ObjectReference) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		var scopes []store.Scope
		workspaceobj := &workspace.Workspace{}
		tenant := api.Path(r, "tenant", "")
		if tenant != "" {
			scopes = append(scopes, base.ScopeTenant(tenant))
		}
		workspacename := api.Path(r, "workspace", "")
		if workspacename == "" {
			return nil, errors.NewBadRequest("workspace name is required")
		}
		storagevolume := api.Path(r, "storagevolume", "")
		if storagevolume == "" {
			return nil, errors.NewBadRequest("storagevolume name is required")
		}
		scopes = append(scopes, base.ScopeWorkspace(workspacename))
		if err := a.Store.Scope(scopes...).Get(ctx, workspacename, workspaceobj); err != nil {
			return nil, err
		}
		return fn(ctx, workspacename, storagevolume, workspaceobj.Cluster.ObjectReference)
	})
}
