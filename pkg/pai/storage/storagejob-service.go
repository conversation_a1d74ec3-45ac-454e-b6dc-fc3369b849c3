package storage

import (
	"fmt"
	"net/url"
	"strings"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/store"
)

var _ FromTo = &StorageJob{}

type StorageJob struct {
	Base          `json:",inline"`
	Paused        bool              `json:"paused"`
	Workspace     string            `json:"workspace"`
	StorageVolume string            `json:"storageVolume"`
	Config        map[string]string `json:"config"`
	Image         string            `json:"image"`
	Kind          StorageJobKind    `json:"kind"`
	Status        StorageJobStatus  `json:"status"`
}
type StorageJobStatus struct {
	Phase               StorageJobPhase `json:"phase,omitempty"`
	StartTimestamp      *store.Time     `json:"startTimestamp,omitempty"`
	CompletionTimestamp *store.Time     `json:"completionTimestamp,omitempty"`
	RestartCount        int32           `json:"restartCount,omitempty"`
}

type StorageJobKind string

const (
	StorageJobKindDefault        StorageJobKind = "Default"
	StorageJobKindGit            StorageJobKind = "Git"
	StorageJobKindURI            StorageJobKind = "URI"
	StorageJobKindHuggingFace    StorageJobKind = "HuggingFace"
	StorageJobKindModelScope     StorageJobKind = "ModelScope"
	StorageJobKindPythonEnv      StorageJobKind = "PythonEnv"
	StorageJobKindModelPub       StorageJobKind = "ModelPub"
	StorageJobKindModelPubTask   StorageJobKind = "ModelPubTask"
	StorageJobKindMoha           StorageJobKind = "Moha"
	StorageJobKindGeneralization StorageJobKind = "Generalization"
)

type StorageJobPhase string

const (
	StorageJobPhaseRunning   StorageJobPhase = "Running"
	StorageJobPhaseSucceeded StorageJobPhase = "Succeeded"
	StorageJobPhaseFailed    StorageJobPhase = "Failed"
)

func (s *StorageJob) From(obj client.Object) error {
	job, ok := obj.(*batchv1.Job)
	if !ok {
		return fmt.Errorf("not a storagevolume")
	}
	s.Base.From(job)
	annotation := job.GetAnnotations()
	s.Paused = ptr.Deref(job.Spec.Suspend, false)
	s.Workspace = job.Namespace
	s.StorageVolume = annotation[AnnotationStorageVolume]
	s.Kind = StorageJobKind(annotation[AnnotationStorageJobKind])
	s.Config = annotation
	s.Status = StorageJobStatus{
		StartTimestamp:      job.Status.StartTime,
		CompletionTimestamp: job.Status.CompletionTime,
		RestartCount:        job.Status.Failed + job.Status.Succeeded,
	}
	if job.Status.Succeeded > 0 {
		s.Status.Phase = StorageJobPhaseSucceeded
	} else if job.Status.Failed > 0 {
		s.Status.Phase = StorageJobPhaseFailed
	} else {
		s.Status.Phase = StorageJobPhaseRunning
	}
	return nil
}

func (s *StorageJob) To(obj client.Object) error {
	job, ok := obj.(*batchv1.Job)
	if !ok {
		return fmt.Errorf("not a storagevolume")
	}
	s.Base.To(job)

	job.ObjectMeta = v1.ObjectMeta{
		Name:      s.Name,
		Namespace: s.Workspace,
	}
	job.Annotations = MergeMap(job.Annotations, map[string]string{
		AnnotationStorageVolume:  s.StorageVolume,
		AnnotationStorageJobKind: string(s.Kind),
	})
	podTemplate := &corev1.PodTemplateSpec{
		ObjectMeta: v1.ObjectMeta{
			Labels:      job.Labels,
			Annotations: job.Annotations,
		},
	}
	if err := s.generatePodTemplate(podTemplate); err != nil {
		return err
	}
	job.Spec = batchv1.JobSpec{
		Suspend:      ptr.To(s.Paused),
		BackoffLimit: ptr.To(int32(3)),
		Template:     *podTemplate,
		Selector: &v1.LabelSelector{
			MatchLabels: job.Labels,
		},
	}
	return nil
}

func (s *StorageJob) generatePodTemplate(podTemplate *corev1.PodTemplateSpec) error {
	workDir := TryKeysDef("/data", s.Config, "pwd", "workdir", "cwd")
	c := corev1.Container{
		Name:            "stroagevolume-job",
		Image:           TryKeysDef(s.Image, s.Config, "image"),
		WorkingDir:      workDir,
		VolumeMounts:    []corev1.VolumeMount{{Name: "data", MountPath: workDir}},
		ImagePullPolicy: corev1.PullPolicy(TryKeysDef(string(corev1.PullIfNotPresent), s.Config, "pull-policy")),
	}
	switch s.Kind {
	case StorageJobKindDefault, "":
		if script := TryKeys(s.Config, "script", "shell"); script != "" {
			c.Command = []string{"/bin/sh", "-c"}
			c.Args = []string{script}
		} else {
			c.Command = strings.Fields(TryKeys(s.Config, "command"))
			c.Args = strings.Fields(TryKeys(s.Config, "args"))
		}
	case StorageJobKindURI:
		if err := completeWgetContainer(&c, s.Config, s.Image); err != nil {
			return err
		}
	case StorageJobKindGit:
		if err := completedGitContainer(&c, s.Config, s.Image); err != nil {
			return err
		}
	case StorageJobKindHuggingFace:
		if err := completeHFContainer(&c, s.Image, s.Config); err != nil {
			return err
		}
	case StorageJobKindModelScope:
		if err := completeModelScopeContainer(&c, s.Image, s.Config); err != nil {
			return err
		}
	case StorageJobKindMoha:
		if err := completeMohaContainer(&c, s.Image, s.Config); err != nil {
			return err
		}
	case StorageJobKindPythonEnv: // init workspace python env job
		if err := completeCondaContainer(&c, s.Config, s.Image); err != nil {
			return err
		}
	case StorageJobKindModelPub: // model publish job
		c.Image = TryKeysDef(s.Image, s.Config, "image", "docker")
		c.Command = []string{"tail", "-f", "/dev/null"}

	case StorageJobKindModelPubTask:
		if err := completedModulePubContainer(&c, s.Config, s.Image); err != nil {
			return err
		}
	case StorageJobKindGeneralization:
		c.Image = TryKeysDef(s.Image, s.Config, "image", "docker")
		for k, v := range s.Config {
			c.Args = append(c.Args, fmt.Sprintf("--%s=%s", k, v))
		}
	}
	podTemplate.Spec = corev1.PodSpec{
		RestartPolicy: corev1.RestartPolicyOnFailure,
		Containers:    []corev1.Container{c},
		Volumes: []corev1.Volume{
			{
				Name: "data",
				VolumeSource: corev1.VolumeSource{PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
					ClaimName: s.StorageVolume,
				}},
			},
		},
		// set downloader pod to non root user to avoid permission issues
		// if downloader run as root user, the users in the mounted pvc could not access the data if it's not root user
		SecurityContext: &corev1.PodSecurityContext{
			RunAsUser:  ptr.To(int64(1000)),
			RunAsGroup: ptr.To(int64(1000)),
			FSGroup:    ptr.To(int64(1000)),
		},
	}
	return nil
}

func completeWgetContainer(c *corev1.Container, config map[string]string, image string) error {
	url := TryKeys(config, "url", "uri", "urls")
	if url == "" {
		return fmt.Errorf("url not provided")
	}
	c.Image = TryKeysDef(image, config, "image", "docker")
	c.ImagePullPolicy = corev1.PullAlways
	c.Command = []string{"/bin/sh", "-c"}
	c.Args = []string{fmt.Sprintf(`wget '%s'`, url)}
	return nil
}

func completeMohaContainer(c *corev1.Container, haimage string, config map[string]string) error {
	giturl := TryKeysDef("https://moha.xiaoshiai.cn", config, "url", "addr")
	var mohaType string
	typ := TryKeysDef("model", config, "type", "repo-type")
	switch typ {
	case LabelStorageVolumeKindModelSet:
		mohaType = "/models"
	case LabelStorageVolumeKindDataSet:
		mohaType = "/datasets"
	}
	names := strings.SplitN(TryKeys(config, "name", "repo"), "/", 2)
	if len(names) < 2 {
		return fmt.Errorf("invalid model/dataset name %s", TryKeys(config, "name", "repo"))
	}
	repoOri, repoName := names[0], names[1]
	repoName += ".git"
	realUrl, err := url.JoinPath(giturl, repoOri, mohaType, repoName)
	if err != nil {
		return err
	}
	config["url"] = realUrl // update url in config
	return completedGitContainer(c, config, haimage)
}

func completeModelScopeContainer(c *corev1.Container, image string, config map[string]string) error {
	giturl := TryKeysDef("https://www.modelscope.cn", config, "url", "addr")
	typ := TryKeysDef("model", config, "type", "repo-type")
	if typ == "dataset" {
		giturl += "/datasets"
	}
	giturl += "/" + TryKeys(config, "name", "repo")
	config["url"] = giturl + ".git" // update url in config
	return completedGitContainer(c, config, image)
}

func completeHFContainer(c *corev1.Container, hfimage string, config map[string]string) error {
	if config == nil {
		config = map[string]string{}
	}
	giturl := TryKeysDef("https://huggingface.co", config, "url", "addr")
	typ := TryKeysDef("model", config, "type", "repo-type")
	switch typ {
	case LabelStorageVolumeKindDataSet:
		giturl += "/datasets"
	}
	giturl += "/" + TryKeys(config, "name", "repo")
	gitu, err := url.Parse(giturl)
	if err != nil {
		return fmt.Errorf("invalid git url: %s", err)
	}
	config["url"] = gitu.String() // update url in config
	return completedGitContainer(c, config, hfimage)
}

func completedGitContainer(c *corev1.Container, config map[string]string, gitimage string) error {
	giturl := TryKeys(config, "url", "addr")
	if giturl == "" {
		return fmt.Errorf("git url not provided")
	}
	gitu, err := url.Parse(giturl)
	if err != nil {
		return fmt.Errorf("invalid git url: %s", err)
	}
	switch gitu.Scheme {
	case "http", "https":
		username, password := TryKeys(config, "username", "user"), TryKeys(config, "password", "token")
		gitu.User = url.UserPassword(username, password)
	case "ssh", "git+ssh", "":
		sshkey := TryKeys(config, "ssh-key")
		_ = sshkey
		return fmt.Errorf("ssh not supported yet")
	}
	c.Image = TryKeysDef(gitimage, config, "image", "docker") // allow override git image
	c.ImagePullPolicy = corev1.PullAlways
	c.Args = []string{
		gitu.String(),                           // git url
		TryKeys(config, "branch", "ref", "tag"), // branch
		TryKeysDef(".", config, "dest", "dir"),  // dest
	}
	return nil
}

func completeCondaContainer(c *corev1.Container, config map[string]string, image string) error {
	workDir := "/datas/conda"
	scriptlines := []string{}
	if reset := TryKeys(config, "reset"); reset == "true" {
		scriptlines = append(scriptlines, fmt.Sprintf("rm -rf %s/*", workDir))
	}
	// tensorflow==2.3.1;fastapi==0.63.0
	if reqs := TryKeys(config, "requir", "requires"); reqs != "" {
		requirements := strings.Builder{}
		for i := range strings.SplitSeq(reqs, ";") {
			i = strings.TrimSpace(i)
			if i == "" {
				continue
			}
			requirements.WriteString(i + "\n")
		}
		scriptlines = append(scriptlines, fmt.Sprintf("echo '%s' > requirements.txt", requirements.String()))
	}
	{
		pythonVersion := TryKeys(config, "version", "versions")
		pipServer := TryKeysDef("https://mirrors.aliyun.com/pypi/simple/", config, "pip-server")
		var source string
		condaresource := TryKeys(config, "conda", "condas")
		for i := range strings.SplitSeq(condaresource, ";") {
			source += fmt.Sprintf("%s ", i)
		}
		command := fmt.Sprintf("/conda.sh %s %s %s requirements.txt %s", workDir, pythonVersion, pipServer, source)
		scriptlines = append(scriptlines, command)
	}

	c.Image = TryKeysDef(image, config, "image")
	c.VolumeMounts = []corev1.VolumeMount{{Name: "data", MountPath: workDir}}
	c.WorkingDir = workDir
	c.Command = []string{"/bin/bash", "-c"}
	c.Args = []string{strings.Join(scriptlines, " && ")}
	return nil
}
func completedModulePubContainer(c *corev1.Container, config map[string]string, image string) error {
	if err := completedGitContainer(c, config, image); err != nil {
		return err
	}
	if TryKeys(config, "username", "user") == "" {
		return fmt.Errorf("username not provided")
	}
	force := TryKeys(config, "force_push", "force")
	c.Args = append(c.Args, force)
	commit := TryKeys(config, "commit_info", "commit")
	if commit == "" {
		commit = "Initial commit"
	}
	c.Args = append(c.Args, commit)
	return nil
}
