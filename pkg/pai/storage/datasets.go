package storage

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type Dataset struct {
	store.ObjectMeta   `json:",inline"`
	Tenant             string            `json:"tenant,omitempty"`                       // tenant is the tenant that this storage set belongs to
	Workspace          string            `json:"workspace,omitempty"`                    // workspace is the workspace that this storage set belongs to
	StorageClusterName string            `json:"storageClusterName" validate:"required"` // storageClusterName is the name of the storage cluster that this storage set belongs to
	Capacity           resource.Quantity `json:"capacity" validate:"required"`           // capacity is the total capacity of the storage set, e.g., "100Gi"

	// Downloaders is a list of downloaders that can be used to download files from this storage set.
	// It auto creates downloaders when the storage set is created.
	Downloaders []Downloader     `json:"downloaders"`
	Public      bool             `json:"public"` // public storage set can be accessed by all users
	Status      StorageSetStatus `json:"status"`
}

func DatasetFrom(set StorageSet) Dataset {
	return Dataset{
		ObjectMeta:         set.ObjectMeta,
		Tenant:             set.Tenant,
		Workspace:          set.Workspace,
		StorageClusterName: set.StorageClusterName,
		Capacity:           set.Capacity,
		Public:             set.Public,
		Status:             set.Status,
	}
}

func DatasetTo(set Dataset) StorageSet {
	return StorageSet{
		ObjectMeta:         set.ObjectMeta,
		Tenant:             set.Tenant,
		Workspace:          set.Workspace,
		StorageClusterName: set.StorageClusterName,
		Capacity:           set.Capacity,
		Public:             set.Public,
		Status:             set.Status,
	}
}

func (a *API) ListSharedDataset(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		list, err := ListStorageSets(r, a.Store, ListStorageSetOptions{
			Tenant: tenant,
			Public: ptr.To(true), // Only list public datasets
		})
		if err != nil {
			return nil, err
		}
		// Convert StorageSet to Dataset
		datasetlist := base.MapList(list, DatasetFrom)
		return datasetlist, nil
	})
}

func (a *API) GetSharedDataset(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		dataset := api.Path(r, "dataset", "")
		if dataset == "" {
			return nil, errors.NewBadRequest("dataset name is required")
		}
		set, err := GetSharedStorageSet(ctx, a.Store, tenant, dataset)
		if err != nil {
			return nil, err
		}
		return DatasetFrom(*set), nil
	})
}

func (a *API) ListDataset(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		list, err := ListStorageSets(r, a.Store, ListStorageSetOptions{
			Tenant:    tenant,
			Workspace: workspace,
		})
		if err != nil {
			return nil, err
		}
		// Convert StorageSet to Dataset
		datasetlist := base.MapList(list, DatasetFrom)
		return datasetlist, nil
	})
}

func (a *API) GetDataset(w http.ResponseWriter, r *http.Request) {
	a.onDataset(w, r, func(ctx context.Context, tenant, workspace, dataset string) (any, error) {
		return base.GenericGet(r, a.Store, &Dataset{}, dataset)
	})
}

func (a *API) CreateDataset(w http.ResponseWriter, r *http.Request) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		dataset := &Dataset{}
		if err := api.Body(r, dataset); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateDataset(dataset); err != nil {
			return nil, err
		}
		storageset := DatasetTo(*dataset)
		created, err := CreateStorageSet(ctx, a.Store, tenant, workspace, &storageset)
		if err != nil {
			return nil, err
		}
		if err := CreateDownloaders(ctx, a.Store, created, dataset.Downloaders); err != nil {
			return created, err
		}
		return created, nil
	})
}

func validateDataset(dataset *Dataset) error {
	if dataset.Name == "" {
		return errors.NewBadRequest("dataset name is required")
	}
	if dataset.StorageClusterName == "" {
		return errors.NewBadRequest("storageClusterName is required")
	}
	if dataset.Capacity.IsZero() {
		return errors.NewBadRequest("capacity is required")
	}
	return nil
}

func (a *API) UpdateDataset(w http.ResponseWriter, r *http.Request) {
	a.onDataset(w, r, func(ctx context.Context, tenant, workspace, dataset string) (any, error) {
		datasetObj := &Dataset{}
		if err := api.Body(r, datasetObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateDataset(datasetObj); err != nil {
			return nil, err
		}
		storageset := DatasetTo(*datasetObj)
		updated, err := UpdateStorageSet(ctx, a.Store, tenant, workspace, &storageset)
		if err != nil {
			return nil, err
		}
		returnedSet := DatasetFrom(*updated)
		return returnedSet, nil
	})
}

func (a *API) DeleteDataset(w http.ResponseWriter, r *http.Request) {
	a.onDataset(w, r, func(ctx context.Context, tenant, workspace, dataset string) (any, error) {
		ret, err := DeleteStorageSet(ctx, a.Store, tenant, workspace, dataset)
		if err != nil {
			return nil, err
		}
		return DatasetFrom(*ret), nil
	})
}

func (a *API) onDataset(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, workspace, dataset string) (any, error)) {
	base.OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		dataset := api.Path(r, "dataset", "")
		if dataset == "" {
			return nil, errors.NewBadRequest("dataset name is required")
		}
		return fn(ctx, tenant, workspace, dataset)
	})
}

func (a *API) DatasetGroup() api.Group {
	return api.
		NewGroup("").
		Tag("Dataset").
		SubGroup(
			api.NewGroup("/tenants/{tenant}/datasets").
				Route(
					api.GET("").
						Operation("list shared datasets").
						Param(api.PageParams...).
						To(a.ListSharedDataset).
						Response(store.List[Dataset]{}),

					api.GET("/{Dataset}").
						Operation("get shared storage set").
						To(a.GetSharedDataset).
						Response(Dataset{}),
				),
			api.NewGroup("/tenants/{tenant}/workspaces/{workspace}/datasets").
				Route(
					api.GET("").
						Operation("list storage sets").
						Param(api.PageParams...).
						To(a.ListDataset).
						Response(store.List[Dataset]{}),

					api.GET("/{dataset}").
						Operation("get storage set").
						To(a.GetDataset).
						Response(Dataset{}),

					api.POST("").
						Operation("create storage set").
						To(a.CreateDataset).
						Param(api.BodyParam("dataset", Dataset{})).
						Response(Dataset{}),

					api.PUT("/{dataset}").
						Operation("update storage set").
						To(a.UpdateDataset).
						Param(api.BodyParam("dataset", Dataset{})).
						Response(Dataset{}),

					api.DELETE("/{dataset}").
						Operation("delete storage set").
						To(a.DeleteDataset),
				),
		)
}
