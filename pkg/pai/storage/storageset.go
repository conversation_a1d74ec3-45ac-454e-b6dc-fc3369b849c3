package storage

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type StorageSet struct {
	store.ObjectMeta   `json:",inline"`
	Tenant             string            `json:"tenant,omitempty"`                       // tenant is the tenant that this storage set belongs to
	Workspace          string            `json:"workspace,omitempty"`                    // workspace is the workspace that this storage set belongs to
	StorageClusterName string            `json:"storageClusterName" validate:"required"` // storageClusterName is the name of the storage cluster that this storage set belongs to
	Kind               StorageSetKind    `json:"kind"`
	Capacity           resource.Quantity `json:"capacity" validate:"required"` // capacity is the total capacity of the storage set, e.g., "100Gi"
	Config             map[string]string `json:"config"`
	S3                 S3Info            `json:"s3"`
	Public             bool              `json:"public"` // public storage set can be accessed by all users
	Status             StorageSetStatus  `json:"status"`
}

type StorageSetKind string

type StorageSetStatus struct {
	S3      S3Info `json:"s3"`
	Message string `json:"message,omitempty"`
	Path    string `json:"path"` // path is the storage path of the storage set
}

type S3Info struct {
	Endpoint string `json:"endpoint,omitempty"` // Endpoint is the S3 endpoint URL
	// Bucket          string `json:"bucket,omitempty"`          // Bucket is the S3 bucket name
	AccessKeyID     string `json:"accessKeyID,omitempty"`     // AccessKeyID is the S3 access key ID
	SecretAccessKey string `json:"secretAccessKey,omitempty"` // SecretAccessKey is the S3 secret access key

	ReadOnlyAccessKeyID     string `json:"readOnlyAccessKeyID,omitempty"`     // ReadOnlyAccessKeyID is the S3 read-only access key ID
	ReadOnlySecretAccessKey string `json:"readOnlySecretAccessKey,omitempty"` // ReadOnlySecretAccessKey is the S3 read-only secret access key
}

type ListStorageSetOptions struct {
	Tenant    string
	Kind      StorageSetKind // If set, only list storage sets of this kind
	Workspace string
	Public    *bool // If true, only list public storage sets
}

func ListStorageSets(r *http.Request, storage store.Store, opts ListStorageSetOptions) (store.List[StorageSet], error) {
	list := store.List[StorageSet]{}

	reqopts := api.GetListOptions(r)
	options := []store.ListOption{
		store.WithPageSize(reqopts.Page, reqopts.Size),
		store.WithSort(reqopts.Sort),
		store.WithSearch(reqopts.Search),
	}
	if labelsSelectorStr := api.Query(r, "label-selector", ""); labelsSelectorStr != "" {
		labelsSelector, err := base.ParseLabelSelector(api.Query(r, "label-selector", ""))
		if err != nil {
			return list, err
		}
		options = append(options, store.WithLabelRequirementsFromSelector(labelsSelector))
	}
	fieldsselector := store.Requirements{}
	if opts.Tenant != "" {
		fieldsselector = append(fieldsselector, store.RequirementEqual("tenant", opts.Tenant))
	}
	if opts.Workspace != "" {
		fieldsselector = append(fieldsselector, store.RequirementEqual("workspace", opts.Workspace))
	}
	if opts.Kind != "" {
		fieldsselector = append(fieldsselector, store.RequirementEqual("kind", opts.Kind))
	}
	if opts.Public != nil {
		fieldsselector = append(fieldsselector, store.RequirementEqual("public", *opts.Public))
	}
	options = append(options, store.WithFieldRequirements(fieldsselector...))
	if err := storage.List(r.Context(), &list, options...); err != nil {
		return list, err
	}
	return list, nil
}

func GetSharedStorageSet(ctx context.Context, storage store.Store, tenant, name string) (*StorageSet, error) {
	set := &StorageSet{}
	if err := storage.Get(ctx, name, set, store.WithGetFieldRequirements(store.RequirementEqual("public", true))); err != nil {
		return nil, err
	}
	return set, nil
}

func GetStorageSet(ctx context.Context, storage store.Store, tenant, workspace, name string) (*StorageSet, error) {
	options := []store.GetOption{
		store.WithGetFieldRequirements(
			store.RequirementEqual("tenant", tenant),
			store.RequirementEqual("workspace", workspace),
		),
	}
	set := &StorageSet{}
	if err := storage.Get(ctx, name, set, options...); err != nil {
		return nil, err
	}
	return set, nil
}

func CreateStorageSet(ctx context.Context, storage store.Store, tenant, workspace string, set *StorageSet) (*StorageSet, error) {
	set.Tenant = tenant
	set.Workspace = workspace
	if err := storage.Create(ctx, set); err != nil {
		return nil, err
	}
	return set, nil
}

func UpdateStorageSet(ctx context.Context, storage store.Store, tenant, workspace string, set *StorageSet) (*StorageSet, error) {
	set.Tenant = tenant
	set.Workspace = workspace
	if err := storage.Update(ctx, set); err != nil {
		return nil, err
	}
	return set, nil
}

func DeleteStorageSet(ctx context.Context, storage store.Store, tenant, workspace, name string) (*StorageSet, error) {
	set := &StorageSet{ObjectMeta: store.ObjectMeta{Name: name}}
	options := []store.DeleteOption{
		store.WithDeleteFieldRequirements(
			store.RequirementEqual("tenant", tenant),
			store.RequirementEqual("workspace", workspace),
		),
	}
	if err := storage.Delete(ctx, set, options...); err != nil {
		return nil, err
	}
	return set, nil
}

func CreateDownloaders(ctx context.Context, storage store.Store, set *StorageSet, downloaders []Downloader) error {
	if len(downloaders) == 0 {
		return nil // No downloaders to apply
	}
	return nil
}
