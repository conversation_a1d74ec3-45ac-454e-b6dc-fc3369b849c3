package storage

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	cloudclient "xiaoshiai.cn/rune/pkg/cloud/client"
)

type API struct {
	Store       store.Store
	CloudClient cloudclient.CloudClient
}

func NewAPI(store store.Store, client cloudclient.CloudClient) *API {
	return &API{
		Store:       store,
		CloudClient: client,
	}
}

func (a *API) Group() api.Group {
	return api.NewGroup("").
		SubGroup(
			base.NewTenantOrClusterGroup().
				SubGroup(a.StorageClusterGroup()),
			base.NewClusterGroup().
				SubGroup(a.StorageClusterGroup()),

			base.NewWorkspaceGroup().
				SubGroup(a.StorageVolumeGroup()),

			base.NewWorkspaceStorageVolumeGroup().
				SubGroup(a.StorageJobGroup()),
		)
}
