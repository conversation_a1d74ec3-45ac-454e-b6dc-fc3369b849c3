package storage

import (
	"context"
	"net/http"
	"time"

	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

func (a *API) StorageVolumeGroup() api.Group {
	return api.NewGroup("/storagevolumes").
		Tag("StorageVolume").
		Route(
			api.GET("").
				Operation("list storage volumes").
				Param(api.PageParams...).
				To(a.ListStorageVolume).
				Response([]StorageVolume{}),

			api.GET("/{storagevolume}").
				Operation("get storage volume").
				To(a.GetStorageVolume).
				Response(StorageVolume{}),

			api.POST("").
				Operation("create storage volume").
				To(a.CreateStorageVolume).
				Param(api.BodyParam("storagevolume", StorageVolume{})).
				Response(StorageVolume{}),

			api.PUT("/{storagevolume}").
				Operation("update storage volume").
				To(a.UpdateStorageVolume).
				Param(api.BodyParam("storagevolume", StorageVolume{})).
				Response(StorageVolume{}),

			api.DELETE("/{storagevolume}").
				Operation("delete storage volume").
				To(a.DeleteStorageVolume),
		)
}

func (a *API) DeleteStorageVolume(w http.ResponseWriter, r *http.Request) {
	a.onWorkspaceCluster(w, r, func(ctx context.Context, workspace string, ref store.ObjectReference) (any, error) {
		storagevolume := api.Path(r, "storagevolume", "")
		if storagevolume == "" {
			return nil, errors.NewBadRequest("storagevolume name is required")
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, delete(ctx, kube, &paiv1beta1.StorageVolume{}, storagevolume, workspace)
	})
}

func (a *API) UpdateStorageVolume(w http.ResponseWriter, r *http.Request) {
	a.onWorkspaceCluster(w, r, func(ctx context.Context, workspace string, ref store.ObjectReference) (any, error) {
		storagevolume := api.Path(r, "storagevolume", "")
		if storagevolume == "" {
			return nil, errors.NewBadRequest("storagevolume name is required")
		}
		storagevolumeObj := &StorageVolume{}
		if err := api.Body(r, storagevolumeObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, update(ctx, kube, storagevolumeObj, &paiv1beta1.StorageVolume{}, storagevolume, workspace)
	})
}

func (a *API) CreateStorageVolume(w http.ResponseWriter, r *http.Request) {
	a.onWorkspaceCluster(w, r, func(ctx context.Context, workspace string, ref store.ObjectReference) (any, error) {
		storagevolume := &StorageVolume{}
		if err := api.Body(r, storagevolume); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, create(ctx, kube, storagevolume, &paiv1beta1.StorageVolume{})
	})
}

func (a *API) GetStorageVolume(w http.ResponseWriter, r *http.Request) {
	a.onWorkspaceCluster(w, r, func(ctx context.Context, workspace string, ref store.ObjectReference) (any, error) {
		storagevolume := api.Path(r, "storagevolume", "")
		if storagevolume == "" {
			return nil, errors.NewBadRequest("storagevolume name is required")
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		dest := &StorageVolume{}
		if err := get(ctx, kube, &paiv1beta1.StorageVolume{}, dest, storagevolume, workspace); err != nil {
			return nil, err
		}
		return dest, nil
	})
}

func (a *API) ListStorageVolume(w http.ResponseWriter, r *http.Request) {
	a.onWorkspaceCluster(w, r, func(ctx context.Context, workspace string, ref store.ObjectReference) (any, error) {
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		var dest []*StorageVolume
		if err := list(ctx, kube, &paiv1beta1.StorageVolumeList{}, dest, func() *StorageVolume {
			return &StorageVolume{}
		}, client.InNamespace(workspace)); err != nil {
			return nil, err
		}
		listopts := api.GetListOptions(r)
		namefunc := func(item *StorageVolume) string { return item.Name }
		timefunc := func(item *StorageVolume) time.Time { return item.UpdatedAt }
		paged := api.PageFrom(dest, listopts.Page, listopts.Size, nil,
			api.SortByFunc(listopts.Sort, namefunc, timefunc))
		return paged, nil
	})
}

func (a *API) onWorkspaceCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, space string, ref store.ObjectReference) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		var scopes []store.Scope
		workspaceobj := &workspace.Workspace{}
		tenant := api.Path(r, "tenant", "")
		if tenant != "" {
			scopes = append(scopes, base.ScopeTenant(tenant))
		}
		workspacename := api.Path(r, "workspace", "")
		if workspacename == "" {
			return nil, errors.NewBadRequest("workspace name is required")
		}
		scopes = append(scopes, base.ScopeWorkspace(workspacename))
		if err := a.Store.Scope(scopes...).Get(ctx, workspacename, workspaceobj); err != nil {
			return nil, err
		}
		return fn(ctx, workspacename, workspaceobj.Cluster.ObjectReference)
	})
}
