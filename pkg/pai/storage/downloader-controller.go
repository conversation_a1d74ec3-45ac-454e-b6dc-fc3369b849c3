package storage

import (
	"context"
	"fmt"
	"net/url"
	"reflect"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	"xiaoshiai.cn/rune/pkg/base"
	cloudclient "xiaoshiai.cn/rune/pkg/cloud/client"
	"xiaoshiai.cn/rune/pkg/cloud/kube"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
)

const (
	AnnotationDownloaderIdentity = "downloader." + common.GroupPrefix + "/identity"
)

const (
	DownloaderFinalizer = "downloader." + common.GroupPrefix + "/finalizer"
)

func NewDownloaderController(ctx context.Context, storage store.Store, cloudcli cloudclient.CloudClient, info StorageClusterProviderHolder) (*controller.Controller, error) {
	dynSource := cloudclient.NewDynamicClusterResourceSource(ctx, cloudcli, kube.Scheme, DynamicResourceKeyFunc)

	rec := &DownloaderReconciler{
		Storage:                      storage,
		Holder:                       info,
		DynamicClusterResourceSource: dynSource,
	}
	better := controller.NewBetterReconciler(rec,
		storage,
		controller.WithFinalizer(DownloaderFinalizer),
		controller.WithAutosetStatus())
	c := controller.
		NewController("downloaders", better).
		Watch(controller.NewStoreSource(storage, &Downloader{})).
		Watch(dynSource)

	return c, nil
}

func DynamicResourceKeyFunc(cluster store.ObjectReference, obj client.Object) []controller.ScopedKey {
	switch typed := obj.(type) {
	case *corev1.Pod:
		annotation := typed.GetAnnotations()
		if annotation == nil {
			return nil
		}
		identity, ok := annotation[AnnotationDownloaderIdentity]
		if !ok {
			return nil
		}
		ref := controller.DecodeReference(identity)
		return []controller.ScopedKey{controller.NewScopedKey(ref.Scopes, ref.Name)}
	}
	return nil
}

type DownloaderReconciler struct {
	// DefaultNamespace is the default namespace for the downloader job run
	DefaultNamespace             string
	CloudClient                  cloudclient.CloudClient
	Storage                      store.Store
	Holder                       StorageClusterProviderHolder
	DynamicClusterResourceSource *cloudclient.DynamicClusterResourceSource[controller.ScopedKey]
	images                       *ImagesOptions
}

func (d DownloaderReconciler) GetStorageSetAndCluster(ctx context.Context, tenant, storagesetname string) (*StorageSet, *StorageCluster, error) {
	storage := d.Storage.Scope(base.ScopeTenant(tenant))
	// Get the storage set
	storageset := &StorageSet{}
	if err := storage.Get(ctx, storagesetname, storageset); err != nil {
		return nil, nil, err
	}
	storagecluster := &StorageCluster{}
	if err := d.Storage.Get(ctx, storageset.StorageClusterName, storagecluster); err != nil {
		return nil, nil, err
	}
	if storagecluster.Cluster.Name == "" {
		return nil, nil, errors.NewBadRequest(fmt.Sprintf("storage cluster %s has no cluster reference", storagecluster.Name))
	}
	return storageset, storagecluster, nil
}

func (d DownloaderReconciler) GetStorageSetCluster(ctx context.Context, tenant, storagesetname string) (store.ObjectReference, error) {
	storage := d.Storage.Scope(base.ScopeTenant(tenant))
	// Get the storage set
	storageset := StorageSet{}
	if err := storage.Get(ctx, storagesetname, &storageset); err != nil {
		return store.ObjectReference{}, err
	}
	storagecluster := StorageCluster{}
	if err := d.Storage.Get(ctx, storageset.StorageClusterName, &storagecluster); err != nil {
		return store.ObjectReference{}, err
	}
	ref := storagecluster.Cluster
	if ref.Name == "" {
		return store.ObjectReference{}, errors.NewBadRequest(fmt.Sprintf("storage cluster %s has no cluster reference", storagecluster.Name))
	}
	return ref, nil
}

// 创建pv和pvc
func (d *DownloaderReconciler) syncStorage(ctx context.Context, cluster string, cli client.Client, options provider.ProviderPathOptions) error {
	pd, err := d.Holder.GetProvider(ctx, cluster)
	if err != nil {
		return err
	}
	persistenVolume, err := pd.PersistentVolumeClaimContext(options, true)
	if err != nil {
		return err
	}
	var objects []client.Object
	objects = append(objects, persistenVolume.ExtraResources...)
	objects = append(objects, &persistenVolume.PersistentVolume)
	objects = append(objects, &persistenVolume.PersistentVolumeClaim)
	for _, obj := range objects {
		if _, err := controllerutil.CreateOrUpdate(ctx, cli, obj, func() error {
			return nil
		}); err != nil {
			return err
		}
	}
	return nil
}

func (d *DownloaderReconciler) Sync(ctx context.Context, downloader *Downloader) (controller.Result, error) {
	jobName, jobNamespace := GenerateDownloaderJobName(downloader), d.DefaultNamespace

	set, cluster, err := d.GetStorageSetAndCluster(ctx, downloader.Tenant, downloader.Storageset)
	if err != nil {
		return controller.Result{}, fmt.Errorf("failed to get storage set cluster: %w", err)
	}
	ref := cluster.Cluster
	kubes, err := d.CloudClient.GetKubernetes(ctx, ref)
	if err != nil {
		return controller.Result{}, fmt.Errorf("failed to get kubernetes client for storage set %s: %w", downloader.Storageset, err)
	}

	// add to watch
	d.DynamicClusterResourceSource.StartWatch(ctx, ref, &corev1.PodList{})
	cli := kubes.Client()
	// todo - 需要确保对应存储的CSI驱动已经部署
	if err := d.syncStorage(ctx, ref.Name, cli, provider.ProviderPathOptions{
		StorageSetName: downloader.Storageset,
		TenantName:     downloader.Tenant,
		Capacity:       set.Capacity,
		Path:           StoragesetIdentity(downloader.Tenant, downloader.Storageset),
		Namespace:      d.DefaultNamespace,
	}); err != nil {
		return controller.Result{}, fmt.Errorf("failed to createOrUpdate storage %w", err)
	}

	existsjob := &batchv1.Job{ObjectMeta: metav1.ObjectMeta{Name: jobName, Namespace: jobNamespace}}

	// skip if paused or downloader removed
	if TryKeys(downloader.Config, "paused") == "true" {
		downloader.Status.Message = "paused"
		// pause job by patching it
		patch := client.RawPatch(types.MergePatchType, []byte(`{"spec":{"suspend":true}}`))
		if err := client.IgnoreNotFound(cli.Patch(ctx, existsjob, patch)); err != nil {
			return controller.Result{}, fmt.Errorf("failed to patch downloader job %s: %w", jobName, err)
		}
		return controller.Result{}, nil
	}

	// job config changed, delete job and recreate
	if downloader.Status.StartTimestamp.IsZero() || !reflect.DeepEqual(downloader.Config, downloader.Status.Config) {
		if err := cli.Get(ctx, client.ObjectKeyFromObject(existsjob), existsjob); err != nil {
			if !errors.IsNotFound(err) {
				return controller.Result{}, fmt.Errorf("failed to get existing job %s: %w", jobName, err)
			}
		} else {
			if existsjob.DeletionTimestamp != nil {
				return controller.Result{RequeueAfter: 5 * time.Second}, nil
			}
			if err := client.IgnoreNotFound(cli.Delete(ctx, existsjob)); err != nil {
				return controller.Result{}, fmt.Errorf("failed to delete existing job %s: %w", jobName, err)
			}
			return controller.Result{RequeueAfter: 5 * time.Second}, nil
		}
		downloader.Status.Phase = DownloaderPhasePending
		downloader.Status.Config = downloader.Config // update status config
		downloader.Status.StartTimestamp = &metav1.Time{Time: time.Now()}
		downloader.Status.CompletionTimestamp = nil // reset completion timestamp

		if err := d.CompleteDownloaderJob(ctx, cli, downloader, existsjob); err != nil {
			return controller.Result{}, fmt.Errorf("failed to apply downloader job: %w", err)
		}
		if err := cli.Create(ctx, existsjob); err != nil {
			return controller.Result{}, fmt.Errorf("failed to create downloader job: %w", err)
		}
		return controller.Result{}, nil
	}
	// check if job already exists
	if err := cli.Get(ctx, client.ObjectKeyFromObject(existsjob), existsjob); err != nil {
		if !errors.IsNotFound(err) {
			return controller.Result{}, fmt.Errorf("failed to get existing job %s: %w", jobName, err)
		}
		// job not found, create a new one
		if err := d.CompleteDownloaderJob(ctx, cli, downloader, existsjob); err != nil {
			return controller.Result{}, fmt.Errorf("failed to create downloader pod: %w", err)
		}
		if err := cli.Create(ctx, existsjob); err != nil {
			return controller.Result{}, fmt.Errorf("failed to create downloader job: %w", err)
		}
		return controller.Result{}, nil
	}
	// update status based on job status
	if existsjob.Status.Succeeded > 0 {
		downloader.Status.Phase = DownloaderPhaseSucceeded
	} else if existsjob.Status.Failed > 0 {
		downloader.Status.Phase = DownloaderPhaseFailed
	} else {
		downloader.Status.Phase = DownloaderPhaseRunning
	}
	downloader.Status.RestartCount = existsjob.Status.Failed + existsjob.Status.Succeeded
	downloader.Status.StartTimestamp = existsjob.Status.StartTime
	downloader.Status.CompletionTimestamp = existsjob.Status.CompletionTime
	return controller.Result{}, nil
}

func GenerateDownloaderJobName(downloader *Downloader) string {
	fullname := fmt.Sprintf("dl-%s-%s-%s", downloader.Tenant, downloader.Storageset, downloader.Name)
	return EncodeToK8sName(fullname)
}

func (c *DownloaderReconciler) Remove(ctx context.Context, key *Downloader) (controller.Result, error) {
	if err := c.RemoveDownloader(ctx, key); err != nil {
		return controller.Result{}, fmt.Errorf("failed to remove downloader %s: %w", key.Name, err)
	}
	return controller.Result{}, nil
}

func (d DownloaderReconciler) CompleteDownloaderJob(ctx context.Context, cli client.Client, downloader *Downloader, job *batchv1.Job) error {
	job.Labels = MergeMap(job.Labels, downloader.Labels)
	job.Annotations = MergeMap(job.Annotations, downloader.Annotations)
	job.Spec.BackoffLimit = ptr.To(int32(3))

	// This is a placeholder function, actual implementation will depend on the specific requirements
	podTemplate := corev1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      downloader.Labels,
			Annotations: downloader.Annotations,
		},
		Spec: corev1.PodSpec{},
	}
	podTemplate.Labels = MergeMap(podTemplate.Labels, map[string]string{
		"app":        downloader.Name,
		"downloader": "true",
	})
	podTemplate.Annotations = MergeMap(podTemplate.Annotations, map[string]string{
		AnnotationDownloaderIdentity: controller.EncodeReference(store.ObjectReferenceFrom(downloader)),
	})
	if err := d.CompleteDownloaderPod(ctx, downloader, &podTemplate); err != nil {
		return fmt.Errorf("failed to complete downloader pod template: %w", err)
	}
	job.Spec.Template = podTemplate
	job.Spec.Selector = &metav1.LabelSelector{MatchLabels: podTemplate.Labels}
	return nil
}

const (
	StorageDownloaderKindDefault        DownloaderKind = "Default"
	StorageDownloaderKindGit            DownloaderKind = "Git"
	StorageDownloaderKindURI            DownloaderKind = "URI"
	StorageDownloaderKindHDFS           DownloaderKind = "HDFS"
	StorageDownloaderKindHuggingFace    DownloaderKind = "HuggingFace"
	StorageDownloaderKindModelScope     DownloaderKind = "ModelScope"
	StorageDownloaderKindModelx         DownloaderKind = "ModelX"
	StorageDownloaderKindPythonEnv      DownloaderKind = "PythonEnv"
	StorageDownloaderKindCopyData       DownloaderKind = "CopyData"
	StorageDownloaderKindModelPub       DownloaderKind = "ModelPub"
	StorageDownloaderKindModelPubTask   DownloaderKind = "ModelPubTask" // 一键发布任务
	StorageDownloaderKindWarmUp         DownloaderKind = "WarmUp"
	StorageDownloaderKindClone          DownloaderKind = "Clone"
	StorageDownloaderKindMoha           DownloaderKind = "Moha"
	StorageDownloaderKindGeneralization DownloaderKind = "Generalization" // 数据集泛化任务
)

type ImagesOptions struct {
	Base                    string `json:"base,omitempty"`
	HuggingFace             string `json:"huggingFace,omitempty"`
	ModelScope              string `json:"modelScope,omitempty"`
	Git                     string `json:"git,omitempty"`
	DistributionInitializer string `json:"distributionInitializer,omitempty"`
	Ternsorboard            string `json:"tensorboard,omitempty"`
	PythonEnv               string `json:"pythonEnv,omitempty"`
	CopyData                string `json:"copydata,omitempty"`
	ModulePubTask           string `json:"modulePubTask,omitempty"`
	Generalization          string `json:"generalization,omitempty"`
}

func (r *DownloaderReconciler) CompleteDownloaderPod(ctx context.Context,
	downloader *Downloader, podTemplate *corev1.PodTemplateSpec,
) error {
	if downloader.Config == nil {
		downloader.Config = map[string]string{}
	}
	tenantname, storagesetName := downloader.Tenant, downloader.Storageset
	pvcName := StoragesetIdentity(tenantname, storagesetName)

	workDir := TryKeysDef("/data", downloader.Config, "pwd", "workdir", "cwd")
	c := corev1.Container{
		Name:            "downloader",
		Image:           TryKeysDef(r.images.Base, downloader.Config, "image"),
		WorkingDir:      workDir,
		VolumeMounts:    []corev1.VolumeMount{{Name: "data", MountPath: workDir}},
		ImagePullPolicy: corev1.PullPolicy(TryKeysDef(string(corev1.PullIfNotPresent), downloader.Config, "pull-policy")),
	}
	switch downloader.Kind {
	case StorageDownloaderKindDefault, "":
		if script := TryKeys(downloader.Config, "script", "shell"); script != "" {
			c.Command = []string{"/bin/sh", "-c"}
			c.Args = []string{script}
		} else {
			c.Command = strings.Fields(TryKeys(downloader.Config, "command"))
			c.Args = strings.Fields(TryKeys(downloader.Config, "args"))
		}
	case StorageDownloaderKindURI:
		if err := completeWgetContainer(&c, downloader.Config, r.images.Base); err != nil {
			return err
		}
	case StorageDownloaderKindGit:
		if err := completedGitContainer(&c, downloader.Config, r.images.Git); err != nil {
			return err
		}
	case StorageDownloaderKindHuggingFace:
		if err := completeHFContainer(&c, r.images.HuggingFace, downloader.Config); err != nil {
			return err
		}
	case StorageDownloaderKindModelScope:
		if err := completeModelScopeContainer(&c, r.images.ModelScope, downloader.Config); err != nil {
			return err
		}
	case StorageDownloaderKindMoha:
		if err := completeMohaContainer(&c, r.images.HuggingFace, downloader.Config); err != nil {
			return err
		}
	case StorageDownloaderKindPythonEnv: // init workspace python env job
		if err := completeCondaContainer(&c, downloader.Config, r.images.PythonEnv); err != nil {
			return err
		}
	case StorageDownloaderKindCopyData: // copy data job
		storagset := &StorageSet{}
		if err := r.Storage.Scope(base.ScopeTenant(downloader.Tenant)).Get(ctx, storagesetName, storagset); err != nil {
			return fmt.Errorf("failed to get storage set %s: %w", storagesetName, err)
		}
		if err := completdCopyContainer(&c, storagset, downloader.Config, r.images.CopyData); err != nil {
			return err
		}
	case StorageDownloaderKindModelPub: // model publish job
		c.Image = TryKeysDef(r.images.Git, downloader.Config, "image", "docker")
		c.Command = []string{"tail", "-f", "/dev/null"}

	case StorageDownloaderKindModelPubTask:
		if err := completedModulePubContainer(&c, downloader.Config, r.images.ModulePubTask); err != nil {
			return err
		}
	case StorageDownloaderKindGeneralization:
		c.Image = TryKeysDef(r.images.Generalization, downloader.Config, "image", "docker")
		for k, v := range downloader.Config {
			c.Args = append(c.Args, fmt.Sprintf("--%s=%s", k, v))
		}
	case StorageDownloaderKindWarmUp: // warm up data
		return fmt.Errorf("warmup not supported")
	case StorageDownloaderKindClone: // clone data
		if err := completedCloneContainer(&c, downloader.Config, r.images.Base); err != nil {
			return fmt.Errorf("failed to complete clone container: %w", err)
		}
	}
	podTemplate.Spec = corev1.PodSpec{
		RestartPolicy: corev1.RestartPolicyOnFailure,
		Containers:    []corev1.Container{c},
		Volumes: []corev1.Volume{
			{
				Name: "data",
				VolumeSource: corev1.VolumeSource{PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
					ClaimName: pvcName,
				}},
			},
		},
		// set downloader pod to non root user to avoid permission issues
		// if downloader run as root user, the users in the mounted pvc could not access the data if it's not root user
		SecurityContext: &corev1.PodSecurityContext{
			RunAsUser:  ptr.To(int64(1000)),
			RunAsGroup: ptr.To(int64(1000)),
			FSGroup:    ptr.To(int64(1000)),
		},
	}
	return nil
}

func (d DownloaderReconciler) RemoveDownloader(ctx context.Context, downloader *Downloader) error {
	jobname, jobnamespace := GenerateDownloaderJobName(downloader), d.DefaultNamespace

	job := &batchv1.Job{ObjectMeta: metav1.ObjectMeta{Name: jobname, Namespace: jobnamespace}}
	ref, err := d.GetStorageSetCluster(ctx, downloader.Tenant, downloader.Storageset)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return fmt.Errorf("failed to get storage set cluster: %w", err)
	}
	kubes, err := d.CloudClient.GetKubernetes(ctx, ref)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil // cluster not found, nothing to do
		}
		return fmt.Errorf("failed to get kubernetes client for storage set %s: %w", downloader.Storageset, err)
	}
	cli := kubes.Client()
	// Delete the job in the cluster
	if err := client.IgnoreNotFound(cli.Delete(ctx, job)); err != nil {
		return fmt.Errorf("failed to delete job: %w", err)
	}
	return nil
}

func StoragesetIdentity(tenant, name string) string {
	return fmt.Sprintf("%s-%s", tenant, name)
}

func completeWgetContainer(c *corev1.Container, config map[string]string, image string) error {
	url := TryKeys(config, "url", "uri", "urls")
	if url == "" {
		return fmt.Errorf("url not provided")
	}
	c.Image = TryKeysDef(image, config, "image", "docker")
	c.ImagePullPolicy = corev1.PullAlways
	c.Command = []string{"/bin/sh", "-c"}
	c.Args = []string{fmt.Sprintf(`wget '%s'`, url)}
	return nil
}

func completeMohaContainer(c *corev1.Container, haimage string, config map[string]string) error {
	giturl := TryKeysDef("https://moha.xiaoshiai.cn", config, "url", "addr")
	var mohaType string
	typ := TryKeysDef("model", config, "type", "repo-type")
	switch typ {
	case pai.LabelStorageSetKindModelSet:
		mohaType = "/models"
	case pai.LabelStorageSetKindDataSet:
		mohaType = "/datasets"
	}
	names := strings.SplitN(TryKeys(config, "name", "repo"), "/", 2)
	if len(names) < 2 {
		return fmt.Errorf("invalid model/dataset name %s", TryKeys(config, "name", "repo"))
	}
	repoOri, repoName := names[0], names[1]
	repoName += ".git"
	realUrl, err := url.JoinPath(giturl, repoOri, mohaType, repoName)
	if err != nil {
		return err
	}
	config["url"] = realUrl // update url in config
	return completedGitContainer(c, config, haimage)
}

func completeModelScopeContainer(c *corev1.Container, image string, config map[string]string) error {
	giturl := TryKeysDef("https://www.modelscope.cn", config, "url", "addr")
	typ := TryKeysDef("model", config, "type", "repo-type")
	if typ == "dataset" {
		giturl += "/datasets"
	}
	giturl += "/" + TryKeys(config, "name", "repo")
	config["url"] = giturl + ".git" // update url in config
	return completedGitContainer(c, config, image)
}

func completeHFContainer(c *corev1.Container, hfimage string, config map[string]string) error {
	if config == nil {
		config = map[string]string{}
	}
	giturl := TryKeysDef("https://huggingface.co", config, "url", "addr")
	typ := TryKeysDef("model", config, "type", "repo-type")
	switch typ {
	case pai.LabelStorageSetKindDataSet:
		giturl += "/datasets"
	}
	giturl += "/" + TryKeys(config, "name", "repo")
	gitu, err := url.Parse(giturl)
	if err != nil {
		return fmt.Errorf("invalid git url: %s", err)
	}
	config["url"] = gitu.String() // update url in config
	return completedGitContainer(c, config, hfimage)
}

func completedGitContainer(c *corev1.Container, config map[string]string, gitimage string) error {
	giturl := TryKeys(config, "url", "addr")
	if giturl == "" {
		return fmt.Errorf("git url not provided")
	}
	gitu, err := url.Parse(giturl)
	if err != nil {
		return fmt.Errorf("invalid git url: %s", err)
	}
	switch gitu.Scheme {
	case "http", "https":
		username, password := TryKeys(config, "username", "user"), TryKeys(config, "password", "token")
		gitu.User = url.UserPassword(username, password)
	case "ssh", "git+ssh", "":
		sshkey := TryKeys(config, "ssh-key")
		_ = sshkey
		return fmt.Errorf("ssh not supported yet")
	}
	c.Image = TryKeysDef(gitimage, config, "image", "docker") // allow override git image
	c.ImagePullPolicy = corev1.PullAlways
	c.Args = []string{
		gitu.String(),                           // git url
		TryKeys(config, "branch", "ref", "tag"), // branch
		TryKeysDef(".", config, "dest", "dir"),  // dest
	}
	return nil
}

func completdCopyContainer(c *corev1.Container, storagset *StorageSet, config map[string]string, copyimage string) error {
	c.Image = TryKeysDef(copyimage, config, "image")
	srcEndpoint := storagset.S3.Endpoint
	srcAccessKey, srcSecretKey := storagset.S3.ReadOnlyAccessKeyID, storagset.S3.ReadOnlySecretAccessKey
	srcBucket := StoragesetIdentity(storagset.Tenant, storagset.Name)
	srcAlias := generateCopyObjectAlias(srcAccessKey, srcSecretKey, srcEndpoint)

	destEndpoint, destBucket := parseVirtualHostStyleToPathStyle(TryKeys(config, "endpoint"), TryKeys(config, "bucket"))
	destAccessKey, destSecretKey := TryKeys(config, "accessKey"), TryKeys(config, "secretKey")
	destAlias := generateCopyObjectAlias(destAccessKey, destSecretKey, destEndpoint)

	command := fmt.Sprintf(`mc mirror pai-src/%s pai-tgt/%s --json`, srcBucket, destBucket)
	if TryKeys(config, "overwrite") == "true" {
		command += " --overwrite"
	}
	c.Command = []string{"/bin/sh", "-c"}
	c.Args = []string{command}
	c.Env = append(c.Env, []corev1.EnvVar{
		{Name: "MC_ALIAS_SRC", Value: srcAlias},
		{Name: "MC_ALIAS_TGT", Value: destAlias},
	}...)
	return nil
}

func generateCopyObjectAlias(ak, sk, endpoint string) string {
	u, e := url.Parse(endpoint)
	if e != nil {
		return endpoint
	}
	u.User = url.UserPassword(ak, sk)
	return u.String()
}

func parseVirtualHostStyleToPathStyle(endpoint, bucket string) (string, string) {
	if !strings.HasPrefix(endpoint, "http") {
		endpoint = "http://" + endpoint
	}
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		return endpoint, bucket
	}
	hostParts := strings.Split(parsedURL.Host, ".")
	if len(hostParts) > 2 && hostParts[0] == bucket {
		// host style,need parse to path style
		return fmt.Sprintf("%s://%s", parsedURL.Scheme, strings.Join(hostParts[1:], ".")), bucket
	}
	return endpoint, bucket
}

func completedModulePubContainer(c *corev1.Container, config map[string]string, image string) error {
	if err := completedGitContainer(c, config, image); err != nil {
		return err
	}
	if TryKeys(config, "username", "user") == "" {
		return fmt.Errorf("username not provided")
	}
	force := TryKeys(config, "force_push", "force")
	c.Args = append(c.Args, force)
	commit := TryKeys(config, "commit_info", "commit")
	if commit == "" {
		commit = "Initial commit"
	}
	c.Args = append(c.Args, commit)
	return nil
}

func completeCondaContainer(c *corev1.Container, config map[string]string, image string) error {
	workDir := "/datas/conda"
	scriptlines := []string{}
	if reset := TryKeys(config, "reset"); reset == "true" {
		scriptlines = append(scriptlines, fmt.Sprintf("rm -rf %s/*", workDir))
	}
	// tensorflow==2.3.1;fastapi==0.63.0
	if reqs := TryKeys(config, "requir", "requires"); reqs != "" {
		requirements := strings.Builder{}
		for i := range strings.SplitSeq(reqs, ";") {
			i = strings.TrimSpace(i)
			if i == "" {
				continue
			}
			requirements.WriteString(i + "\n")
		}
		scriptlines = append(scriptlines, fmt.Sprintf("echo '%s' > requirements.txt", requirements.String()))
	}
	{
		pythonVersion := TryKeys(config, "version", "versions")
		pipServer := TryKeysDef("https://mirrors.aliyun.com/pypi/simple/", config, "pip-server")
		var source string
		condaresource := TryKeys(config, "conda", "condas")
		for i := range strings.SplitSeq(condaresource, ";") {
			source += fmt.Sprintf("%s ", i)
		}
		command := fmt.Sprintf("/conda.sh %s %s %s requirements.txt %s", workDir, pythonVersion, pipServer, source)
		scriptlines = append(scriptlines, command)
	}

	c.Image = TryKeysDef(image, config, "image")
	c.VolumeMounts = []corev1.VolumeMount{{Name: "data", MountPath: workDir}}
	c.WorkingDir = workDir
	c.Command = []string{"/bin/bash", "-c"}
	c.Args = []string{strings.Join(scriptlines, " && ")}
	return nil
}

func completedCloneContainer(c *corev1.Container, config map[string]string, image string) error {
	c.Image = TryKeysDef(image, config, "image")
	c.VolumeMounts = []corev1.VolumeMount{{Name: "data", MountPath: "/data"}}
	c.WorkingDir = "/data"
	c.Command = []string{"/app/clone"}
	c.Args = []string{
		"--origin=" + TryKeys(config, "origin"),
		"--current=" + TryKeysDef("", config, "current"),
		"--mountpod-namespace=" + TryKeysDef("juicefs-system", config, "mountpod-namespace"),
		"--tenant=" + TryKeysDef("", config, "tenant"),
	}
	return nil
}
