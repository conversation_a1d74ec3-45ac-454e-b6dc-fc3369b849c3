package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"sync"

	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
)

type StorageClusterProviderHolder interface {
	GetProvider(ctx context.Context, clustername string) (provider.Provider, error)
	Sync(ctx context.Context, cluster *StorageCluster) error
	Remove(ctx context.Context, clustername string) error
}

const (
	StorageClusterFinalizer = "storagecluster." + common.GroupPrefix + "/finalizer"
)

func NewStorageClusterController(ctx context.Context, storage store.Store, info StorageClusterProviderHolder) (*controller.Controller, error) {
	rec := &StorageClusterReconciler{
		Client:    storage,
		Holder:    info,
		IsManager: true,
	}
	better := controller.NewBetterReconciler(rec,
		storage,
		controller.WithFinalizer(StorageClusterFinalizer),
		controller.WithAutosetStatus())
	c := controller.
		NewController("storageclusters", better).
		Watch(controller.NewStoreSource(storage, &StorageCluster{}))
	return c, nil
}

// NewStorageClusterInfoController only watches the storage clusters and updates cluster provider cache.
func NewStorageClusterInfoController(ctx context.Context, storage store.Store, info StorageClusterProviderHolder) (*controller.Controller, error) {
	rec := &StorageClusterReconciler{
		Client: storage,
		Holder: info,
	}
	better := controller.NewBetterReconciler(rec, storage)
	c := controller.
		NewController("storageclusters-info", better).
		Watch(controller.NewStoreSource(storage, &StorageCluster{}))
	return c, nil
}

type StorageClusterReconciler struct {
	Client store.Store
	Holder StorageClusterProviderHolder
	// IsManager indicates if this reconciler is a manager for storage clusters
	// only managers can update the status of storage clusters
	IsManager bool
}

func (c *StorageClusterReconciler) Initialize(ctx context.Context) error {
	list := store.List[StorageCluster]{}
	if err := c.Client.List(ctx, &list); err != nil {
		return err
	}
	for _, cluster := range list.Items {
		if err := c.Holder.Sync(ctx, &cluster); err != nil {
			log.Error(err, "init storage cluster provider", "name", cluster.Name)
		}
	}
	return nil
}

func (c *StorageClusterReconciler) Sync(ctx context.Context, key *StorageCluster) (controller.Result, error) {
	if c.IsManager {
		if err := c.Holder.Sync(ctx, key); err != nil {
			key.Status.Ready = false
			log.Error(err, "sync storage cluster provider", "name", key.Name)
			return controller.Result{}, err
		}
		key.Status.Ready = true
	} else {
		if err := c.Holder.Sync(ctx, key); err != nil {
			log.Error(err, "sync storage cluster provider", "name", key.Name)
		}
	}
	return controller.Result{}, nil
}

func (c *StorageClusterReconciler) Remove(ctx context.Context, key *StorageCluster) (controller.Result, error) {
	if err := c.Holder.Remove(ctx, key.Name); err != nil {
		log.Info("storage cluster remove", "name", key.Name)
	}
	return controller.Result{}, nil
}

var _ StorageClusterProviderHolder = &StorageClusterManager{}

type StorageClusterManager struct {
	factory provider.ProviderFactory
	cache   map[string]ConfigHashProvider
	mu      sync.RWMutex
}

type ConfigHashProvider struct {
	CancelFunc context.CancelFunc
	ConfigHash uint32
	Provider   provider.Provider
}

func NewStorageClusterManager(factory provider.ProviderFactory) *StorageClusterManager {
	return &StorageClusterManager{
		factory: factory,
		mu:      sync.RWMutex{},
		cache:   make(map[string]ConfigHashProvider),
	}
}

func (m *StorageClusterManager) GetProvider(ctx context.Context, clustername string) (provider.Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if provider, ok := m.cache[clustername]; ok {
		return provider.Provider, nil
	}
	return nil, errors.NewNotFound("storage cluster provider", clustername)
}

func (m *StorageClusterManager) Sync(ctx context.Context, cluster *StorageCluster) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	confighash := ComputeHash(cluster.Config)

	existstprovider, ok := m.cache[cluster.Name]
	if ok && existstprovider.ConfigHash == confighash {
		return nil
	}
	// maybe the provider has changed, we need to recreate it
	providerctx, cancel := context.WithCancel(ctx)
	provider, err := m.factory.NewProvider(providerctx, string(cluster.Provider), provider.NewProviderOptions{Config: cluster.Config})
	if err != nil {
		log.Error(err, "create storage cluster provider", "name", cluster.Name, "provider", cluster.Provider)
		cancel()
		return err
	}
	newprovider := ConfigHashProvider{
		ConfigHash: confighash,
		CancelFunc: cancel,
		Provider:   provider,
	}
	m.cache[cluster.Name] = newprovider
	// close the old provider if it exists
	if ok {
		if existstprovider.CancelFunc != nil {
			existstprovider.CancelFunc()
		}
		if err := existstprovider.Provider.Destroy(); err != nil {
			log.Error(err, "destroy old storage cluster provider", "name", cluster.Name)
		}
	}
	return nil
}

func (m *StorageClusterManager) Remove(ctx context.Context, clustername string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if provider, ok := m.cache[clustername]; ok {
		if provider.CancelFunc != nil {
			provider.CancelFunc()
		}
		if err := provider.Provider.Destroy(); err != nil {
			log.Error(err, "destroy storage cluster provider", "name", clustername)
		}
		delete(m.cache, clustername)
		return nil
	}
	return errors.NewNotFound("storage cluster provider", clustername)
}

func ComputeHash(values ...any) uint32 {
	hasher := fnv.New32a()
	hasher.Reset()
	for _, value := range values {
		data, err := json.Marshal(value)
		if err != nil {
			fmt.Fprintf(hasher, "%#v", value)
		} else {
			hasher.Write(data)
		}
		hasher.Write([]byte{0})
	}
	return hasher.Sum32()
}
