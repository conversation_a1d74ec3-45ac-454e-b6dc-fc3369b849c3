package storage

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func (a *API) ListStorageCluster(w http.ResponseWriter, r *http.Request) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		var dest []*StorageCluster
		if err := list(ctx, kube, &paiv1beta1.StorageClusterList{}, dest, func() *StorageCluster {
			return &StorageCluster{}
		}); err != nil {
			return nil, err
		}
		listopts := api.GetListOptions(r)
		namefunc := func(item *StorageCluster) string { return item.Name }
		timefunc := func(item *StorageCluster) time.Time { return item.UpdatedAt }
		paged := api.PageFrom(dest, listopts.Page, listopts.Size, nil,
			api.SortByFunc(listopts.Sort, namefunc, timefunc))
		return paged, nil
	})
}

func (a *API) GetStorageCluster(w http.ResponseWriter, r *http.Request) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		storagecluster := api.Path(r, "storagecluster", "")
		if storagecluster == "" {
			return nil, errors.NewBadRequest("storagecluster name is required")
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		dest := &StorageCluster{}
		if err := get(ctx, kube, &paiv1beta1.StorageCluster{}, dest, storagecluster, ""); err != nil {
			return nil, err
		}
		return dest, nil
	})
}

func (a *API) CreateStorageCluster(w http.ResponseWriter, r *http.Request) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		storagecluster := &StorageCluster{}
		if err := api.Body(r, storagecluster); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageCluster(storagecluster); err != nil {
			return nil, err
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, create(ctx, kube, storagecluster, &paiv1beta1.StorageCluster{})
	})
}

func validateStorageCluster(storagecluster *StorageCluster) error {
	// if storagecluster.Name == "" {
	// 	return errors.NewBadRequest("storagecluster name is required")
	// }
	// if storagecluster.Provider == "" {
	// 	return errors.NewBadRequest("provider is required")
	// }
	return nil
}

func (a *API) UpdateStorageCluster(w http.ResponseWriter, r *http.Request) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		storagecluster := api.Path(r, "storagecluster", "")
		if storagecluster == "" {
			return nil, errors.NewBadRequest("storagecluster name is required")
		}
		storageclusterObj := &StorageCluster{}
		if err := api.Body(r, storageclusterObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageCluster(storageclusterObj); err != nil {
			return nil, err
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, update(ctx, kube, storageclusterObj, &paiv1beta1.StorageCluster{}, storagecluster, "")
	})
}

func (a *API) DeleteStorageCluster(w http.ResponseWriter, r *http.Request) {
	cluster.OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		storagecluster := api.Path(r, "storagecluster", "")
		if storagecluster == "" {
			return nil, errors.NewBadRequest("storagecluster name is required")
		}
		kube, err := a.CloudClient.GetKubernetes(ctx, ref)
		if err != nil {
			return nil, err
		}
		return nil, delete(ctx, kube, &paiv1beta1.StorageCluster{}, storagecluster, "")
	})
}

func (a *API) StorageClusterGroup() api.Group {
	return api.
		NewGroup("/storageclusters").
		Tag("StorageCluster").
		Route(
			api.GET("").
				Operation("list storage clusters").
				Param(api.PageParams...).
				To(a.ListStorageCluster).
				Response([]StorageCluster{}),

			api.GET("/{storagecluster}").
				Operation("get storage cluster").
				To(a.GetStorageCluster).
				Response(StorageCluster{}),

			api.POST("").
				Operation("create storage cluster").
				To(a.CreateStorageCluster).
				Param(api.BodyParam("storagecluster", StorageCluster{})).
				Response(StorageCluster{}),

			api.PUT("/{storagecluster}").
				Operation("update storage cluster").
				To(a.UpdateStorageCluster).
				Param(api.BodyParam("storagecluster", StorageCluster{})).
				Response(StorageCluster{}),

			api.DELETE("/{storagecluster}").
				Operation("delete storage cluster").
				To(a.DeleteStorageCluster),
		)
}
