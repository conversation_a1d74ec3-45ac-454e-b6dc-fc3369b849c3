package storage

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type StorageCluster struct {
	store.ObjectMeta `json:",inline"`
	// Provider is the storage cluster provider, e.g., "Juicefs", "Cephfs"
	Provider StorageClusterProvider `json:"provider" validate:"required"`
	// Cluster is the nearby cluster that this storage cluster belongs to.
	// It is used to run downloaders to the cluster.
	Cluster          store.ObjectReference        `json:"cluster"`
	Config           map[string]string            `json:"config"`
	MountOption      MountOptions                 `json:"mount_option"`
	AvailableRegions []store.LocalObjectReference `json:"availableRegions,omitempty"` // AvailableRegions is the list of regions where this storage cluster is available
	Status           StorageClusterStatus         `json:"status"`
}

type MountOptions struct {
	MountCmd   string   `json:"mount_cmd"`
	Source     string   `json:"source"`
	Options    []string `json:"options,omitempty"`
	MountFlags []string `json:"mount_flags,omitempty"`
}

type StorageClusterStatus struct {
	Ready   bool   `json:"ready"`             // Ready indicates if the storage cluster is ready to use
	Message string `json:"message,omitempty"` // Message is the status message of the storage cluster
}

type StorageClusterProvider string

const (
	StorageClusterProviderNFS     StorageClusterProvider = "nfs"
	StorageClusterProviderCephfs  StorageClusterProvider = "cephfs"
	StorageClusterProviderJuicefs StorageClusterProvider = "juicefs"
)

func (a *API) ListStorageCluster(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		list := store.List[StorageCluster]{}
		return base.GenericList(r, a.Store, &list)
	})
}

func (a *API) GetStorageCluster(w http.ResponseWriter, r *http.Request) {
	a.onStorageCluster(w, r, func(ctx context.Context, storagecluster string) (any, error) {
		return base.GenericGet(r, a.Store, &StorageCluster{}, storagecluster)
	})
}

func (a *API) CreateStorageCluster(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		storagecluster := &StorageCluster{}
		if err := api.Body(r, storagecluster); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageCluster(storagecluster); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, a.Store, storagecluster)
	})
}

func validateStorageCluster(storagecluster *StorageCluster) error {
	if storagecluster.Name == "" {
		return errors.NewBadRequest("storagecluster name is required")
	}
	if storagecluster.Provider == "" {
		return errors.NewBadRequest("provider is required")
	}
	return nil
}

func (a *API) UpdateStorageCluster(w http.ResponseWriter, r *http.Request) {
	a.onStorageCluster(w, r, func(ctx context.Context, storagecluster string) (any, error) {
		storageclusterObj := &StorageCluster{}
		if err := api.Body(r, storageclusterObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageCluster(storageclusterObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, a.Store, storageclusterObj, storagecluster)
	})
}

func (a *API) DeleteStorageCluster(w http.ResponseWriter, r *http.Request) {
	a.onStorageCluster(w, r, func(ctx context.Context, storagecluster string) (any, error) {
		return base.GenericDelete(r, a.Store, &StorageCluster{}, storagecluster)
	})
}

func (a *API) onStorageCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storagecluster string) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		storagecluster := api.Path(r, "storagecluster", "")
		if storagecluster == "" {
			return nil, errors.NewBadRequest("storagecluster name is required")
		}
		return fn(ctx, storagecluster)
	})
}

func (a *API) StorageClusterGroup() api.Group {
	return api.
		NewGroup("/storagecluster").
		Tag("StorageCluster").
		Route(
			api.GET("").
				Operation("list storage clusters").
				Param(api.PageParams...).
				To(a.ListStorageCluster).
				Response(store.List[StorageCluster]{}),

			api.GET("/{storagecluster}").
				Operation("get storage cluster").
				To(a.GetStorageCluster).
				Response(StorageCluster{}),

			api.POST("").
				Operation("create storage cluster").
				To(a.CreateStorageCluster).
				Param(api.BodyParam("storagecluster", StorageCluster{})).
				Response(StorageCluster{}),

			api.PUT("/{storagecluster}").
				Operation("update storage cluster").
				To(a.UpdateStorageCluster).
				Param(api.BodyParam("storagecluster", StorageCluster{})).
				Response(StorageCluster{}),

			api.DELETE("/{storagecluster}").
				Operation("delete storage cluster").
				To(a.DeleteStorageCluster),
		)
}
