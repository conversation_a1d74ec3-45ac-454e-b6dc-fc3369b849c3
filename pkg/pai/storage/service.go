package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/store"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func processList(list client.ObjectList) ([]client.Object, error) {
	var oss []client.Object
	listValue := reflect.ValueOf(list).Elem()
	itemsField := listValue.FieldByName("Items")
	if !itemsField.IsValid() {
		return nil, fmt.Errorf("objectList does not have an Items field")
	}
	if itemsField.Kind() != reflect.Slice {
		return nil, fmt.Errorf("Items field is not a slice")
	}
	for i := 0; i < itemsField.Len(); i++ {
		item := itemsField.Index(i)
		var obj client.Object
		if item.Kind() == reflect.Ptr {
			obj = item.Interface().(client.Object)
		} else {
			obj = item.Addr().Interface().(client.Object)
		}
		oss = append(oss, obj)
	}
	return oss, nil
}

type FromTo interface {
	From(obj client.Object) error
	To(obj client.Object) error
}

func list[T client.ObjectList, K FromTo](ctx context.Context, kube cluster.Kubernetes, source T, dest []K, newFunc func() K, opts ...client.ListOption) error {
	if err := kube.Client().List(ctx, source, opts...); err != nil {
		return err
	}
	objs, err := processList(source)
	if err != nil {
		return err
	}
	dest = make([]K, 0, len(objs))
	for i, item := range objs {
		dest[i] = newFunc()
		if err := dest[i].From(item); err != nil {
			return err
		}
	}
	return nil
}

func get[T client.Object, K FromTo](ctx context.Context, kube cluster.Kubernetes, source T, dest K, name, namespace string) error {
	source.SetName(name)
	source.SetNamespace(namespace)
	if err := kube.Client().Get(ctx, client.ObjectKeyFromObject(source), source); err != nil {
		return err
	}
	return dest.From(source)
}

func create[T client.Object, K FromTo](ctx context.Context, kube cluster.Kubernetes, source K, dest T) error {
	if err := source.To(dest); err != nil {
		return err
	}
	return kube.Client().Create(ctx, dest)
}

func createWithOwnerReference[T client.Object, K FromTo, M client.Object](ctx context.Context, kube cluster.Kubernetes, source K, dest T, ref M) error {
	if err := source.To(dest); err != nil {
		return err
	}
	dest.SetOwnerReferences([]v1.OwnerReference{
		{
			APIVersion: ref.GetObjectKind().GroupVersionKind().GroupVersion().String(),
			Kind:       ref.GetObjectKind().GroupVersionKind().Kind,
			Name:       ref.GetName(),
			Controller: ptr.To(true),
		},
	})
	return kube.Client().Create(ctx, dest)
}

func delete[T client.Object](ctx context.Context, kube cluster.Kubernetes, source T, name, namespace string) error {
	source.SetName(name)
	source.SetNamespace(namespace)
	return client.IgnoreNotFound(kube.Client().Delete(ctx, source))
}

func update[T client.Object, K FromTo](ctx context.Context, kube cluster.Kubernetes, source K, dest T, name, namespace string) error {
	if err := source.To(dest); err != nil {
		return err
	}
	dest.SetName(name)
	dest.SetNamespace(namespace)
	return kube.Client().Update(ctx, dest)
}

type Base struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	DisplayName string            `json:"displayName,omitempty"`
	Creator     string            `json:"creator"`
	Description string            `json:"description"`
	CreatedAt   time.Time         `json:"createdAt"`
	UpdatedAt   time.Time         `json:"updatedAt"`
	DeletedAt   *time.Time        `json:"deletedAt"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

func (b *Base) From(obj client.Object) error {
	b.Name = obj.GetName()
	b.Namespace = obj.GetNamespace()
	b.Labels = obj.GetLabels()
	annotation := obj.GetAnnotations()
	b.Annotations = annotation
	b.DisplayName = annotation[AnnotationDisplayName]
	b.Creator = annotation[AnnotationCreator]
	b.Description = annotation[AnnotationDescription]
	b.CreatedAt = obj.GetCreationTimestamp().Time
	b.UpdatedAt = obj.GetCreationTimestamp().Time
	if deletion := obj.GetDeletionTimestamp(); deletion != nil {
		b.DeletedAt = &deletion.Time
	}
	b.Labels = obj.GetLabels()
	b.Annotations = obj.GetAnnotations()
	return nil
}

func (b *Base) To(obj client.Object) error {
	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = map[string]string{}
	}
	obj.SetName(b.Name)
	obj.SetNamespace(b.Namespace)
	if b.DisplayName != "" {
		annotations[AnnotationDisplayName] = b.DisplayName
	}
	if b.Creator != "" {
		annotations[AnnotationCreator] = b.Creator
	}
	if b.Description != "" {
		annotations[AnnotationDescription] = b.Description
	}
	obj.SetAnnotations(annotations)
	obj.SetLabels(b.Labels)
	return nil
}

type StorageClusterStatus struct {
	Message string `json:"message"`
	// TotalVolume int32  `json:"totalVolume"`
	// UsedBytes   int64  `json:"usedBytes"`
}

var _ FromTo = &StorageCluster{}

type StorageCluster struct {
	Base    `json:",inline"`
	Kind    string                `json:"kind"`
	Cluster store.ObjectReference `json:"cluster"`
	Values  map[string]any        `json:"values"`
	Status  StorageClusterStatus  `json:"status"`
}

func (s *StorageCluster) From(obj client.Object) error {
	ss, ok := obj.(*paiv1beta1.StorageCluster)
	if !ok {
		return fmt.Errorf("not a storagecluster")
	}
	s.Base.From(ss)
	s.Kind = string(ss.Spec.Provider)
	if err := json.Unmarshal(ss.Spec.Values.Raw, &s.Values); err != nil {
		return err
	}
	s.Status.Message = ss.Status.Message
	// s.Status.TotalVolume = ss.Status.TotalVolume
	// s.Status.UsedBytes = ss.Status.UsedBytes
	return nil
}
func (s *StorageCluster) To(obj client.Object) error {
	ss, ok := obj.(*paiv1beta1.StorageCluster)
	if !ok {
		return fmt.Errorf("not a storagecluster")
	}
	s.Base.To(ss)
	values, err := json.Marshal(s.Values)
	if err != nil {
		return err
	}
	ss.ObjectMeta = v1.ObjectMeta{
		Name: s.Name,
	}
	ss.Spec = paiv1beta1.StorageClusterSpec{
		Provider: paiv1beta1.StorageClusterProvider(s.Kind),
		Values:   runtime.RawExtension{Raw: values},
	}

	return nil
}

var _ FromTo = &StorageVolume{}

type StorageVolumeStatus struct {
	StoragePath string `json:"storagePath"`
	Message     string `json:"message"`
}
type StorageVolumeAccount struct {
	AccessKey string `json:"accessKey"`
	SecretKey string `json:"secretKey"`
	Role      string `json:"role"`
}

type StorageVolume struct {
	Base           `json:",inline"`
	StorageCluster string               `json:"storageCluster"`
	Workspace      string               `json:"workspace"`
	Size           string               `json:"size"`
	Manager        StorageVolumeAccount `json:"manager"`
	ReadOnly       StorageVolumeAccount `json:"readOnly"`
	Status         StorageVolumeStatus  `json:"status"`
}

func (s *StorageVolume) From(obj client.Object) error {
	sv, ok := obj.(*paiv1beta1.StorageVolume)
	if !ok {
		return fmt.Errorf("not a storagevolume")
	}
	s.Base.From(sv)
	s.Workspace = sv.Namespace
	s.StorageCluster = sv.Spec.ClusterReference.Name
	s.Size = sv.Spec.Capability.String()
	for _, account := range sv.Spec.Accounts {
		if account.Role == paiv1beta1.S3AccountRoleRW {
			s.Manager = StorageVolumeAccount{
				AccessKey: account.AccessKey,
				SecretKey: account.SecretKey,
				Role:      string(account.Role),
			}
		} else {
			s.ReadOnly = StorageVolumeAccount{
				AccessKey: account.AccessKey,
				SecretKey: account.SecretKey,
				Role:      string(account.Role),
			}
		}
	}
	s.Status.Message = sv.Status.Message
	s.Status.StoragePath = sv.Status.StoragePath
	return nil
}

func (s *StorageVolume) To(obj client.Object) error {
	sv, ok := obj.(*paiv1beta1.StorageVolume)
	if !ok {
		return fmt.Errorf("not a storagevolume")
	}
	s.Base.To(sv)
	sv.ObjectMeta = v1.ObjectMeta{
		Name:      s.Name,
		Namespace: s.Workspace,
	}
	sv.Spec = paiv1beta1.StorageVolumeSpec{
		ClusterReference: corev1.ObjectReference{
			Name: s.StorageCluster,
		},
		Capability: resource.MustParse(s.Size),
		Accounts: []paiv1beta1.S3Account{
			{
				AccessKey: s.Manager.AccessKey,
				SecretKey: s.Manager.SecretKey,
				Role:      paiv1beta1.S3AccountRole(s.Manager.Role),
			},
			{
				AccessKey: s.ReadOnly.AccessKey,
				SecretKey: s.ReadOnly.SecretKey,
				Role:      paiv1beta1.S3AccountRole(s.ReadOnly.Role),
			},
		},
	}

	return nil
}
