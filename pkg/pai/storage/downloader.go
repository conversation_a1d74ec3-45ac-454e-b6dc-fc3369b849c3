package storage

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type Downloader struct {
	store.ObjectMeta `json:",inline"`
	Tenant           string            `json:"tenant,omitempty"`
	Storageset       string            `json:"storageset,omitempty"`
	Kind             DownloaderKind    `json:"kind,omitempty"`
	Config           map[string]string `json:"config,omitempty"`
	Status           DownloaderStatus  `json:"status"`
}

type DownloaderKind string

type DownloaderStatus struct {
	Phase               DownloaderPhase   `json:"phase,omitempty"`
	Config              map[string]string `json:"config,omitempty"`
	Message             string            `json:"message,omitempty"`
	StartTimestamp      *store.Time       `json:"startTimestamp,omitempty"`
	CompletionTimestamp *store.Time       `json:"completionTimestamp,omitempty"`
	RestartCount        int32             `json:"restartCount,omitempty"`
}

type DownloaderPhase string

const (
	DownloaderPhasePending   DownloaderPhase = "Pending"
	DownloaderPhaseRunning   DownloaderPhase = "Running"
	DownloaderPhaseSucceeded DownloaderPhase = "Succeeded"
	DownloaderPhaseFailed    DownloaderPhase = "Failed"
)

func (a *API) ListDownloaders(w http.ResponseWriter, r *http.Request) {
	a.onStorageSetStorage(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := store.List[Downloader]{}
		return base.GenericList(r, storage, &list)
	})
}

func (a *API) GetDownloader(w http.ResponseWriter, r *http.Request) {
	a.onDownloader(w, r, func(ctx context.Context, storage store.Store, downloader string) (any, error) {
		return base.GenericGet(r, storage, &Downloader{}, downloader)
	})
}

func (a *API) CreateDownloader(w http.ResponseWriter, r *http.Request) {
	a.onStorageSetStorage(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		downloader := &Downloader{}
		if err := api.Body(r, downloader); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateDownloader(downloader); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, storage, downloader)
	})
}

func validateDownloader(downloader *Downloader) error {
	if downloader.Name == "" {
		return errors.NewBadRequest("downloader name is required")
	}
	// Add more validation as needed
	return nil
}

func (a *API) UpdateDownloader(w http.ResponseWriter, r *http.Request) {
	a.onDownloader(w, r, func(ctx context.Context, storage store.Store, downloader string) (any, error) {
		downloaderObj := &Downloader{}
		if err := api.Body(r, downloaderObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateDownloader(downloaderObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, storage, downloaderObj, downloader)
	})
}

func (a *API) DeleteDownloader(w http.ResponseWriter, r *http.Request) {
	a.onDownloader(w, r, func(ctx context.Context, storage store.Store, downloader string) (any, error) {
		return base.GenericDelete(r, storage, &Downloader{}, downloader)
	})
}

func (a *API) onDownloader(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, downloader string) (any, error)) {
	a.onStorageSetStorage(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		downloader := api.Path(r, "downloader", "")
		if downloader == "" {
			return nil, errors.NewBadRequest("downloader name is required")
		}
		return fn(ctx, storage, downloader)
	})
}

func (a *API) onStorageSetStorage(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		storageset := api.Path(r, "storageset", "")
		if storageset == "" {
			return nil, errors.NewBadRequest("storageset name is required")
		}
		storage := a.Store.Scope(base.ScopeTenant(tenant)).Scope(store.Scope{Resource: "storagesets", Name: storageset})
		return fn(ctx, storage)
	})
}

func (a *API) DownloadGroup() api.Group {
	return api.
		NewGroup("").
		Tag("StorageSet").
		SubGroup(
			api.NewGroup("/tenants/{tenant}/storagesets/{storageset}/downloaders").
				Route(
					api.GET("").
						Operation("list downloaders").
						Param(api.PageParams...).
						To(a.ListDownloaders).
						Response(store.List[Downloader]{}),

					api.GET("/{downloader}").
						Operation("get downloader").
						To(a.GetDownloader).
						Response(Downloader{}),

					api.POST("").
						Operation("create downloader").
						To(a.CreateDownloader).
						Param(api.BodyParam("downloader", Downloader{})).
						Response(Downloader{}),

					api.PUT("/{downloader}").
						Operation("update downloader").
						To(a.UpdateDownloader).
						Param(api.BodyParam("downloader", Downloader{})).
						Response(Downloader{}),

					api.DELETE("/{downloader}").
						Operation("delete downloader").
						To(a.DeleteDownloader),
				),
		)
}
