package juicefs

import (
	"context"
	"maps"
	"strconv"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/fs"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
)

func NewJuiceFSProvider(ctx context.Context, options map[string]string) (*JuiceFSProvider, error) {
	return &JuiceFSProvider{
		Options: options,
	}, nil
}

var _ provider.Provider = &JuiceFSProvider{}

/*
Options
pathPattern:/,
name:xxxx,
metaurl:xxxx,
storage:xxxx,
bucket:xxxx,
access-key:xxxx,
secret-key:xxxx,
format-options: trash-days=1,block-size=4096
*/
type JuiceFSProvider struct {
	Options map[string]string
}

// Init implements provider.Provider.
func (j *JuiceFSProvider) Init() error {
	panic("unimplemented")
}

// Destroy implements provider.Provider.
func (j *JuiceFSProvider) Destroy() error {
	return nil
}

// FileSystem implements provider.Provider.
func (j *JuiceFSProvider) FileSystem(options provider.ProviderPathOptions) (fs.FileSystem, error) {
	return nil, errors.NewNotImplemented("temporary not implemented")
}

// PersistentVolumeClaimContext implements provider.Provider.
func (j *JuiceFSProvider) PersistentVolumeClaimContext(options provider.ProviderPathOptions, init bool) (provider.PersistentVolumeClaimContext, error) {
	if init {
		if err := j.Init(); err != nil {
			return provider.PersistentVolumeClaimContext{}, err
		}
	}
	uniquePvName := options.TenantName + "-" + options.StorageSetName
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      options.StorageSetName + "-secret",
			Namespace: options.Namespace,
		},
		StringData: map[string]string{
			"name":           or(j.Options, "name"),
			"metaurl":        or(j.Options, "metaurl"),
			"storage":        or(j.Options, "storage"),
			"bucket":         or(j.Options, "bucket"),
			"access-key":     or(j.Options, "access-key", "accessKey"),
			"secret-key":     or(j.Options, "secret-key", "secretKey"),
			"format-options": or(j.Options, "format-options", "formatOptions"),
		},
	}
	secretref := &corev1.SecretReference{
		Name:      secret.Name,
		Namespace: secret.Namespace,
	}
	volumeattr := map[string]string{}
	maps.Copy(volumeattr, map[string]string{
		"capacity":    capacityToJuiceFSSize(options.Capacity),
		"subPath":     options.Path,
		"pathPattern": or(j.Options, "pathPattern"),
	})
	pv := corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: uniquePvName,
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: options.Capacity,
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:               "csi.juicefs.com",
					FSType:               "juicefs",
					VolumeHandle:         uniquePvName,
					VolumeAttributes:     volumeattr,
					NodePublishSecretRef: secretref,
				},
			},
		},
	}
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniquePvName,
			Namespace: options.Namespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{corev1.ResourceStorage: options.Capacity},
			},
			VolumeName:       pv.Name,
			StorageClassName: Ptr(""),
		},
	}

	ret := provider.PersistentVolumeClaimContext{
		PersistentVolumeClaim: pvc,
		PersistentVolume:      pv,
		ExtraResources:        []client.Object{secret},
	}
	return ret, nil
}

func Ptr[T any](v T) *T {
	return &v
}

func capacityToJuiceFSSize(capacity resource.Quantity) string {
	return strconv.FormatInt(capacity.Value(), 10)
}

func or(kv map[string]string, key ...string) string {
	for _, k := range key {
		if v, ok := kv[k]; ok {
			return v
		}
	}
	return ""
}
