package delegate

import (
	"context"
	"fmt"
	"strings"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/fs"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider/cephfs"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider/juicefs"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider/nfs"
)

var _ provider.Provider = &DelegateProvider{}

type DelegateProvider struct{}

// Init implements provider.Provider.
func (d *DelegateProvider) Init() error {
	panic("unimplemented")
}

// Destroy implements provider.Provider.
func (d *DelegateProvider) Destroy() error {
	return nil
}

// FileSystem implements provider.Provider.
func (d *DelegateProvider) FileSystem(options provider.ProviderPathOptions) (fs.FileSystem, error) {
	return nil, errors.NewNotImplemented("not implemented")
}

// PersistentVolumeClaimContext implements provider.Provider.
func (d *DelegateProvider) PersistentVolumeClaimContext(options provider.ProviderPathOptions, init bool) (provider.PersistentVolumeClaimContext, error) {
	return provider.PersistentVolumeClaimContext{}, errors.NewNotImplemented("not implemented")
}

var _ provider.ProviderFactory = &DelegateProviderFactory{}

func NewDelegateProviderFactory() provider.ProviderFactory {
	return &DelegateProviderFactory{}
}

type DelegateProviderFactory struct{}

// NewProvider implements provider.ProviderFactory.
func (d *DelegateProviderFactory) NewProvider(ctx context.Context, kind string, options provider.NewProviderOptions) (provider.Provider, error) {
	switch strings.ToLower(kind) {
	case "juicefs":
		return juicefs.NewJuiceFSProvider(ctx, options.Config)
	case "cephfs":
		return cephfs.NewCephFSProvider(ctx, options.Config)
	case "nfs":
		return nfs.NewNFSProvider(ctx, options.Config)
	default:
		return nil, errors.NewBadRequest(fmt.Sprintf("unknown storage provider kind: %s", kind))
	}
}
