package cephfs

import (
	"context"
	"maps"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/fs"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
)

func NewCephFSProvider(ctx context.Context, options map[string]string) (*CephFSProvider, error) {
	return &CephFSProvider{
		Options: options,
	}, nil
}

var _ provider.Provider = &CephFSProvider{}

/*
Options
userID:xxxx
userKey:xxxxx
monitors:xxxxx,xxxx,xxxx
clusterID:xxxxxx,
fsName:xxxxx

*/

type CephFSProvider struct {
	Options map[string]string
}

// Init implements provider.Provider.
func (c *CephFSProvider) Init() error {
	panic("unimplemented")
}

// Destroy implements provider.Provider.
func (c *CephFSProvider) Destroy() error {
	return nil
}

// FileSystem implements provider.Provider.
func (c *CephFSProvider) FileSystem(options provider.ProviderPathOptions) (fs.FileSystem, error) {
	return nil, errors.NewNotImplemented("temporary not implemented")
}

// PersistentVolumeClaimContext implements provider.Provider.
func (c *CephFSProvider) PersistentVolumeClaimContext(options provider.ProviderPathOptions, init bool) (provider.PersistentVolumeClaimContext, error) {
	if init {
		if err := c.Init(); err != nil {
			return provider.PersistentVolumeClaimContext{}, err
		}
	}

	uniquePvName := options.TenantName + "-" + options.StorageSetName
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      options.StorageSetName + "-secret",
			Namespace: options.Namespace,
		},
		StringData: map[string]string{
			"userID":  or(c.Options, "userID"),
			"userKey": or(c.Options, "userKey"),
		},
	}
	secretref := &corev1.SecretReference{
		Name:      secret.Name,
		Namespace: secret.Namespace,
	}
	volumeattr := map[string]string{
		"mounter":          "kernel",
		"fuseMountOptions": "debug",
		"staticVolume":     "true",
	}
	//maps.Copy(volumeattr, options.Config)
	maps.Copy(volumeattr, map[string]string{
		"rootPath": options.Path,
		// "clusterID":"xxxxx",
		// "fsName":"xxxx",
	})
	pv := corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: uniquePvName,
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: options.Capacity,
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:             "cephfs.csi.ceph.com",
					VolumeHandle:       uniquePvName,
					VolumeAttributes:   volumeattr,
					NodeStageSecretRef: secretref,
					// NodePublishSecretRef:       secretref,
					// ControllerPublishSecretRef: secretref,
					// ControllerExpandSecretRef:  secretref,
				},
			},
		},
	}
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniquePvName,
			Namespace: options.Namespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{corev1.ResourceStorage: options.Capacity},
			},
			VolumeName:       pv.Name,
			StorageClassName: Ptr(""),
		},
	}

	ret := provider.PersistentVolumeClaimContext{
		PersistentVolumeClaim: pvc,
		PersistentVolume:      pv,
		ExtraResources:        []client.Object{secret},
	}
	return ret, nil
}
func Ptr[T any](v T) *T {
	return &v
}

func or(kv map[string]string, key ...string) string {
	for _, k := range key {
		if v, ok := kv[k]; ok {
			return v
		}
	}
	return ""
}
