package cephfs

import (
	"context"
	"testing"

	"k8s.io/client-go/tools/clientcmd"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/pai/storage/provider"
)

const kubeconfigPath = "../../../../../tmp/develop.config"

func setupContainerOperation(ctx context.Context) (client.Client, error) {
	cfg, err := clientcmd.BuildConfigFromFlags("", kubeconfigPath)
	if err != nil {
		return nil, err
	}
	cli, err := cluster.NewKubernetesClients(ctx, cfg)
	if err != nil {
		return nil, err
	}
	return cli.Client(), nil
}

func TestCephfsProvider(t *testing.T) {
	cli, err := setupContainerOperation(t.Context())
	if err != nil {
		t.Fatal(err)
	}
	cephfs, err := NewCephFSProvider(t.Context(), map[string]string{})
	if err != nil {
		t.Fatal(err)
	}
	persistenVolume, err := cephfs.PersistentVolumeClaimContext(provider.ProviderPathOptions{}, true)
	if err != nil {
		t.Fatal(err)
	}
	var objects []client.Object
	objects = append(objects, persistenVolume.ExtraResources...)
	objects = append(objects, &persistenVolume.PersistentVolume)
	objects = append(objects, &persistenVolume.PersistentVolumeClaim)
	for _, obj := range objects {
		if _, err := controllerutil.CreateOrUpdate(t.Context(), cli, obj, func() error {
			return nil
		}); err != nil {
			t.Fatal(err)
		}
	}
	t.Log("success")
}
