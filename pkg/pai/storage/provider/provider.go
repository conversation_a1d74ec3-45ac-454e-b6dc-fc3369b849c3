package provider

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/fs"
)

type NewProviderOptions struct {
	Config map[string]string
}

type ProviderFactory interface {
	// NewProvider creates a new Provider based on the kind and options provided.
	NewProvider(ctx context.Context, kind string, options NewProviderOptions) (Provider, error)
}

type Provider interface {
	// FileSystem returns a FileSystem implementation for the storage provider.
	// some providers may not support this method, in which case it should return an error.
	FileSystem(options ProviderPathOptions) (fs.FileSystem, error)

	// PersistentVolumeClaimContext returns a PersistentVolumeClaimContext for the storage provider.
	// consumers can use this context to create a PersistentVolumeClaim and use it in their applications.
	PersistentVolumeClaimContext(options ProviderPathOptions, init bool) (PersistentVolumeClaimContext, error)

	// Destroy cleans up any resources associated with the provider.
	// this method will be called when the storage cluster config changes or deleted.
	Destroy() error

	Init() error
}

type ProviderPathOptions struct {
	StorageSetName string // Name is the name of pvc
	TenantName     string
	Annotations    map[string]string // Annotations are the annotations to be added to the PersistentVolumeClaim.
	Labels         map[string]string // Labels are the labels to be added to the PersistentVolumeClaim.
	Config         map[string]string // Config is the configuration for the storage provider.
	Capacity       resource.Quantity // Capacity is the requested storage capacity for the PersistentVolumeClaim.
	// Path is the path to the storage provider.
	Path      string
	Namespace string
}

// PersistentVolumeClaimContext defines the resources needed to create a PersistentVolumeClaim.
// It use static provisioning to create a PersistentVolumeClaim and a PersistentVolume.
type PersistentVolumeClaimContext struct {
	PersistentVolumeClaim corev1.PersistentVolumeClaim
	PersistentVolume      corev1.PersistentVolume
	// ExtraResources are extra resources that need to be created along with the PersistentVolumeClaim.
	// This can include resources like ConfigMaps, Secrets, or any other Kubernetes objects that are
	// required for the storage provider to function correctly.
	ExtraResources []client.Object
}
