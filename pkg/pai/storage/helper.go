package storage

import (
	"crypto/md5"
	"encoding/hex"
	"maps"
	"strings"
	"unicode"
)

func MergeMap[M ~map[K]V, K comparable, V any](dest, src M) M {
	if len(src) == 0 {
		return dest
	}
	if dest == nil {
		dest = make(M)
	}
	maps.Copy(dest, src)
	return dest
}

func TryKeysDef(def string, m map[string]string, keys ...string) string {
	if try := TryKeys(m, keys...); try != "" {
		return try
	}
	return def
}

func TryKeys(m map[string]string, keys ...string) string {
	if m == nil {
		return ""
	}
	for _, key := range keys {
		if val, ok := m[key]; ok {
			return val
		}
	}
	return ""
}

// EncodeToK8sName encode a raw name to a k8s name, it escapes all illegal characters and limit the length to 63
// k8s name rule: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
func EncodeToK8sName(in string) string {
	return LimitNameLen(ToRFC1123(in))
}

func LimitNameLen(name string) string {
	const hashlen, maxlen = 6, 63
	if len(name) <= maxlen {
		return name
	}
	i := maxlen - hashlen - 1
	sha := md5.Sum([]byte(name[i:]))
	shastr := hex.EncodeToString(sha[:])
	return name[:i] + "-" + string(shastr[:hashlen])
}

// Most resource types require a name that can be used as a DNS subdomain name as defined in RFC 1123.
// This means the name must:
// - contain no more than 253 characters
// - contain only lowercase alphanumeric characters, '-' or '.'
// - start with an alphanumeric character
// - end with an alphanumeric character
func ToRFC1123(s string) string {
	// replace all non-alphanumeric characters with '-'
	s = strings.Map(func(r rune) rune {
		if unicode.IsLower(r) || unicode.IsDigit(r) || r == '-' || r == '.' {
			return r
		}
		if unicode.IsUpper(r) {
			return unicode.ToLower(r)
		}
		return '-'
	}, s)
	// remove leading and trailing non-alphanumeric characters
	s = strings.TrimFunc(s, func(r rune) bool {
		return !unicode.IsLower(r) && !unicode.IsDigit(r)
	})
	return s
}
