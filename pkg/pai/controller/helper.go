package controller

import (
	"crypto/md5"
	"encoding/hex"
	"math/rand"
	"strings"
	"unicode"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/rune/pkg/apis/pai"
)

const TRUE = "true"

func IsShared(obj client.Object) bool {
	labels := obj.GetLabels()
	return labels != nil && labels[pai.LabelShared] == TRUE
}

func IsReadOnly(obj client.Object) bool {
	labels := obj.GetLabels()
	return labels != nil && (labels[pai.LabelStorageReadOnly] == TRUE || labels["storageset.config.readOnly"] == TRUE)
}

func SetShared(obj client.Object) {
	labels := obj.GetLabels()
	if labels == nil {
		labels = map[string]string{}
	}
	labels[pai.LabelShared] = TRUE
	obj.SetLabels(labels)
}

// chars: 0-9a-z
const letters = "0123456789abcdefghijklmnopqrstuvwxyz"

func IngressHost(prefix, _, host string) string {
	name := RandString(6)
	if prefix == "" {
		return name + "." + host
	}
	return string(prefix) + "-" + name + "." + host
}

func tryKeysDef(def string, m map[string]string, keys ...string) string {
	if try := tryKeys(m, keys...); try == "" {
		return def
	} else {
		return try
	}
}

func tryKeys(m map[string]string, keys ...string) string {
	if m == nil {
		return ""
	}
	for _, key := range keys {
		if val, ok := m[key]; ok {
			return val
		}
	}
	return ""
}

func RandString(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func getJsonPath(kvs any, pathes ...string) string {
	ret := kvs
	for _, k := range pathes {
		kvs, ok := ret.(map[string]any)
		if !ok {
			return ""
		}
		if v, ok := kvs[k]; ok {
			ret = v
		}
	}
	if ret == nil {
		return ""
	}
	str, _ := ret.(string)
	return str
}

func setPath[T comparable](into map[string]any, val T, path ...string) T {
	return setPathOverride(into, val, true, path...)
}

func setPathOverride[T comparable](into map[string]any, val T, override bool, path ...string) T {
	emptyval := *new(T)
	for i, k := range path {
		// last key
		if i == len(path)-1 {
			if exists, ok := into[k]; !ok || exists == emptyval || override {
				into[k] = val
				return val
			} else {
				str, _ := exists.(T)
				return str
			}
		}
		if _, ok := into[k]; !ok {
			into[k] = map[string]any{}
		}
		exists, ok := into[k].(map[string]any)
		if !ok {
			return emptyval
		}
		into = exists
	}
	return emptyval
}

func setPathIfEmpty[T comparable](into map[string]any, val T, path ...string) T {
	return setPathOverride(into, val, false, path...)
}

// EncodeToK8sName encode a raw name to a k8s name, it escapes all illegal characters and limit the length to 63
// k8s name rule: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
func EncodeToK8sName(in string) string {
	return LimitNameLen(ToRFC1123(in))
}

func LimitNameLen(name string) string {
	const hashlen, maxlen = 6, 63
	if len(name) <= maxlen {
		return name
	}
	i := maxlen - hashlen - 1
	sha := md5.Sum([]byte(name[i:]))
	shastr := hex.EncodeToString(sha[:])
	return name[:i] + "-" + string(shastr[:hashlen])
}

// Most resource types require a name that can be used as a DNS subdomain name as defined in RFC 1123.
// This means the name must:
// - contain no more than 253 characters
// - contain only lowercase alphanumeric characters, '-' or '.'
// - start with an alphanumeric character
// - end with an alphanumeric character
func ToRFC1123(s string) string {
	// replace all non-alphanumeric characters with '-'
	s = strings.Map(func(r rune) rune {
		if unicode.IsLower(r) || unicode.IsDigit(r) || r == '-' || r == '.' {
			return r
		}
		if unicode.IsUpper(r) {
			return unicode.ToLower(r)
		}
		return '-'
	}, s)
	// remove leading and trailing non-alphanumeric characters
	s = strings.TrimFunc(s, func(r rune) bool {
		return !unicode.IsLower(r) && !unicode.IsDigit(r)
	})
	return s
}

// [a-z0-9]([-a-z0-9]*[a-z0-9])?
func ToSimplestName(name string) string {
	name = strings.Map(func(r rune) rune {
		if unicode.IsLower(r) || unicode.IsDigit(r) {
			return r
		}
		if unicode.IsUpper(r) {
			return unicode.ToLower(r)
		}
		return '-'
	}, name)
	name = strings.TrimFunc(name, func(r rune) bool {
		return !unicode.IsLower(r) && !unicode.IsDigit(r)
	})
	return name
}

func EncodeToK8sNameNoDot(in string) string {
	return LimitNameLen(ToSimplestName(in))
}

func GetOwner(obj client.Object, ownnerkind schema.GroupVersionKind) *metav1.OwnerReference {
	for _, owner := range obj.GetOwnerReferences() {
		if owner.APIVersion == ownnerkind.GroupVersion().Identifier() && owner.Kind == ownnerkind.Kind {
			return &owner
		}
	}
	return nil
}

func MaxResourceList(total corev1.ResourceList, add corev1.ResourceList) {
	ResourceListCollect(total, add, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		if into.Cmp(val) < 0 {
			*into = val
		}
	})
}

func AddResourceList(total corev1.ResourceList, add corev1.ResourceList) {
	ResourceListCollect(total, add, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		into.Add(val)
	})
}

func SubResourceList(total corev1.ResourceList, sub corev1.ResourceList) {
	ResourceListCollect(total, sub, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		into.Sub(val)
	})
}

type ResourceListCollectFunc func(corev1.ResourceName, *resource.Quantity, resource.Quantity)

func ResourceListCollect(into, vals corev1.ResourceList, collect ResourceListCollectFunc) corev1.ResourceList {
	for resourceName, quantity := range vals {
		lastQuantity := into[resourceName].DeepCopy()
		collect(resourceName, &lastQuantity, quantity)
		into[resourceName] = lastQuantity
	}
	return into
}
