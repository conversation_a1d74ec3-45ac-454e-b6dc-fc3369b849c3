package controller

import (
	"context"
	"fmt"
	"strings"

	"helm.sh/helm/v3/pkg/strvals"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/util/workqueue"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/yaml"
	"xiaoshiai.cn/common/log"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

const (
	ApplicationFinalizer = "application.finalizers.pai.kubegems.io"
)

type ApplicationReconciler struct {
	Client  client.Client
	Applier *HelmApplyer
}

func NewApplicationController(cli client.Client, config *rest.Config, cacheDir string) *ApplicationReconciler {
	return &ApplicationReconciler{
		Client:  cli,
		Applier: NewHelmApplyer(config, cacheDir),
	}
}

func (a *ApplicationReconciler) SetupController(ctx context.Context, mgr ctrl.Manager) error {
	handler := configMapOrSecretTrigger(ctx, mgr.GetClient())
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.Application{}).
		WithOptions(controller.Options{MaxConcurrentReconciles: 3}).
		Watches(&corev1.ConfigMap{}, handler).
		Watches(&corev1.Secret{}, handler).
		Watches(&paiv1beta1.Application{}, applicationUnhealthyTrigger(ctx, mgr.GetClient())).
		Complete(
			NewBetterReconciler(a, mgr,
				WithFinalizer(ApplicationFinalizer),
				WithInjectFinalizers(metav1.FinalizerDeleteDependents),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

func (a *ApplicationReconciler) Sync(ctx context.Context, app *paiv1beta1.Application) error {
	err := a.syncApplication(ctx, app)
	if err != nil {
		app.Status.Phase = paiv1beta1.PhaseFailed
		app.Status.Message = err.Error()
	}
	// update status if updated whenever the sync has error or no
	if err := a.Client.Status().Update(ctx, app); err != nil {
		return err
	}
	return nil
}

func applicationUnhealthyTrigger(ctx context.Context, cli client.Client) handler.EventHandler {
	logger := log.FromContext(ctx)
	return handler.Funcs{
		UpdateFunc: func(ctx context.Context, tue event.TypedUpdateEvent[client.Object], trli workqueue.TypedRateLimitingInterface[reconcile.Request]) {
			app, ok := tue.ObjectNew.(*paiv1beta1.Application)
			if !ok {
				return
			}
			if app.Status.Phase != paiv1beta1.PhaseFailed {
				return
			}
			apps := paiv1beta1.ApplicationList{}
			_ = cli.List(ctx, &apps)
			for _, item := range apps.Items {
				for _, ref := range item.Status.Resources {
					if ref.Namespace == "" {
						continue
					}
					if ref.APIVersion == app.APIVersion && ref.Kind == app.Kind &&
						ref.Name == app.GetName() && ref.Namespace == app.GetNamespace() {
						logger.Info("triggering reconciliation", "application", item.Name, "kind", app.Kind, "name", app.GetName(), "namespace", item.GetNamespace())
						trli.Add(reconcile.Request{NamespacedName: client.ObjectKeyFromObject(&item)})
					}
				}
			}
		},
	}
}

func configMapOrSecretTrigger(ctx context.Context, cli client.Client) handler.EventHandler {
	logger := log.FromContext(ctx)
	return handler.Funcs{
		UpdateFunc: func(ctx context.Context, tue event.TypedUpdateEvent[client.Object], trli workqueue.TypedRateLimitingInterface[reconcile.Request]) {
			kind := ""
			switch tue.ObjectNew.(type) {
			case *corev1.ConfigMap:
				kind = "ConfigMap"
			case *corev1.Secret:
				kind = "Secret"
			default:
				return
			}
			app := paiv1beta1.ApplicationList{}
			_ = cli.List(ctx, &app)
			for _, item := range app.Items {
				for _, ref := range item.Spec.ValuesFrom {
					if ref.Namespace == "" {
						ref.Namespace = item.Namespace
					}
					if ref.Kind == kind && ref.Name == tue.ObjectNew.GetName() && ref.Namespace == tue.ObjectNew.GetNamespace() {
						logger.Info("triggering reconciliation", "plugin", item.Name, "kind", kind, "name", tue.ObjectNew.GetName(), "namespace", item.GetNamespace())
						trli.Add(reconcile.Request{NamespacedName: client.ObjectKeyFromObject(&item)})
					}
				}
			}
		},
	}
}

func (a *ApplicationReconciler) syncApplication(ctx context.Context, app *paiv1beta1.Application) error {
	if app.Spec.Disabled {
		// just remove
		return a.Applier.Remove(ctx, app)
	}
	// check all dependencies are installed
	if err := a.checkDepenency(ctx, app); err != nil {
		return err
	}
	// resolve valuesRef
	if err := a.resolveValuesRef(ctx, app); err != nil {
		return err
	}
	if err := a.preProcessAnnotations(ctx, app); err != nil {
		return err
	}
	if err := a.Applier.Apply(ctx, app); err != nil {
		return err
	}
	if err := a.checkResourcesStatus(ctx, app); err != nil {
		return err
	}
	app.Status.ObservedGeneration = app.Generation
	return nil
}

func (a *ApplicationReconciler) checkResourcesStatus(ctx context.Context, app *paiv1beta1.Application) error {
	for i, res := range app.Status.Resources {
		if res.Kind == "Application" || res.APIVersion == paiv1beta1.GroupVersion.String() {
			if err := a.checkPluginStatus(ctx, &app.Status.Resources[i]); err != nil {
				return err
			}
		}
	}
	return nil
}
func (a *ApplicationReconciler) checkPluginStatus(ctx context.Context, managed *paiv1beta1.ManagedResource) error {
	app := &paiv1beta1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      managed.Name,
			Namespace: managed.Namespace,
		},
	}
	if err := a.Client.Get(ctx, client.ObjectKeyFromObject(app), app); err != nil {
		return err
	}
	if app.Status.Phase != paiv1beta1.PhaseInstalled && app.Status.Message != "" {
		return fmt.Errorf("application %s/%s: %s", app.Namespace, app.Name, app.Status.Message)
	}
	return nil
}

func (a *ApplicationReconciler) preProcessAnnotations(ctx context.Context, app *paiv1beta1.Application) error {
	logger := log.FromContext(ctx)

	if app.Annotations == nil {
		app.Annotations = make(map[string]string)
	}
	namespaces := parseListValues(app.Annotations["applications.kubegems.io/create-namespaces"])
	for _, ns := range namespaces {
		logger.Info("check namespace exists", "namespace", ns)
		if err := a.Client.Get(ctx, types.NamespacedName{Name: ns}, &corev1.Namespace{}); err != nil {
			if !apierrors.IsNotFound(err) {
				return err
			}
			logger.Info("create namespace", "namespace", ns)
			if err := a.Client.Create(ctx, &corev1.Namespace{ObjectMeta: metav1.ObjectMeta{Name: ns}}); err != nil {
				return err
			}
		}
		logger.Info("namespace exists", "namespace", ns)
	}
	return nil
}
func parseListValues(annotations string) []string {
	var list []string
	for _, v := range strings.Split(annotations, ",") {
		v = strings.TrimSpace(v)
		if v != "" {
			list = append(list, v)
		}
	}
	return list
}

func (a *ApplicationReconciler) resolveValuesRef(ctx context.Context, app *paiv1beta1.Application) error {
	base := map[string]any{}
	for _, ref := range app.Spec.ValuesFrom {
		switch strings.ToLower(ref.Kind) {
		case "secret", "secrets":
			secret := &corev1.Secret{ObjectMeta: metav1.ObjectMeta{Name: ref.Name, Namespace: app.Namespace}}
			if err := a.Client.Get(ctx, client.ObjectKeyFromObject(secret), secret); err != nil {
				if ref.Optional && apierrors.IsNotFound(err) {
					continue
				}
				return err
			}
			// --set
			for k, v := range secret.Data {
				if err := mergeInto(ref.Prefix+k, string(v), base); err != nil {
					return fmt.Errorf("parse %#v key[%s]: %w", ref, k, err)
				}
			}
		case "configmap", "configmaps":
			configmap := &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{Name: ref.Name, Namespace: app.Namespace}}
			if err := a.Client.Get(ctx, client.ObjectKeyFromObject(configmap), configmap); err != nil {
				if ref.Optional && apierrors.IsNotFound(err) {
					continue
				}
				return err
			}
			// -f/--values
			for k, v := range configmap.BinaryData {
				currentMap := map[string]interface{}{}
				if err := yaml.Unmarshal(v, &currentMap); err != nil {
					return fmt.Errorf("parse %#v key[%s]: %w", ref, k, err)
				}
				base = mergeMaps(base, currentMap)
			}
			// --set
			for k, v := range configmap.Data {
				if err := mergeInto(ref.Prefix+k, string(v), base); err != nil {
					return fmt.Errorf("parse %#v key[%s]: %w", ref, k, err)
				}
			}
		default:
			return fmt.Errorf("valuesRef kind [%s] is not supported", ref.Kind)
		}
	}
	// inlined values
	base = mergeMaps(base, app.Spec.Values.Object)
	app.Spec.Values = paiv1beta1.Values{Object: base}.FullFill()
	return nil
}

func (a *ApplicationReconciler) checkDepenency(ctx context.Context, app *paiv1beta1.Application) error {
	for _, dep := range app.Spec.Dependencies {
		if dep.Name == "" {
			continue
		}
		if dep.Namespace == "" {
			dep.Namespace = app.Namespace
		}
		if dep.Kind == "" {
			dep.APIVersion = app.APIVersion
			dep.Kind = app.Kind
		}
		newobj, _ := a.Client.Scheme().New(dep.GroupVersionKind())
		depobj, ok := newobj.(client.Object)
		if !ok {
			depobj = &metav1.PartialObjectMetadata{
				TypeMeta: metav1.TypeMeta{
					APIVersion: dep.GroupVersionKind().GroupVersion().String(),
					Kind:       dep.Kind,
				},
			}
		}

		// exists check
		if err := a.Client.Get(ctx, client.ObjectKey{Namespace: dep.Namespace, Name: dep.Name}, depobj); err != nil {
			if apierrors.IsNotFound(err) {
				return DependencyError{Reason: err.Error(), Object: dep}
			}
			return err
		}

		// status check
		switch obj := depobj.(type) {
		case *paiv1beta1.Application:
			if obj.Status.Phase != paiv1beta1.PhaseInstalled {
				return DependencyError{Reason: "not installed", Object: dep}
			}
		}
	}
	return nil
}

type DependencyError struct {
	Reason string
	Object corev1.ObjectReference
}

func (e DependencyError) Error() string {
	return fmt.Sprintf("dependency %s/%s :%s", e.Object.Namespace, e.Object.Name, e.Reason)
}

func (a *ApplicationReconciler) Remove(ctx context.Context, app *paiv1beta1.Application) error {
	return a.Applier.Remove(ctx, app)
}

// b override in a
func mergeMaps(a, b map[string]interface{}) map[string]interface{} {
	out := make(map[string]interface{}, len(a))
	for k, v := range a {
		out[k] = v
	}
	for k, v := range b {
		if v, ok := v.(map[string]interface{}); ok {
			if bv, ok := out[k]; ok {
				if bv, ok := bv.(map[string]interface{}); ok {
					out[k] = mergeMaps(bv, v)
					continue
				}
			}
		}
		out[k] = v
	}
	return out
}

func mergeInto(k, v string, base map[string]interface{}) error {
	if err := strvals.ParseInto(fmt.Sprintf("%s=%s", k, v), base); err != nil {
		return fmt.Errorf("parse %#v key[%s]: %w", k, v, err)
	}
	return nil
}
