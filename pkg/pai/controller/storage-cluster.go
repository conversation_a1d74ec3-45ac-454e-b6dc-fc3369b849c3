package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"slices"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	kubeyaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/rest"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/common/helm"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/pai/controller/storageprovider"
)

const (
	StorageClusterFinalizer = "storagecluster.finalizers.pai.kubegems.io"
)

type StorageClusterReconciler struct {
	// Client is used to interact with the Kubernetes API
	Client client.Client
	Config *rest.Config
}

func NewStorageClusterController(cli client.Client, Config *rest.Config) *StorageClusterReconciler {
	return &StorageClusterReconciler{
		Client: cli,
		Config: Config,
	}
}

func (s *StorageClusterReconciler) SetupController(ctx context.Context, mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.StorageCluster{}).
		Owns(&paiv1beta1.Application{}).
		// Watches(
		// 	&corev1.PersistentVolumeClaim{},
		// 	handler.EnqueueRequestsFromMapFunc(filetr),
		// 	builder.WithPredicates(predicates()),
		// ).
		Complete(
			NewBetterReconciler(s, mgr,
				WithFinalizer(StorageClusterFinalizer),
				WithInjectFinalizers(metav1.FinalizerDeleteDependents),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

// func filetr(ctx context.Context, obj client.Object) []ctrl.Request {
// 	pvc := obj.(*corev1.PersistentVolumeClaim)
// 	if pvc.Spec.StorageClassName == nil {
// 		return nil
// 	}
// 	return []ctrl.Request{
// 		{
// 			NamespacedName: types.NamespacedName{
// 				Name: *pvc.Spec.StorageClassName,
// 			},
// 		},
// 	}
// }

// func predicates() predicate.Predicate {
// 	return predicate.Funcs{
// 		UpdateFunc: func(ue event.UpdateEvent) bool {
// 			pvc,ok:=ue.ObjectNew.(*corev1.PersistentVolumeClaim)
// 			if !ok{
// 				return false
// 			}
// 			return pvc.Status.Phase != ue.ObjectOld.(*corev1.PersistentVolumeClaim).Status.Phase
// 			//return ue.ObjectOld.GetGeneration() != ue.ObjectNew.GetGeneration()
// 		},
// 	}
// }

func (s *StorageClusterReconciler) Sync(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage cluster synced", "cluster", cluster.Name)
	// 1. CSI Driver先手动安装,这里假设已经安装了
	csiDrivers := &storagev1.CSIDriverList{}
	if err := s.Client.List(ctx, csiDrivers); err != nil {
		return err
	}
	if len(csiDrivers.Items) == 0 {
		return fmt.Errorf("no csi driver found")
	}
	provisioner, err := storageprovider.GetStorageProviderProvisioner(cluster.Spec.Provider)
	if err != nil {
		return err
	}
	if !slices.ContainsFunc(csiDrivers.Items, func(i storagev1.CSIDriver) bool {
		return i.Name == provisioner.Provisioner
	}) {
		return fmt.Errorf("csi driver %s not found", provisioner)
	}
	// found
	log.Info("csi driver found", "driver", provisioner)
	// 2. 创建StorageClass,静态存储卷,部署s3gateway和s3gateway的ingress就通过plugin安装
	app := &paiv1beta1.Application{
		ObjectMeta: metav1.ObjectMeta{Name: cluster.Name, Namespace: storageprovider.CSIDriverNamespace},
	}
	if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, app, func() error {
		if err := controllerutil.SetControllerReference(cluster, app, s.Client.Scheme()); err != nil {
			return err
		}
		app.Spec.Kind = paiv1beta1.BundleKindHelm
		app.Spec.Chart = provisioner.Chart
		app.Spec.URL = provisioner.Url
		app.Spec.Version = provisioner.Version
		app.Spec.Values.Raw = cluster.Spec.Values.Raw
		return nil
	}); err != nil {
		cluster.Status.Phase = paiv1beta1.StorageClusterPhaseFailed
		cluster.Status.Message = err.Error()
		return err
	}

	for _, obj := range app.Status.Resources {
		log.Info("storage cluster synced", "cluster", cluster.Name, "object", obj)
		if obj.Kind == "StorageClass" {
			cluster.Status.StorageClassReference = corev1.ObjectReference{
				APIVersion: obj.APIVersion,
				Kind:       obj.Kind,
				Namespace:  obj.Namespace,
				Name:       obj.Name,
			}
		}
		cluster.Status.Objects = append(cluster.Status.Objects, corev1.ObjectReference{
			APIVersion: obj.APIVersion,
			Kind:       obj.Kind,
			Namespace:  obj.Namespace,
			Name:       obj.Name,
		})
	}
	return nil
}

func (s *StorageClusterReconciler) Remove(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage cluster Removed", "cluster", cluster.Name)
	app := &paiv1beta1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Name:      cluster.Name,
			Namespace: storageprovider.CSIDriverNamespace,
		},
	}
	return client.IgnoreNotFound(s.Client.Delete(ctx, app))
	// for _, managedObj := range cluster.Status.Objects {
	// 	gl := managedObj.GetObjectKind().GroupVersionKind().GroupKind()
	// 	//Group: storage.k8s.io
	// 	//Kind: StorageClass
	// 	if gl.Group == "storage.k8s.io" && gl.Kind == "StorageClass" {
	// 		if err := s.Client.Delete(ctx, &storagev1.StorageClass{
	// 			ObjectMeta: metav1.ObjectMeta{
	// 				Name: managedObj.Name,
	// 			},
	// 		}); err != nil {
	// 			return err
	// 		}
	// 	}

	// }

	// //2.删除s3gateway的chart服务
	// if _, err := helm.RemoveChart(ctx, s.Config, cluster.Name, storageprovider.S3GatewayNamespace); err != nil {
	// 	return err
	// }
	// //3.删除ingress
	// return nil
}

func (s *StorageClusterReconciler) syncInstall(ctx context.Context, cluster *paiv1beta1.StorageCluster) (storageprovider.StorageProvider, error) {
	var values map[string]any
	if err := json.Unmarshal(cluster.Spec.Values.Raw, &values); err != nil {
		return nil, err
	}
	helmapplyoptions := helm.ApplyChartOptions{
		Values: values,
	}
	rls, changed, err := helm.ApplyChartFromRepo(ctx, s.Config, cluster.Spec.Repository, cluster.Spec.Chart, cluster.Spec.Version, cluster.Name, storageprovider.CSIDriverNamespace, helmapplyoptions)
	if err != nil {
		return nil, err
	}
	_ = changed
	unstructs, err := readObjects([]byte(rls.Manifest))
	if err != nil {
		return nil, err
	}
	manged := []corev1.ObjectReference{}
	for _, obj := range unstructs {
		manged = append(manged, getReference(obj))
	}
	cluster.Status.Objects = manged
	if rls.Info != nil {
		cluster.Status.Message = rls.Info.Notes
	}
	return s.syncInstallObjects(unstructs, cluster.Spec.Provider)
}

func (s *StorageClusterReconciler) syncInstallObjects(objects []*unstructured.Unstructured, p paiv1beta1.StorageClusterProvider) (storageprovider.StorageProvider, error) {
	var (
		paramters         = map[string]string{}
		secrets           = map[string]string{}
		configmaps        = map[string]string{}
		name, provisioner string
	)
	for _, obj := range objects {
		gvk := obj.GetObjectKind().GroupVersionKind()
		switch gvk {
		case storagev1.SchemeGroupVersion.WithKind("StorageClass"):
			sc := &storagev1.StorageClass{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, sc); err != nil {
				continue
			}
			copyFn(paramters, sc.Parameters)
			name = sc.Name
			provisioner = sc.Provisioner
		case corev1.SchemeGroupVersion.WithKind("Secret"):
			secret := &corev1.Secret{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, secret); err != nil {
				continue
			}
			if len(secret.Data) != 0 {
				var newSec = map[string]string{}
				for k, v := range secret.Data {
					newSec[k] = string(v)
				}
				copyFn(secrets, newSec)
			}
			copyFn(secrets, secret.StringData)

		case corev1.SchemeGroupVersion.WithKind("ConfigMap"):
			cm := &corev1.ConfigMap{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, cm); err != nil {
				continue
			}
			copyFn(configmaps, cm.Data, func(v string) bool {
				return v == ""
			})

		}
	}
	return storageprovider.GetStorageProvider(p, name, provisioner, paramters, configmaps, secrets)
}

func (s *StorageClusterReconciler) syncS3Gateway(ctx context.Context, name, pvc string) error {
	values := make(map[string]any)
	setPath(values, pvc, "gateway", "storage")
	helmapplyoptions := helm.ApplyChartOptions{
		Values: values,
	}
	_, _, err := helm.ApplyChartFromRepo(ctx, s.Config, storageprovider.S3GatewayRepository, storageprovider.S3GatewayChart, storageprovider.S3GatewayVersion, name, storageprovider.S3GatewayNamespace, helmapplyoptions)
	if err != nil {
		return err
	}
	return nil
}

// TODO
func (s *StorageClusterReconciler) syncStorageClusterIngress(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	ingress := &networkingv1.Ingress{ObjectMeta: metav1.ObjectMeta{Name: "", Namespace: storageprovider.S3GatewayNamespace}}
	if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, ingress, func() error {
		// todo
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func readObjects(data []byte) ([]*unstructured.Unstructured, error) {
	d := kubeyaml.NewYAMLOrJSONDecoder(bytes.NewReader(data), 4096)
	var objs []*unstructured.Unstructured
	for {
		u := &unstructured.Unstructured{}
		if err := d.Decode(u); err != nil {
			if err == io.EOF {
				break
			}
			return objs, fmt.Errorf("failed to unmarshal manifest: %v", err)
		}
		if u.Object == nil || len(u.Object) == 0 {
			continue // skip empty object
		}
		objs = append(objs, u)
	}
	return objs, nil
}

func getReference(obj client.Object) corev1.ObjectReference {
	return corev1.ObjectReference{
		APIVersion: obj.GetObjectKind().GroupVersionKind().GroupVersion().String(),
		Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
		Namespace:  obj.GetNamespace(),
		Name:       obj.GetName(),
	}
}

func copyFn[M1 ~map[K]V, M2 ~map[K]V, K comparable, V any](dst M1, src M2, filter ...func(value V) bool) {
	for k, v := range src {
		if len(filter) == 0 {
			dst[k] = v
			continue
		}
		for _, f := range filter {
			if !f(v) {
				dst[k] = v
			}
		}
	}
}
