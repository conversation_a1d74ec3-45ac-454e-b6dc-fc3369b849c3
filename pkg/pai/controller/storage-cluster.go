package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	kubeyaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/rest"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/common/helm"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/pai/controller/storageprovider"
)

const (
	StorageClusterFinalizer = "storagecluster.finalizers.pai.kubegems.io"
)

type StorageClusterReconciler struct {
	// Client is used to interact with the Kubernetes API
	Client client.Client
	Config *rest.Config
}

func NewStorageClusterController(cli client.Client, Config *rest.Config) *StorageClusterReconciler {
	return &StorageClusterReconciler{
		Client: cli,
		Config: Config,
	}
}

func (s *StorageClusterReconciler) SetupController(ctx context.Context, mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.StorageCluster{}).
		Complete(
			NewBetterReconciler(s, mgr,
				WithFinalizer(StorageClusterFinalizer),
				WithInjectFinalizers(metav1.FinalizerDeleteDependents),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

func (s *StorageClusterReconciler) Sync(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage cluster synced", "cluster", cluster.Name)
	provisioner, err := s.syncInstall(ctx, cluster)
	if err != nil {
		return err
	}
	// TODO-以下操作放入chart中

	// 1. 创建/更新静态pv和pvc，并绑定
	staticPV, err := provisioner.PersistentVolumeClaimContext()
	if err != nil {
		return err
	}
	var objects []client.Object
	objects = append(objects, staticPV.ExtraResources...)
	objects = append(objects, &staticPV.PersistentVolume)
	objects = append(objects, &staticPV.PersistentVolumeClaim)
	for _, obj := range objects {
		if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, obj, func() error {
			return nil
		}); err != nil {
			return err
		}
	}
	// 2. 部署/更新ss3gateway的plugin，并把pvc绑定到/s3gateway下
	if err := s.syncS3Gateway(ctx, cluster.Name, staticPV.PersistentVolumeClaim.Name); err != nil {
		return err
	}
	// 3. 创建ingress，映射s3gateway service
	return s.syncStorageClusterIngress(ctx, cluster)
}

func (s *StorageClusterReconciler) Remove(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage cluster synced", "cluster", cluster.Name)
	//1.删除storageclass
	for _, managedObj := range cluster.Status.Objects {
		gl := managedObj.GetObjectKind().GroupVersionKind().GroupKind()
		//Group: storage.k8s.io
		//Kind: StorageClass
		if gl.Group == "storage.k8s.io" && gl.Kind == "StorageClass" {
			if err := s.Client.Delete(ctx, &storagev1.StorageClass{
				ObjectMeta: metav1.ObjectMeta{
					Name: managedObj.Name,
				},
			}); err != nil {
				return err
			}
		}

	}

	//2.删除s3gateway的chart服务
	if _, err := helm.RemoveChart(ctx, s.Config, cluster.Name, storageprovider.S3GatewayNamespace); err != nil {
		return err
	}
	//3.删除ingress
	return nil
}

func (s *StorageClusterReconciler) syncInstall(ctx context.Context, cluster *paiv1beta1.StorageCluster) (storageprovider.StorageProvider, error) {
	var values map[string]any
	if err := json.Unmarshal(cluster.Spec.Values.Raw, &values); err != nil {
		return nil, err
	}
	helmapplyoptions := helm.ApplyChartOptions{
		Values: values,
	}
	rls, changed, err := helm.ApplyChartFromRepo(ctx, s.Config, cluster.Spec.Repository, cluster.Spec.Chart, cluster.Spec.Version, cluster.Name, storageprovider.CSIDriverNamespace, helmapplyoptions)
	if err != nil {
		return nil, err
	}
	_ = changed
	unstructs, err := readObjects([]byte(rls.Manifest))
	if err != nil {
		return nil, err
	}
	manged := []corev1.ObjectReference{}
	for _, obj := range unstructs {
		manged = append(manged, getReference(obj))
	}
	cluster.Status.Objects = manged
	if rls.Info != nil {
		cluster.Status.Message = rls.Info.Notes
	}
	return s.syncInstallObjects(unstructs, cluster.Spec.Provider)
}

func (s *StorageClusterReconciler) syncInstallObjects(objects []*unstructured.Unstructured, p paiv1beta1.StorageClusterProvider) (storageprovider.StorageProvider, error) {
	var (
		paramters         = map[string]string{}
		secrets           = map[string]string{}
		configmaps        = map[string]string{}
		name, provisioner string
	)
	for _, obj := range objects {
		gvk := obj.GetObjectKind().GroupVersionKind()
		switch gvk {
		case storagev1.SchemeGroupVersion.WithKind("StorageClass"):
			sc := &storagev1.StorageClass{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, sc); err != nil {
				continue
			}
			copyFn(paramters, sc.Parameters)
			name = sc.Name
			provisioner = sc.Provisioner
		case corev1.SchemeGroupVersion.WithKind("Secret"):
			secret := &corev1.Secret{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, secret); err != nil {
				continue
			}
			if len(secret.Data) != 0 {
				var newSec = map[string]string{}
				for k, v := range secret.Data {
					newSec[k] = string(v)
				}
				copyFn(secrets, newSec)
			}
			copyFn(secrets, secret.StringData)

		case corev1.SchemeGroupVersion.WithKind("ConfigMap"):
			cm := &corev1.ConfigMap{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, cm); err != nil {
				continue
			}
			copyFn(configmaps, cm.Data, func(v string) bool {
				return v == ""
			})

		}
	}
	return storageprovider.GetStorageProvider(p, name, provisioner, paramters, configmaps, secrets)
}

func (s *StorageClusterReconciler) syncS3Gateway(ctx context.Context, name, pvc string) error {
	values := make(map[string]any)
	setPath(values, pvc, "gateway", "storage")
	helmapplyoptions := helm.ApplyChartOptions{
		Values: values,
	}
	_, _, err := helm.ApplyChartFromRepo(ctx, s.Config, storageprovider.S3GatewayRepository, storageprovider.S3GatewayChart, storageprovider.S3GatewayVersion, name, storageprovider.S3GatewayNamespace, helmapplyoptions)
	if err != nil {
		return err
	}
	return nil
}

// TODO
func (s *StorageClusterReconciler) syncStorageClusterIngress(ctx context.Context, cluster *paiv1beta1.StorageCluster) error {
	ingress := &networkingv1.Ingress{ObjectMeta: metav1.ObjectMeta{Name: "", Namespace: storageprovider.S3GatewayNamespace}}
	if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, ingress, func() error {
		// todo
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func readObjects(data []byte) ([]*unstructured.Unstructured, error) {
	d := kubeyaml.NewYAMLOrJSONDecoder(bytes.NewReader(data), 4096)
	var objs []*unstructured.Unstructured
	for {
		u := &unstructured.Unstructured{}
		if err := d.Decode(u); err != nil {
			if err == io.EOF {
				break
			}
			return objs, fmt.Errorf("failed to unmarshal manifest: %v", err)
		}
		if u.Object == nil || len(u.Object) == 0 {
			continue // skip empty object
		}
		objs = append(objs, u)
	}
	return objs, nil
}

func getReference(obj client.Object) corev1.ObjectReference {
	return corev1.ObjectReference{
		APIVersion: obj.GetObjectKind().GroupVersionKind().GroupVersion().String(),
		Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
		Namespace:  obj.GetNamespace(),
		Name:       obj.GetName(),
	}
}

func copyFn[M1 ~map[K]V, M2 ~map[K]V, K comparable, V any](dst M1, src M2, filter ...func(value V) bool) {
	for k, v := range src {
		if len(filter) == 0 {
			dst[k] = v
			continue
		}
		for _, f := range filter {
			if !f(v) {
				dst[k] = v
			}
		}
	}
}
