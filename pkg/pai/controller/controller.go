package controller

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	_ "k8s.io/client-go/plugin/pkg/client/auth"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	batchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	schedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

const ControllerConcurrency = 3

// nolint: gochecknoinits
func GetScheme() *runtime.Scheme {
	scheme := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	// utilruntime.Must(pluginsv1beta1.AddToScheme(scheme))
	utilruntime.Must(paiv1beta1.AddToScheme(scheme))
	utilruntime.Must(batchv1alpha1.AddToScheme(scheme))
	utilruntime.Must(schedulingv1beta1.AddToScheme(scheme))
	return scheme
}

// nolint lll
type Options struct {
	MetricsAddr       string          `json:"metricsAddr,omitempty" description:"The address the metric endpoint binds to."`
	ProbeAddr         string          `json:"probeAddr,omitempty" description:"The address the probe endpoint binds to."`
	Namespace         string          `json:"namespace,omitempty" description:"Current namespace."`
	Concurrent        int             `json:"concurrent,omitempty" description:"The number of concurrent workers to run."`
	LeaderElection    bool            `json:"leaderElection,omitempty" description:"Enabled leader election"`
	Ingress           *IngressOptions `json:"ingress,omitempty" description:"Ingress config"`
	Images            *ImagesOptions  `json:"images,omitempty" description:"Images config"`
	WebhookAddr       string          `json:"webhookAddr,omitempty" description:"The address the webhook endpoint binds to."`
	EnebleWebhook     bool            `json:"enableWebhook,omitempty" description:"Enable webhook"`
	RequestPercentage int             `json:"requestPercentage,omitempty" description:"The percentage of requests to be intercepted by the webhook"`
	StorageType       string          `json:"storageType,omitempty" description:"The storage type to use"` // default is juicefs
}

type ImagesOptions struct {
	Base                    string `json:"base,omitempty"`
	HuggingFace             string `json:"huggingFace,omitempty"`
	ModelScope              string `json:"modelScope,omitempty"`
	Modelx                  string `json:"modelx,omitempty"`
	Git                     string `json:"git,omitempty"`
	DistributionInitializer string `json:"distributionInitializer,omitempty"`
	Ternsorboard            string `json:"tensorboard,omitempty"`
	PythonEnv               string `json:"pythonEnv,omitempty"`
	CopyData                string `json:"copydata,omitempty"`
	ModulePubTask           string `json:"modulePubTask,omitempty"`
	Generalization          string `json:"generalization,omitempty"`
}

type IngressOptions struct {
	SecretName string `json:"secretName,omitempty"`
	ClassName  string `json:"className,omitempty"`
	BaseHost   string `json:"baseHost,omitempty"`
}

func NewDefaultOptions() *Options {
	return &Options{
		MetricsAddr: "127.0.0.1:9100", // default run under kube-rbac-proxy
		ProbeAddr:   ":8081",
		Namespace:   "kubegems-pai",
		Concurrent:  ControllerConcurrency,
		Ingress:     &IngressOptions{},
		Images: &ImagesOptions{
			Base:                    "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/busybox:latest",
			HuggingFace:             "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/huggingface-downloader:latest",
			ModelScope:              "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/modelscope-downloader:latest",
			Modelx:                  "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/modelx:latest",
			Git:                     "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/git-downloader:latest",
			DistributionInitializer: "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/distribution-initializer:latest",
			Ternsorboard:            "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/tensorboard:2.16.2",
			PythonEnv:               "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/anaconda3:v0.0.4",
			CopyData:                "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/mc:v0.0.1",
			ModulePubTask:           "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/module-pub:latest",
			Generalization:          "registry.cn-hangzhou.aliyuncs.com/xiaoshiai/dataset-enhance:latest",
		},
		WebhookAddr:       ":8443",
		RequestPercentage: 80,
		StorageType:       "juicefs",
	}
}

func Run(ctx context.Context, options *Options) error {
	setupLog := ctrl.Log.WithName("setup")

	ctrloptions := ctrl.Options{
		Scheme: GetScheme(),
		// MetricsBindAddress:     options.MetricsAddr,
		HealthProbeBindAddress: options.ProbeAddr,
		LeaderElection:         options.LeaderElection,
		LeaderElectionID:       pai.GroupName,
	}
	if options.EnebleWebhook {
		// webhookHost, webhookportstr, _ := net.SplitHostPort(options.WebhookAddr)
		// ctrloptions.Host = webhookHost
		// ctrloptions.Port, _ = strconv.Atoi(webhookportstr)
	}
	cfg, err := ctrl.GetConfig()
	if err != nil {
		return fmt.Errorf("failed to get config: %w", err)
	}
	// cli, err := client.New(cfg, client.Options{Scheme: GetScheme()})
	// if err != nil {
	// 	return fmt.Errorf("failed to create client: %w", err)
	// }
	// if err := InitResources(ctx, cli, options); err != nil {
	// 	return fmt.Errorf("failed to init resources: %w", err)
	// }
	mgr, err := ctrl.NewManager(cfg, ctrloptions)
	if err != nil {
		setupLog.Error(err, "unable to create manager")
		return err
	}
	if err := Setup(ctx, mgr, options); err != nil {
		setupLog.Error(err, "unable to create plugin controller", "controller", "plugin")
		return err
	}

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		return err
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		return err
	}
	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		return err
	}
	return nil
}

func Setup(ctx context.Context, mgr ctrl.Manager, options *Options) error {
	ingressoptions, err := GetBaseIngressSetting(options.Ingress)
	if err != nil {
		return err
	}
	cli := mgr.GetClient()
	// var storageprovider StorageProvider
	// if options.StorageType == "cephfs" {
	// 	storageprovider = NewCephFSStorageProvider(cli, StorageCephFSNamespace, ingressoptions)
	// } else {
	// 	storageprovider = NewJuiceFSStorageProvider(cli, StorageJuiceFSNamespace, ingressoptions)
	// }
	// dec := admission.NewDecoder(cli.Scheme())

	mgr.GetLogger()
	reconcilers := []SetupController{
		// &StorageSetReconciler{
		// 	provider:     storageprovider,
		// 	paiNamespace: options.Namespace,
		// 	Client:       cli,
		// 	images:       options.Images,
		// },
		// &TenantReconciler{provider: storageprovider, Client: cli},
		NewJobReconciler(cli,
			options,
			&IngressNetworkProvider{Client: cli, Options: ingressoptions},
			&VolcanoJobProvider{Client: cli, Images: options.Images},
		),
		// &QueueReconciler{Client: cli, PaiNamespace: options.Namespace},
		// &SkuReconciler{Client: cli, RequestPercentage: options.RequestPercentage},
		// &QuotaReconciler{Client: cli, AdminssionDec: dec},
		// &NodeReconciler{Client: cli},
		// &VisualConfigReconciler{
		// 	Client:         cli,
		// 	Options:        options,
		// 	EventRecorder:  mgr.GetEventRecorderFor("visualconfig-controller"),
		// 	PaiNamespace:   options.Namespace,
		// 	IngressOptions: ingressoptions,
		// 	Logger:         mgr.GetLogger().WithName("visualconfig-controller"),
		// },
	}
	for _, r := range reconcilers {
		if err := r.SetupController(ctx, mgr); err != nil {
			return err
		}
		if options.EnebleWebhook {
			if sethook, ok := r.(SetupWebhook); ok {
				if err := sethook.SetupWebhook(ctx, mgr); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func GetBaseIngressSetting(opts *IngressOptions) (*IngressNetworkBaseSetting, error) {
	basehost := opts.BaseHost
	if !strings.HasPrefix(basehost, "http://") && !strings.HasPrefix(basehost, "https://") {
		// If the base host does not start with http:// or https://, we assume it's a hostname.
		basehost = "http://" + basehost
	}
	u, err := url.Parse(basehost) // validate URL format
	if err != nil {
		return nil, fmt.Errorf("invalid base host URL: %s, error: %v", basehost, err)
	}
	return &IngressNetworkBaseSetting{
		IngressClassName: opts.ClassName,
		BaseHost:         u.Hostname(),
		BasePort:         u.Port(),
		BaseSchema:       u.Scheme, // use the scheme from the parsed URL
		TLSSecretName:    opts.SecretName,
	}, nil
}
