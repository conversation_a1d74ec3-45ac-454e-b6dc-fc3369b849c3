package storageprovider

import (
	"encoding/json"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ StorageProvider = &cephFS{}

type cephFS struct {
	Name        string
	Provisioner string
	Monitors    []string
	ClusterID   string
	FsName      string
	UserID      string
	UserKey     string
}

func newCephFSProvider(name, provisioner string, paramters, configmaps, secret map[string]string) (StorageProvider, error) {
	cf := &cephFS{
		Name:        name,
		Provisioner: provisioner,
		ClusterID:   paramters["clusterID"],
		FsName:      paramters["fsName"],
	}
	type ConfigJson struct {
		//ClusterID string   `json:"clusterID"`
		Monitors []string `json:"monitors"`
	}
	var jsons []ConfigJson
	if err := json.Unmarshal([]byte(configmaps["config.json"]), &jsons); err != nil {
		return nil, err
	}
	if len(jsons) == 0 {
		return nil, fmt.Errorf("no monitors")
	}
	cf.Monitors = append(cf.Monitors, jsons[0].Monitors...)
	cf.UserID = secret["userID"]
	cf.UserKey = secret["userKey"]
	return cf, nil
}

// PersistentVolumeClaimContext implements controller.StorageProvision.
func (c *cephFS) PersistentVolumeClaimContext() (PersistentVolumeClaimContext, error) {
	uniqueName := "cephfs-" + c.Name
	size := resource.MustParse("1Pi")
	volumeattr := map[string]string{
		"mounter":      "kernel",
		"staticVolume": "true",
		"rootPath":     "/",
		"monitors":     strings.Join(c.Monitors, ","),
		"clusterID":    c.ClusterID,
		"fsName":       c.FsName,
	}

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniqueName + "-secret",
			Namespace: S3GatewayNamespace,
		},
		StringData: map[string]string{
			"userID":  c.UserID,
			"userKey": c.UserKey,
		},
	}
	secretref := &corev1.SecretReference{
		Name:      secret.Name,
		Namespace: secret.Namespace,
	}

	pv := corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: uniqueName,
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: size,
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:             c.Provisioner,
					VolumeHandle:       uniqueName,
					VolumeAttributes:   volumeattr,
					NodeStageSecretRef: secretref,
				},
			},
		},
	}
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniqueName,
			Namespace: S3GatewayNamespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: size,
				},
			},
			VolumeName:       pv.Name,
			StorageClassName: ptr.To(""),
		},
	}
	ret := PersistentVolumeClaimContext{
		PersistentVolumeClaim: pvc,
		PersistentVolume:      pv,
		ExtraResources:        []client.Object{secret},
	}
	return ret, nil
}

// GetPersistentVolumePath implements controller.StorageProvision.
// get dynamic pv's real path on cephfs
func (c *cephFS) GetPersistentVolumePath() string {
	return "subvolumePath"
}
