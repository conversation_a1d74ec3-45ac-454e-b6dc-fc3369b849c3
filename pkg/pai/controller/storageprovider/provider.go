package storageprovider

import (
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

const (
	S3GatewayNamespace  = "rune-s3gateway"
	CSIDriverNamespace  = "rune-csi-driver"
	S3GatewayRepository = "https://charts.kubegems.io/kubegems"
	S3GatewayChart      = "s3gateway"
	S3GatewayVersion    = "0.0.1"
)

type PersistentVolumeClaimContext struct {
	PersistentVolumeClaim corev1.PersistentVolumeClaim
	PersistentVolume      corev1.PersistentVolume
	ExtraResources        []client.Object
}

type StorageProvider interface {
	//GetPersistentVolumePath() string
	PersistentVolumeClaimContext() (PersistentVolumeClaimContext, error)
}

func GetStorageProvider(p paiv1beta1.StorageClusterProvider, name, provisioner string, paramters, configmaps, secret map[string]string) (StorageProvider, error) {
	switch p {
	case paiv1beta1.StorageClusterProviderCephFS:
		return newCephFSProvider(name, provisioner, paramters, configmaps, secret)
	case paiv1beta1.StorageClusterProviderJuiceFS:
		return newJuiceFSProvider(name, provisioner, secret)
	case paiv1beta1.StorageClusterProviderNFS:
		return newNFSProvision(name, provisioner, paramters)
	default:
		return nil, fmt.Errorf("unknown type:%v", p)
	}
}

func GetStorageProviderVolumePath(p paiv1beta1.StorageClusterProvider) string {
	switch p {
	case paiv1beta1.StorageClusterProviderCephFS:
		return "subvolumePath"
	case paiv1beta1.StorageClusterProviderJuiceFS:
		return "subPath"
	case paiv1beta1.StorageClusterProviderNFS:
		return "share"
	default:
		return ""
	}
}

type StorageProvisioner struct {
	Provisioner string
	Chart       string
	Url         string
	Version     string
}

func GetStorageProviderProvisioner(p paiv1beta1.StorageClusterProvider) (StorageProvisioner, error) {
	sp := StorageProvisioner{
		Url: "https://charts.kubegems.io/kubegems",
	}
	switch p {
	case paiv1beta1.StorageClusterProviderCephFS:
		sp.Provisioner = "cephfs.csi.ceph.com"
		sp.Chart = "storage-cephfs"
		sp.Version = "0.1.0"
		return sp, nil
	case paiv1beta1.StorageClusterProviderJuiceFS:
		sp.Provisioner = "csi.juicefs.com"
		sp.Chart = "storage-juicefs"
		sp.Version = "0.1.0"
		return sp, nil
	case paiv1beta1.StorageClusterProviderNFS:
		sp.Provisioner = "nfs.csi.k8s.io"
		sp.Chart = "storage-nfs"
		sp.Version = "0.1.0"
		return sp, nil
	default:
		return sp, fmt.Errorf("unknown type:%v", p)
	}
}
