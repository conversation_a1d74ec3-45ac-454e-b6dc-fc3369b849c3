package storageprovider

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ StorageProvider = &juiceFS{}

type juiceFS struct {
	Name          string
	Provisioner   string
	FsName        string
	Meta          string
	Storage       string
	Bucket        string
	AccessKey     string
	SecretKey     string
	FormatOptions string
}

func newJuiceFSProvider(name, provisioner string, secret map[string]string) (StorageProvider, error) {
	return &juiceFS{
		Name:          name,
		Provisioner:   provisioner,
		FsName:        secret["name"],
		Meta:          secret["metaurl"],
		Storage:       secret["storage"],
		Bucket:        secret["bucket"],
		AccessKey:     secret["access-key"],
		SecretKey:     secret["secret-key"],
		FormatOptions: secret["format-options"],
	}, nil
}

// https://juicefs.com/docs/zh/csi/guide/pv#static-provisioning
// PersistentVolumeClaimContext implements controller.StorageProvision.
func (j *juiceFS) PersistentVolumeClaimContext() (PersistentVolumeClaimContext, error) {
	uniqueName := "juicefs-" + j.Name
	size := resource.MustParse("1Pi")
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniqueName + "-secret",
			Namespace: S3GatewayNamespace,
		},
		StringData: map[string]string{
			"name":           j.FsName,
			"metaurl":        j.Meta,
			"storage":        j.Storage,
			"bucket":         j.Bucket,
			"access-key":     j.AccessKey,
			"secret-key":     j.SecretKey,
			"format-options": j.FormatOptions,
		},
	}
	secretref := &corev1.SecretReference{
		Name:      secret.Name,
		Namespace: secret.Namespace,
	}

	pv := corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: uniqueName,
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: size,
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:               j.Provisioner,
					VolumeHandle:         uniqueName,
					FSType:               "juicefs",
					NodePublishSecretRef: secretref,
				},
			},
		},
	}
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniqueName,
			Namespace: S3GatewayNamespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: size,
				},
			},
			VolumeName:       pv.Name,
			StorageClassName: ptr.To(""),
		},
	}
	ret := PersistentVolumeClaimContext{
		PersistentVolumeClaim: pvc,
		PersistentVolume:      pv,
		ExtraResources:        []client.Object{secret},
	}
	return ret, nil
}

// GetPersistentVolumePath implements controller.StorageProvision.
func (j *juiceFS) GetPersistentVolumePath() string {
	return "subPath"
}
