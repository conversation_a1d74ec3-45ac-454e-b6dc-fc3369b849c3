package storageprovider

import (
	"maps"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
)

var _ StorageProvider = &nfs{}

type nfs struct {
	Name        string
	Provisioner string
	Server      string
}

func newNFSProvision(name, provisioner string, paramters map[string]string) (StorageProvider, error) {
	return &nfs{
		Name:        name,
		Provisioner: provisioner,
		Server:      paramters["server"],
	}, nil
}

// PersistentVolumeClaimContext implements controller.StorageProvision.
func (n *nfs) PersistentVolumeClaimContext() (PersistentVolumeClaimContext, error) {
	uniqueName := "nfs-" + n.Name
	size := resource.MustParse("1Pi")
	volumeattr := map[string]string{}
	maps.Copy(volumeattr, map[string]string{
		"share":  "/",
		"server": n.Server,
	})

	pv := corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: uniqueName,
		},
		Spec: corev1.PersistentVolumeSpec{
			Capacity: corev1.ResourceList{
				corev1.ResourceStorage: size,
			},
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimRetain,
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:           n.Provisioner,
					VolumeHandle:     uniqueName,
					VolumeAttributes: volumeattr,
				},
			},
		},
	}
	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uniqueName,
			Namespace: S3GatewayNamespace,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteMany,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: size,
				},
			},
			VolumeName:       pv.Name,
			StorageClassName: ptr.To(""),
		},
	}
	ret := PersistentVolumeClaimContext{
		PersistentVolumeClaim: pvc,
		PersistentVolume:      pv,
		//ExtraResources:        []client.Object{secret},
	}
	return ret, nil
}

// GetPersistentVolumePath implements controller.StorageProvision.
func (n *nfs) GetPersistentVolumePath() string {
	return "share"
}
