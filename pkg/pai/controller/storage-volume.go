package controller

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	storagev1 "k8s.io/api/storage/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

const (
	StorageVolumeFinalizer = "storagevolume.finalizers.pai.kubegems.io"
)

func NewStorageVolumeController(cli client.Client) *StorageVolumeReconciler {
	return &StorageVolumeReconciler{
		Client: cli,
	}
}

type StorageVolumeReconciler struct {
	// Client is used to interact with the Kubernetes API
	Client client.Client
}

func (s *StorageVolumeReconciler) SetupController(ctx context.Context, mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.StorageVolume{}).
		Complete(
			NewBetterReconciler(s, mgr,
				WithFinalizer(StorageVolumeFinalizer),
				WithInjectFinalizers(metav1.FinalizerDeleteDependents),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

func (s *StorageVolumeReconciler) Sync(ctx context.Context, volume *paiv1beta1.StorageVolume) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage volume synced", "volume", volume.Name)
	scRef := volume.Spec.ClusterReference
	// scRef转化为StorageClass
	storageclass := &storagev1.StorageClass{}
	if err := s.Client.Get(ctx, client.ObjectKey{Name: scRef.Name}, storageclass); err != nil {
		return err
	}
	if *storageclass.VolumeBindingMode != storagev1.VolumeBindingImmediate {
		return fmt.Errorf("storage class %s is not immediate binding mode", scRef.Name)
	}
	// 需要考虑storageclass的绑定模式 Immediate/WaitForFirstConsumer
	// 暂时先支持 Immediate 模式，该模式创建pvc之后，csi会立刻创建对应的pv和该pvc绑定
	pvc := &corev1.PersistentVolumeClaim{ObjectMeta: metav1.ObjectMeta{Name: volume.Name, Namespace: volume.Namespace}}
	if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, pvc, func() error {
		//merge lables and annotations
		pai.MergeLabelsAnnotations(pvc, volume.Labels, volume.Annotations)
		if err := controllerutil.SetControllerReference(volume, pvc, s.Client.Scheme()); err != nil {
			return err
		}
		pvc.Spec.StorageClassName = pointer.String(storageclass.Name)
		pvc.Spec.AccessModes = []corev1.PersistentVolumeAccessMode{corev1.ReadWriteMany}
		pvc.Spec.Resources.Requests = corev1.ResourceList{
			corev1.ResourceStorage: volume.Spec.Capability,
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (s *StorageVolumeReconciler) Remove(ctx context.Context, volume *paiv1beta1.StorageVolume) error {
	return nil
}
