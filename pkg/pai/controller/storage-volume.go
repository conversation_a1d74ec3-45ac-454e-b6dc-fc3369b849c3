package controller

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
	"xiaoshiai.cn/rune/pkg/pai/controller/storageprovider"
)

const (
	StorageVolumeFinalizer = "storagevolume.finalizers.pai.kubegems.io"
)

func NewStorageVolumeController(cli client.Client) *StorageVolumeReconciler {
	return &StorageVolumeReconciler{
		Client: cli,
	}
}

type StorageVolumeReconciler struct {
	// Client is used to interact with the Kubernetes API
	Client client.Client
}

func (s *StorageVolumeReconciler) SetupController(ctx context.Context, mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.StorageVolume{}).
		Owns(&corev1.PersistentVolumeClaim{}).
		Complete(
			NewBetterReconciler(s, mgr,
				WithFinalizer(StorageVolumeFinalizer),
				WithInjectFinalizers(metav1.FinalizerDeleteDependents),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

func (s *StorageVolumeReconciler) Sync(ctx context.Context, volume *paiv1beta1.StorageVolume) error {
	log := logr.FromContextOrDiscard(ctx)
	log.Info("storage volume synced", "volume", volume.Name)
	cluster := &paiv1beta1.StorageCluster{}
	if err := s.Client.Get(ctx, client.ObjectKey{Name: volume.Spec.ClusterReference.Name}, cluster); err != nil {
		return err
	}
	realPath := storageprovider.GetStorageProviderVolumePath(cluster.Spec.Provider)
	// 需要考虑storageclass的绑定模式 Immediate/WaitForFirstConsumer
	// 暂时先支持 Immediate 模式，该模式创建pvc之后，csi会立刻创建对应的pv和该pvc绑定
	// if *storageclass.VolumeBindingMode != storagev1.VolumeBindingImmediate {
	// 	return fmt.Errorf("storage class %s is not immediate binding mode", scRef.Name)
	// }
	// 创建或更新 PVC
	pvc := &corev1.PersistentVolumeClaim{ObjectMeta: metav1.ObjectMeta{Name: volume.Name, Namespace: volume.Namespace}}
	if _, err := controllerutil.CreateOrUpdate(ctx, s.Client, pvc, func() error {
		//merge lables and annotations
		pai.MergeLabelsAnnotations(pvc, volume.Labels, volume.Annotations)
		if err := controllerutil.SetControllerReference(volume, pvc, s.Client.Scheme()); err != nil {
			return err
		}
		pvc.Spec.StorageClassName = ptr.To(cluster.Name)
		pvc.Spec.AccessModes = []corev1.PersistentVolumeAccessMode{corev1.ReadWriteMany}
		pvc.Spec.Resources.Requests = corev1.ResourceList{
			corev1.ResourceStorage: volume.Spec.Capability,
		}
		return nil
	}); err != nil {
		return err
	}
	// 等待 PVC 绑定到 PV
	return s.waitForPVCBinding(ctx, volume, pvc, realPath)
}

type getRealPathFn func() string

// waitForPVCBinding 等待 PVC 绑定到 PV 并更新 StorageVolume 状态
func (s *StorageVolumeReconciler) waitForPVCBinding(ctx context.Context, volume *paiv1beta1.StorageVolume, pvc *corev1.PersistentVolumeClaim, realpath string) error {
	log := logr.FromContextOrDiscard(ctx)
	// 重新获取 PVC 的最新状态
	currentPVC := &corev1.PersistentVolumeClaim{}
	if err := s.Client.Get(ctx, client.ObjectKeyFromObject(pvc), currentPVC); err != nil {
		log.Error(err, "failed to get PVC", "pvc", pvc.Name)
		return err
	}

	// 检查 PVC 绑定状态
	switch currentPVC.Status.Phase {
	case corev1.ClaimBound:
		// PVC 已绑定，更新 StorageVolume 状态
		log.Info("PVC is bound to PV", "pvc", currentPVC.Name, "pv", currentPVC.Spec.VolumeName)
		return s.updateStorageVolumeStatus(ctx, volume, currentPVC, paiv1beta1.StorageVolumePhaseBound, realpath, "PVC successfully bound to PV")

	case corev1.ClaimPending:
		// PVC 仍在等待绑定，继续等待
		log.Info("PVC is pending, waiting for PV binding", "pvc", currentPVC.Name)
		if err := s.updateStorageVolumeStatus(ctx, volume, currentPVC, paiv1beta1.StorageVolumePhasePending, realpath, "Waiting for PV to be created and bound"); err != nil {
			return err
		}
		// 返回 ErrRequeue 让 controller 重新调度
		return ErrRequeue
	case corev1.ClaimLost:
		// PVC 丢失，返回错误
		log.Error(nil, "PVC is lost", "pvc", currentPVC.Name)
		return s.updateStorageVolumeStatus(ctx, volume, currentPVC, paiv1beta1.StorageVolumePhaseLost, realpath, "PVC is lost")
	default:
		// 未知状态
		log.Info("PVC is in unknown state", "pvc", currentPVC.Name, "phase", currentPVC.Status.Phase)
		if err := s.updateStorageVolumeStatus(ctx, volume, currentPVC, paiv1beta1.StorageVolumePhaseUnknown, realpath, fmt.Sprintf("PVC is in unknown state: %s", currentPVC.Status.Phase)); err != nil {
			return err
		}
		return ErrRequeue
	}
}

// updateStorageVolumeStatus 更新 StorageVolume 的状态
func (s *StorageVolumeReconciler) updateStorageVolumeStatus(ctx context.Context, volume *paiv1beta1.StorageVolume, pvc *corev1.PersistentVolumeClaim, phase paiv1beta1.StorageVolumePhase, realpath, message string) error {
	log := logr.FromContextOrDiscard(ctx)
	// 更新基本状态字段
	volume.Status.Phase = phase
	volume.Status.Message = message
	volume.Status.PVCName = pvc.Name

	// 如果 PVC 已绑定到 PV，获取 PV 信息
	if pvc.Spec.VolumeName != "" {
		volume.Status.PVName = pvc.Spec.VolumeName

		// 获取 PV 信息来设置存储路径
		pv := &corev1.PersistentVolume{}
		if err := s.Client.Get(ctx, client.ObjectKey{Name: pvc.Spec.VolumeName}, pv); err == nil {
			// 从 PV 的 CSI 配置中提取存储路径
			if pv.Spec.CSI != nil && pv.Spec.CSI.VolumeAttributes != nil {
				if rootPath, exists := pv.Spec.CSI.VolumeAttributes[realpath]; exists {
					volume.Status.StoragePath = rootPath
				}
			}
		} else {
			log.Error(err, "failed to get PV", "pv", pvc.Spec.VolumeName)
		}
	}
	// 更新 Conditions
	s.updateConditions(volume, phase, message)
	return nil
}

// updateConditions 更新 StorageVolume 的 Conditions
func (s *StorageVolumeReconciler) updateConditions(volume *paiv1beta1.StorageVolume, phase paiv1beta1.StorageVolumePhase, message string) {
	conditionType := "Ready"
	conditionStatus := metav1.ConditionFalse
	reason := string(phase)

	if phase == paiv1beta1.StorageVolumePhaseBound {
		conditionStatus = metav1.ConditionTrue
		reason = "Bound"
	}

	// 查找现有的 Ready condition
	var existingCondition *metav1.Condition
	for i := range volume.Status.Conditions {
		if volume.Status.Conditions[i].Type == conditionType {
			existingCondition = &volume.Status.Conditions[i]
			break
		}
	}

	now := metav1.Now()
	newCondition := metav1.Condition{
		Type:               conditionType,
		Status:             conditionStatus,
		LastTransitionTime: now,
		Reason:             reason,
		Message:            message,
	}

	if existingCondition == nil {
		// 添加新的 condition
		volume.Status.Conditions = append(volume.Status.Conditions, newCondition)
	} else if existingCondition.Status != conditionStatus || existingCondition.Reason != reason {
		// 更新现有的 condition
		existingCondition.Status = conditionStatus
		existingCondition.LastTransitionTime = now
		existingCondition.Reason = reason
		existingCondition.Message = message
	} else {
		// 只更新消息，不更新时间戳
		existingCondition.Message = message
	}
}

func (s *StorageVolumeReconciler) Remove(ctx context.Context, volume *paiv1beta1.StorageVolume) error {
	// pvc has owner reference, so it will be deleted automatically
	return nil
}
