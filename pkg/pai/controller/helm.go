package controller

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	"helm.sh/helm/v3/pkg/chart/loader"
	"helm.sh/helm/v3/pkg/release"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	kubeyaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/rest"
	"xiaoshiai.cn/common/helm"
	"xiaoshiai.cn/common/log"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

type HelmApplyer struct {
	Config   *rest.Config
	CacheDir string
}

func NewHelmApplyer(config *rest.Config, cacheDir string) *HelmApplyer {
	return &HelmApplyer{Config: config, CacheDir: cacheDir}
}

func (r *HelmApplyer) downLoad(ctx context.Context, app *paiv1beta1.Application) (string, error) {
	name := app.Name
	if chart := app.Spec.Chart; chart != "" {
		name = chart
	}
	version := app.Spec.Version
	if version == "" {
		version = app.Status.Version // use the installed version
	}
	dir, err := helm.DownloadWithCache(ctx, app.Spec.URL, name, version, helm.DownloadOptions{
		Cachedir: r.CacheDir,
	})
	if err != nil {
		log.FromContext(ctx).Error(err, "failed to download chart", "app", app.Name)
		return "", err
	}
	return dir, nil
}

func (r *HelmApplyer) Template(ctx context.Context, app *paiv1beta1.Application) ([]byte, error) {
	rls := r.getPreRelease(app)
	dir, err := r.downLoad(ctx, app)
	if err != nil {
		return nil, err
	}
	chart, err := loader.Load(dir)
	if err != nil {
		return nil, fmt.Errorf("load chart: %w", err)
	}
	rs, err := helm.TemplateChart(ctx, rls.Name, rls.Namespace, chart, nil)
	return []byte(rs.Manifest), nil
}

func (r *HelmApplyer) Apply(ctx context.Context, app *paiv1beta1.Application) error {
	rls := r.getPreRelease(app)
	dir, err := r.downLoad(ctx, app)
	if err != nil {
		return err
	}
	overrideFiles := make(map[string][]byte, len(app.Spec.FileOverrides))
	for _, file := range app.Spec.FileOverrides {
		overrideFiles[file.Name] = []byte(file.Content)
	}
	applyedRelease, _, err := helm.ApplyChartFromFile(ctx, r.Config, dir, rls.Name, rls.Namespace, helm.ApplyChartOptions{
		FileOverrides: overrideFiles,
	})
	if err != nil {
		return err
	}
	app.Status.Resources = parseResourceReferences([]byte(applyedRelease.Manifest))
	if applyedRelease.Info.Status != release.StatusDeployed {
		return fmt.Errorf("apply not finished:%s", applyedRelease.Info.Description)
	}
	app.Status.Phase = paiv1beta1.PhaseInstalled
	app.Status.Message = applyedRelease.Info.Notes
	app.Status.Namespace = applyedRelease.Namespace
	app.Status.CreationTimestamp = convtime(applyedRelease.Info.FirstDeployed.Time)
	app.Status.UpgradeTimestamp = convtime(applyedRelease.Info.LastDeployed.Time)
	app.Status.Values = paiv1beta1.Values{Object: applyedRelease.Config}
	app.Status.Version = applyedRelease.Chart.Metadata.Version
	app.Status.AppVersion = applyedRelease.Chart.Metadata.AppVersion
	return nil
}

func (r *HelmApplyer) Remove(ctx context.Context, app *paiv1beta1.Application) error {
	logger := log.FromContext(ctx)
	if app.Status.Phase == paiv1beta1.PhaseDisabled {
		logger.Info("already removed or not installed")
		return nil
	}
	rls := r.getPreRelease(app)
	// uninstall
	removedRelease, err := helm.RemoveChart(ctx, r.Config, rls.Name, rls.Namespace)
	if err != nil {
		return err
	}
	log.Info("removed")
	if removedRelease == nil {
		app.Status.Phase = paiv1beta1.PhaseDisabled
		app.Status.Message = "plugin not install"
		return nil
	}
	app.Status.Phase = paiv1beta1.PhaseDisabled
	app.Status.Message = removedRelease.Info.Description
	return nil
}

func (r HelmApplyer) getPreRelease(app *paiv1beta1.Application) *release.Release {
	releaseNamespace := app.Spec.InstallNamespace
	if releaseNamespace == "" {
		releaseNamespace = app.Namespace
	}
	return &release.Release{Name: app.Name, Namespace: releaseNamespace, Config: app.Spec.Values.Object}
}

func parseResourceReferences(resources []byte) []paiv1beta1.ManagedResource {
	ress, _ := splitYAML(resources)
	managedResources := make([]paiv1beta1.ManagedResource, len(ress))
	for i, res := range ress {
		managedResources[i] = paiv1beta1.ManagedResource{
			APIVersion: res.GetObjectKind().GroupVersionKind().GroupVersion().String(),
			Kind:       res.GetObjectKind().GroupVersionKind().Kind,
			Name:       res.GetName(),
			Namespace:  res.GetNamespace(),
		}
	}
	return managedResources
}

func splitYAML(data []byte) ([]*unstructured.Unstructured, error) {
	d := kubeyaml.NewYAMLOrJSONDecoder(bytes.NewReader(data), 4096)
	var objs []*unstructured.Unstructured
	for {
		u := &unstructured.Unstructured{}
		if err := d.Decode(u); err != nil {
			if err == io.EOF {
				break
			}
			return objs, fmt.Errorf("failed to unmarshal manifest: %v", err)
		}
		if u.Object == nil || len(u.Object) == 0 {
			continue // skip empty object
		}
		objs = append(objs, u)
	}
	return objs, nil
}
func convtime(t time.Time) metav1.Time {
	t, _ = time.Parse(time.RFC3339, t.Format(time.RFC3339))
	return metav1.Time{Time: t}
}
