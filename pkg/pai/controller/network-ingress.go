package controller

import (
	"context"
	"net"
	"net/url"
	"strings"

	"golang.org/x/exp/maps"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/utils/pointer"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

// func ingressPrefix(ss *paiv1beta1.StorageSet) string {
// 	prefix := ""
// 	if labels := ss.Labels; labels != nil {
// 		switch labels[pai.LabelStorageSetKind] {
// 		case "dataset":
// 			prefix = "ds"
// 		case "modelset":
// 			prefix = "ms"
// 		case "output":
// 			prefix = "out"
// 		case "workspace":
// 			prefix = "pdw"
// 		}
// 	}
// 	return string(prefix)
// }

var _ NetworkProvider = &IngressNetworkProvider{}

type RuntimeIngressOptions struct {
	SecretName string `json:"secretName,omitempty"`
	ClassName  string `json:"className,omitempty"`
	BaseHost   string `json:"baseHost,omitempty"`
}
type IngressNetworkBaseSetting struct {
	IngressClassName string
	BaseHost         string
	BasePort         string
	BaseSchema       string // default is http/https, if TLSSecretName is set, it will be https
	TLSSecretName    string
}

func (s IngressNetworkBaseSetting) Schema() string {
	if s.TLSSecretName != "" {
		return "https"
	}
	if s.BaseSchema != "" {
		return s.BaseSchema
	}
	return "http"
}

func (s IngressNetworkBaseSetting) URL(hostnameoverride string) string {
	schema, host, port := s.Schema(), s.BaseHost, s.BasePort
	if hostnameoverride != "" {
		host = hostnameoverride
	}
	if port != "" {
		host = net.JoinHostPort(host, port)
	}
	u := url.URL{Scheme: schema, Host: host}
	return u.String()
}

type IngressNetworkProvider struct {
	Options *IngressNetworkBaseSetting
	Client  client.Client
}

func (p *IngressNetworkProvider) SyncJobNetwork(
	ctx context.Context, job *paiv1beta1.Job,
) (*NetworkProviderStatus, error) {
	return p.applySvcIngress(ctx, job)
}

func (p *IngressNetworkProvider) RemoveJobNetwork(ctx context.Context, job *paiv1beta1.Job) error {
	if err := p.removeSvcIngress(ctx, job); err != nil {
		return err
	}
	return nil
}

var defaultPathType = networkingv1.PathTypeImplementationSpecific

// nolint: funlen,gocognit
func (p *IngressNetworkProvider) applySvcIngress(
	ctx context.Context, job *paiv1beta1.Job,
) (*NetworkProviderStatus, error) {
	baseoptions := p.Options

	rolePorts := map[string][]paiv1beta1.JobRolPortStatus{}
	for _, role := range job.Spec.Roles {
		if len(role.Ports) == 0 {
			continue
		}
		roleitemname := p.roleItemName(*job, role)
		svc := &corev1.Service{ObjectMeta: metav1.ObjectMeta{Name: roleitemname, Namespace: job.Namespace}}
		_, err := controllerutil.CreateOrUpdate(ctx, p.Client, svc, func() error {
			pai.MergeLabelsAnnotations(svc, job.Labels, job.Annotations)

			// owner reference
			if err := controllerutil.SetControllerReference(job, svc, p.Client.Scheme()); err != nil {
				return err
			}

			existsports := map[string]*corev1.ServicePort{}
			for i, p := range svc.Spec.Ports {
				existsports[p.Name] = &svc.Spec.Ports[i]
			}
			for _, port := range role.Ports {
				existsport, ok := existsports[port.Name]
				if !ok {
					svc.Spec.Ports = append(svc.Spec.Ports, corev1.ServicePort{Name: port.Name})
					existsport = &svc.Spec.Ports[len(svc.Spec.Ports)-1]
				}
				existsport.Port = port.Port
				existsport.TargetPort = intstr.FromString(port.Name)
			}
			if svc.Spec.Selector == nil {
				svc.Spec.Selector = make(map[string]string)
			}
			maps.Copy(svc.Spec.Selector, JobRolePodLabel(job, role))
			return nil
		})
		if err != nil {
			return nil, err
		}
		// ingress
		ingress := &networkingv1.Ingress{ObjectMeta: metav1.ObjectMeta{Name: roleitemname, Namespace: job.Namespace}}
		if _, err = controllerutil.CreateOrUpdate(ctx, p.Client, ingress, func() error {
			var portsStatus []paiv1beta1.JobRolPortStatus
			pai.MergeLabelsAnnotations(ingress, nil, map[string]string{
				"nginx.ingress.kubernetes.io/proxy-body-size": "0",
			})
			pai.MergeLabelsAnnotations(ingress, job.Labels, job.Annotations)
			// owner reference
			if err := controllerutil.SetControllerReference(job, ingress, p.Client.Scheme()); err != nil {
				return err
			}
			if job.Annotations != nil && job.Annotations[pai.AnnotationIngressClassName] != "" {
				ingress.Spec.IngressClassName = pointer.String(job.Annotations[pai.AnnotationIngressClassName])
			}
			if ingress.Spec.IngressClassName == nil && baseoptions.IngressClassName != "" {
				ingress.Spec.IngressClassName = pointer.String(baseoptions.IngressClassName)
			}
			for i, port := range role.Ports {
				if len(ingress.Spec.Rules) <= i {
					ingress.Spec.Rules = append(ingress.Spec.Rules, networkingv1.IngressRule{})
				}
				if ingress.Spec.Rules[i].Host == "" {
					ingresshost := IngressHost("im", job.Name, baseoptions.BaseHost)
					if job.Labels != nil && job.Labels[pai.LabelJobKind] == pai.JobKindInference {
						ingresshost = IngressHost("inf", job.Name, baseoptions.BaseHost)
					}
					ingress.Spec.Rules[i].Host = ingresshost
				}
				ingress.Spec.Rules[i].IngressRuleValue = networkingv1.IngressRuleValue{HTTP: &networkingv1.HTTPIngressRuleValue{
					Paths: []networkingv1.HTTPIngressPath{{
						Path:     "/",
						PathType: &defaultPathType,
						Backend: networkingv1.IngressBackend{
							Service: &networkingv1.IngressServiceBackend{
								Name: roleitemname, Port: networkingv1.ServiceBackendPort{Name: port.Name},
							},
						},
					}},
				}}
				portsStatus = append(portsStatus, paiv1beta1.JobRolPortStatus{
					Name: port.Name,
					Port: port.Port,
					Access: &paiv1beta1.JobSpecPortAccess{
						Type: paiv1beta1.JobAccessType(strings.ToUpper(baseoptions.Schema())),
						Host: baseoptions.URL(ingress.Spec.Rules[i].Host),
					},
				})
			}
			if baseoptions.TLSSecretName != "" {
				hosts := []string{}
				for _, rule := range ingress.Spec.Rules {
					hosts = append(hosts, rule.Host)
				}
				if len(ingress.Spec.TLS) == 0 {
					ingress.Spec.TLS = []networkingv1.IngressTLS{{}}
				}
				ingress.Spec.TLS[0].SecretName = baseoptions.TLSSecretName
				ingress.Spec.TLS[0].Hosts = hosts
			}
			rolePorts[role.Name] = portsStatus
			return nil
		}); err != nil {
			return nil, err
		}
	}
	return &NetworkProviderStatus{RolePorts: rolePorts}, nil
}

func (p *IngressNetworkProvider) roleItemName(job paiv1beta1.Job, role paiv1beta1.JobRole) string {
	return job.Name + "-" + role.Name
}

func (p *IngressNetworkProvider) removeSvcIngress(ctx context.Context, job *paiv1beta1.Job) error {
	for _, role := range job.Spec.Roles {
		roleitemname := p.roleItemName(*job, role)
		ingress := &networkingv1.Ingress{ObjectMeta: metav1.ObjectMeta{Name: roleitemname, Namespace: job.Namespace}}
		if err := client.IgnoreNotFound(p.Client.Delete(ctx, ingress)); err != nil {
			return err
		}
		svc := &corev1.Service{ObjectMeta: metav1.ObjectMeta{Name: roleitemname, Namespace: job.Namespace}}
		if err := client.IgnoreNotFound(p.Client.Delete(ctx, svc)); err != nil {
			return err
		}
	}
	return nil
}
