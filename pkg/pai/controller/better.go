package controller

import (
	"context"
	"errors"
	"reflect"
	"strconv"
	"sync"
	"time"

	"github.com/go-logr/logr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

type SetupController interface {
	SetupController(ctx context.Context, mgr ctrl.Manager) error
}

type SetupWebhook interface {
	SetupWebhook(ctx context.Context, mgr ctrl.Manager) error
}

type BetterReconciler[T client.Object] interface {
	Sync(ctx context.Context, obj T) error
	Remove(ctx context.Context, obj T) error
}

type BaseBetterReconciler[T client.Object] struct {
	Options BetterReconcilerOptions
	Client  client.Client
	BetterReconciler[T]

	mu              sync.Mutex
	reversionStatus map[string]int64
}

type ReversionCheckPolicy string

const (
	ReversionCheckPolicyBiggerThan ReversionCheckPolicy = ">"
	ReversionCheckPolicyNotLess    ReversionCheckPolicy = ">="
)

type BetterReconcilerOptions struct {
	finalizer            string
	injectFinalizers     []string
	reversionCheckPolicy ReversionCheckPolicy
	skipSetMessage       bool
}

func WithFinalizer(finalizer string) BetterReconcilerOption {
	return func(o *BetterReconcilerOptions) {
		o.finalizer = finalizer
	}
}

func WithInjectFinalizers(finalizers ...string) BetterReconcilerOption {
	return func(o *BetterReconcilerOptions) {
		o.injectFinalizers = append(o.injectFinalizers, finalizers...)
	}
}

func WithReversionCheckPolicy(policy ReversionCheckPolicy) BetterReconcilerOption {
	return func(o *BetterReconcilerOptions) {
		o.reversionCheckPolicy = policy
	}
}

var ErrRequeue = errors.New("requeue")

type BetterReconcilerOption func(*BetterReconcilerOptions)

func NewBetterReconciler[T client.Object](br BetterReconciler[T],
	mgr ctrl.Manager, options ...BetterReconcilerOption,
) reconcile.Reconciler {
	opts := &BetterReconcilerOptions{}
	for _, opt := range options {
		opt(opts)
	}
	return &BaseBetterReconciler[T]{
		Options:          *opts,
		BetterReconciler: br,
		Client:           mgr.GetClient(),
	}
}

func (r *BaseBetterReconciler[T]) canPassRevCheck(req ctrl.Request, obj client.Object) bool {
	// check reversion is old, when cache reconcile request which is not the latest version
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.reversionStatus == nil {
		r.reversionStatus = map[string]int64{}
	}
	revkey := req.String()
	reqrev, _ := strconv.ParseInt(obj.GetResourceVersion(), 10, 64)
	if prerev, ok := r.reversionStatus[revkey]; ok {
		isPass := false
		switch r.Options.reversionCheckPolicy {
		case ReversionCheckPolicyBiggerThan:
			isPass = prerev < reqrev
		case ReversionCheckPolicyNotLess, "":
			isPass = prerev <= reqrev
		default:
			isPass = true
		}
		if isPass {
			r.reversionStatus[revkey] = reqrev
			return true
		} else {
			return false
		}
	} else {
		r.reversionStatus[revkey] = reqrev
		return true
	}
}

// nolint: funlen,gocognit
func (r *BaseBetterReconciler[T]) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := logr.FromContextOrDiscard(ctx)

	log.Info("start reconcile")
	defer log.Info("finish reconcile")

	obj, _ := reflect.New(reflect.TypeOf(*new(T)).Elem()).Interface().(T)
	if err := r.Client.Get(ctx, req.NamespacedName, obj); err != nil {
		log.Error(err, "unable to fetch")
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	// a workaround for
	// https://github.com/kubernetes-sigs/controller-runtime/issues/1622
	if !r.canPassRevCheck(req, obj) {
		log.Info("skip reconcile, request is old, requeue")
		return ctrl.Result{Requeue: true, RequeueAfter: time.Second * 3}, nil
	}
	defer func() {
		// update rev after sync
		_ = r.canPassRevCheck(req, obj)
	}()

	finalizer := r.Options.finalizer
	if obj.GetDeletionTimestamp() != nil {
		log.Info("is being deleted")
		if finalizer != "" && !controllerutil.ContainsFinalizer(obj, finalizer) {
			return ctrl.Result{}, nil
		}
		if err := r.BetterReconciler.Remove(ctx, obj); err != nil {
			log.Error(err, "unable to remove")
			return ctrl.Result{}, err
		}
		if finalizer != "" {
			controllerutil.RemoveFinalizer(obj, finalizer)
		}
		if err := r.Client.Update(ctx, obj); err != nil {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}
	if r.Options.injectFinalizers != nil {
		changed := false
		for _, finalizer := range r.Options.injectFinalizers {
			if !controllerutil.ContainsFinalizer(obj, finalizer) {
				controllerutil.AddFinalizer(obj, finalizer)
				changed = true
			}
		}
		if changed {
			if err := r.Client.Update(ctx, obj); err != nil {
				return ctrl.Result{}, err
			}
		}
	}
	if finalizer != "" && !controllerutil.ContainsFinalizer(obj, finalizer) {
		controllerutil.AddFinalizer(obj, finalizer)
		if err := r.Client.Update(ctx, obj); err != nil {
			return ctrl.Result{}, err
		}
	}
	err := r.BetterReconciler.Sync(ctx, obj)
	if errors.Is(err, ErrRequeue) {
		return ctrl.Result{Requeue: true, RequeueAfter: time.Second * 3}, nil
	}
	if err != nil {
		log.Error(err, "unable to sync")
		if !r.Options.skipSetMessage {
			r.reflectSetMessage(obj, err.Error())
		}
	}
	if updateerr := r.Client.Status().Update(ctx, obj); updateerr != nil {
		log.Error(updateerr, "unable to update status")
		return ctrl.Result{}, updateerr
	}
	return ctrl.Result{}, err
}

func (r *BaseBetterReconciler[T]) reflectSetMessage(obj any, msg string) {
	v := reflect.ValueOf(obj)
	for v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return
	}

	statusv := v.FieldByName("Status")
	if !statusv.IsValid() || statusv.Kind() != reflect.Struct {
		return
	}
	msgv := statusv.FieldByName("Message")
	if !msgv.IsValid() || msgv.Kind() != reflect.String {
		return
	}
	if msgv.CanSet() {
		msgv.SetString(msg)
	}
}
