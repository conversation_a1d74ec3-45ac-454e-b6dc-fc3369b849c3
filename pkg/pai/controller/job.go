package controller

import (
	"context"
	"sync"
	"time"

	"github.com/containers/image/v5/docker/reference"
	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/utils/ptr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	batchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	schedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

const (
	JobFinalizer        = "job.finalizers.pai.kubegems.io"
	DefaultHistoryLimit = 10
	LabelValueTrue      = "true"
)

func JobRolePodLabel(job *paiv1beta1.Job, role paiv1beta1.JobRole) map[string]string {
	return map[string]string{
		pai.LabelJobName:  job.Name,
		pai.LabelRoleName: role.Name,
	}
}

func NewJobReconciler(cli client.Client, options *Options,
	networkProvider NetworkProvider, jobProvider JobProvider,
) *JobReconciler {
	return &JobReconciler{
		Client:          cli,
		NetworkProvider: networkProvider,
		JobProvider:     jobProvider,
		Options:         options,
		cache: &StatusCache{
			data: make(map[client.ObjectKey]*StatusCacheDetaisl),
		},
	}
}

type JobReconciler struct {
	Client          client.Client
	NetworkProvider NetworkProvider
	JobProvider     JobProvider
	Options         *Options

	cache *StatusCache
}

func (r *JobReconciler) SetupController(_ context.Context, mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&paiv1beta1.Job{}).
		Owns(&batchv1alpha1.Job{}, builder.WithPredicates(r.volcanoJobChanges())).
		// we enqueue the volcano job name/namespace when the podgroup phase changes
		// it is just ok when pai job name is same as volcano job name
		Watches(
			&schedulingv1beta1.PodGroup{},
			handler.EnqueueRequestForOwner(GetScheme(), mgr.GetRESTMapper(), &batchv1alpha1.Job{}),
			builder.WithPredicates(r.volcanoPodGroupPhaseChanges()),
		).
		Complete(
			NewBetterReconciler(r, mgr,
				WithFinalizer(JobFinalizer),
				WithReversionCheckPolicy(ReversionCheckPolicyNotLess),
			),
		)
}

func isFinalPhase(phase paiv1beta1.JobPhase) bool {
	return phase == paiv1beta1.JobPhaseCompleted ||
		phase == paiv1beta1.JobPhaseFailed ||
		phase == paiv1beta1.JobPhasePaused ||
		phase == paiv1beta1.JobPhaseUnknown
}

func (r *JobReconciler) Sync(ctx context.Context, job *paiv1beta1.Job) error {
	log := logr.FromContextOrDiscard(ctx)
	cacheStatus := r.cache.Get(job)

	if updated, err := r.setReplicasAdjust(ctx, job); err != nil {
		return err
	} else if updated {
		// update replicas and wait for next reconcile
		return r.Client.Update(ctx, job)
	}
	pauseSwitched := cacheStatus.Paused != job.Spec.Paused
	cacheStatus.Paused = job.Spec.Paused
	if job.Spec.Paused {
		log.Info("job is paused, check job", "job", job.Name)
		return r.pause(ctx, job)
	}
	// from paused to running
	if pauseSwitched && !job.Spec.Paused {
		log.Info("job is unpaused, start job", "job", job.Name)
		return r.start(ctx, job)
	}
	// check exists job status
	jobuid, jobstatus, err := r.JobProvider.CheckJob(ctx, job)
	if err != nil {
		return err
	}
	networkStatus, err := r.NetworkProvider.SyncJobNetwork(ctx, job)
	if err != nil {
		return err
	}
	r.networkStatus(ctx, job, networkStatus)
	// create a new job
	if jobuid == "" {
		log.Info("job not exists, start job", "job", job.Name)
		return r.start(ctx, job)
	}
	log.Info("job exists, sync job", "job", job.Name)
	// sync job and get status
	if err := r.JobProvider.SyncJob(ctx, job); err != nil {
		return err
	}
	// job already reached a result
	if jobstatus.FinishTimestamp != nil {
		// we got an outdated job status from cache
		if job.Status.State.StartTimestamp != nil && jobstatus.FinishTimestamp.Before(job.Status.State.StartTimestamp) {
			log.Error(nil, "job status is outdated, ignore", "job", job.Name)
			return nil
		}
		return r.finished(ctx, job, jobstatus)
	}
	// update status
	if err := r.jobStatus(ctx, job, jobstatus); err != nil {
		return err
	}
	return nil
}

func (r *JobReconciler) setReplicasAdjust(ctx context.Context, job *paiv1beta1.Job) (bool, error) {
	log := logr.FromContextOrDiscard(ctx)
	hpaRole := getHPARole(job)
	if job.Spec.Replicas == nil {
		job.Spec.Replicas = ptr.To(int32(hpaRole.Replicas))
		log.Info("job replicas not set, set to role replicas", "role", hpaRole.Name, "replicas", hpaRole.Replicas)
		return true, nil
	} else {
		old, now := hpaRole.Replicas, *job.Spec.Replicas
		if old != now {
			log.Info("job replicas changed, update role replicas", "old", old, "new", now, "role", hpaRole.Name)
			hpaRole.Replicas = now
			return true, nil
		}
	}
	return false, nil
}

func (r *JobReconciler) jobStatus(_ context.Context, job *paiv1beta1.Job, jobstatus *JobProviderStatus) error {
	// update status
	job.Status.State.Phase = jobstatus.Phase
	if job.Status.State.StartTimestamp == nil {
		job.Status.State.StartTimestamp = jobstatus.StartTimestamp
	}
	job.Status.State.RunningTimestamp = jobstatus.RunningTimestamp
	job.Status.State.FinishTimestamp = jobstatus.FinishTimestamp
	job.Status.State.Reason = jobstatus.Message
	job.Status.Message = jobstatus.Message
	// update role status
	updateRoleStatusFunc(job, func(role paiv1beta1.JobRole, stats *paiv1beta1.JobRoleStatus) {
		newstatus := jobstatus.Roles[stats.Name]
		stats.Resources = newstatus.Resources
		stats.Replicas.Desired = newstatus.DesiredReplicas
		stats.Replicas.Current = newstatus.CurrentReplicas
		stats.Replicas.Running = newstatus.RunnigReplicas
	})
	// hpa replicas status
	hpaRole := getHPARole(job)
	job.Status.Replicas = jobstatus.Roles[hpaRole.Name].CurrentReplicas
	job.Status.Selector = labels.SelectorFromValidatedSet(JobRolePodLabel(job, *hpaRole)).String()
	return nil
}

func getHPARole(job *paiv1beta1.Job) *paiv1beta1.JobRole {
	if len(job.Spec.Roles) == 0 {
		return &paiv1beta1.JobRole{}
	}
	return &job.Spec.Roles[len(job.Spec.Roles)-1]
}

func (r *JobReconciler) networkStatus(_ context.Context, job *paiv1beta1.Job, status *NetworkProviderStatus) {
	updateRoleStatusFunc(job, func(role paiv1beta1.JobRole, stats *paiv1beta1.JobRoleStatus) {
		stats.Ports = status.RolePorts[stats.Name]
	})
}

func updateRoleStatusFunc(job *paiv1beta1.Job, fn func(role paiv1beta1.JobRole, stats *paiv1beta1.JobRoleStatus)) {
	updatedRolesStatus := make([]paiv1beta1.JobRoleStatus, 0, len(job.Spec.Roles))
	oldRolesStatus := make(map[string]paiv1beta1.JobRoleStatus, len(job.Status.Roles))
	for _, role := range job.Status.Roles {
		oldRolesStatus[role.Name] = role
	}
	for _, role := range job.Spec.Roles {
		rolestatus, ok := oldRolesStatus[role.Name]
		if !ok {
			// init emmpty role status
			rolestatus = paiv1beta1.JobRoleStatus{Name: role.Name}
		}
		// update role
		fn(role, &rolestatus)
		updatedRolesStatus = append(updatedRolesStatus, rolestatus)
	}
	job.Status.Roles = updatedRolesStatus
}

func (r *JobReconciler) pause(ctx context.Context, job *paiv1beta1.Job) error {
	log := logr.FromContextOrDiscard(ctx)
	// ignore if job is already reached a final phase
	if isFinalPhase(job.Status.State.Phase) {
		return nil
	}
	job.Status.State.Phase = paiv1beta1.JobPhasePaused
	job.Status.State.Reason = "Paused"
	job.Status.State.FinishTimestamp = &metav1.Time{Time: time.Now()}
	// remve job when initiative paused
	log.Info("job is paused, remove job", "job", job.Name)
	if err := r.JobProvider.RemoveJob(ctx, job); err != nil {
		return err
	}
	return nil
}

func (r *JobReconciler) finished(ctx context.Context, job *paiv1beta1.Job, jobstatus *JobProviderStatus) error {
	job.Status.State.Phase = jobstatus.Phase
	job.Status.State.FinishTimestamp = jobstatus.FinishTimestamp
	if job.Status.State.StartTimestamp == nil {
		job.Status.State.StartTimestamp = jobstatus.StartTimestamp
	}
	job.Status.Message = jobstatus.Message

	// update status
	if err := r.Client.Status().Update(ctx, job); err != nil {
		return err
	}
	// set spec to paused
	job.Spec.Paused = true
	return r.Client.Update(ctx, job)
}

func (r *JobReconciler) start(ctx context.Context, job *paiv1beta1.Job) error {
	// reset status
	if isFinalPhase(job.Status.State.Phase) {
		r.recordHistory(ctx, job)
		job.Status.Message = ""
		job.Status.State = paiv1beta1.JobStatusState{
			Phase:          paiv1beta1.JobPhasePending,
			StartTimestamp: &metav1.Time{Time: time.Now()},
		}
	}
	// check sku limit
	// if err := r.checkSKULimit(ctx, job); err != nil {
	// 	job.Status.State.Phase = paiv1beta1.JobPhaseFailed
	// 	return fmt.Errorf("check sku limit: %w", err)
	// }
	// job image pull secret
	if err := r.syncJobPullSecret(ctx, job); err != nil {
		return err
	}
	if err := r.syncVisual(ctx, job); err != nil {
		return err
	}
	// if err := r.labelStorageset(ctx, job); err != nil {
	// 	return err
	// }
	if err := r.JobProvider.RemoveJob(ctx, job); err != nil {
		return err
	}
	if err := r.JobProvider.SyncJob(ctx, job); err != nil {
		return err
	}
	networkStatus, err := r.NetworkProvider.SyncJobNetwork(ctx, job)
	if err != nil {
		return err
	}
	r.networkStatus(ctx, job, networkStatus)
	return nil
}

func (r *JobReconciler) syncVisual(ctx context.Context, job *paiv1beta1.Job) error {
	for _, role := range job.Spec.Roles {
		if role.Annotations == nil {
			continue
		}
		logdir := role.Annotations[pai.AnnotationJobTensorboardLogdir]
		if logdir == "" {
			continue
		}
		// if err := r.syncVisualForRole(ctx, job, &role, logdir); err != nil {
		// 	return err
		// }
	}
	return nil
}

// func (r *JobReconciler) syncVisualForRole(ctx context.Context,
// 	job *paiv1beta1.Job,
// 	role *paiv1beta1.JobRole,
// 	logdir string,
// ) error {
// 	for _, mount := range role.Mounts {
// 		if mount.Readonly {
// 			continue
// 		}
// 		if !strings.HasPrefix(logdir, mount.Path) {
// 			continue
// 		}
// 		log := logr.FromContextOrDiscard(ctx)
// 		log.Info("found logdir, sync visual config for role", "role", role.Name, "logdir", logdir)
// 		vc := &paiv1beta1.VisualConfig{
// 			ObjectMeta: metav1.ObjectMeta{
// 				Name:      job.Name,
// 				Namespace: job.Namespace,
// 			},
// 		}
// 		_, err := controllerutil.CreateOrPatch(ctx, r.Client, vc, func() error {
// 			// set owner reference to enable garbage collection
// 			if err := controllerutil.SetControllerReference(job, vc, r.Client.Scheme()); err != nil {
// 				return err
// 			}
// 			pai.MergeLabelsAnnotations(vc, job.Labels, job.Annotations)
// 			vc.Spec.TensorBoardSpec = &paiv1beta1.TensorBoardSpec{
// 				Enabled: true,
// 				LogDir:  logdir,
// 				Image:   r.Options.Images.Ternsorboard,
// 				PVCReference: paiv1beta1.PVCReference{
// 					Name:      mount.Name,
// 					MountPath: mount.Path,
// 				},
// 			}
// 			return nil
// 		})
// 		if err != nil {
// 			return err
// 		}
// 		// only process one role
// 		return nil
// 	}
// 	return nil
// }

// 创建数据标注job的时候，给选定的数据集加上 pai.kubegems.io/labeling  的label，删除数据标注job的时候，移除这个label  @一只大坑
// func (r *JobReconciler) labelStorageset(ctx context.Context, job *paiv1beta1.Job) error {
// 	if job.Labels == nil || job.Labels[pai.LabelJobKind] != pai.JobsKindLabeling {
// 		return nil
// 	}
// 	return onJobStorageSet(ctx, r.Client, job,
// 		func(ctx context.Context, cli client.Client, ss *paiv1beta1.StorageSet) error {
// 			if ss.Labels != nil && ss.Labels[pai.LabelStorageSetKind] == pai.LabelStorageSetKindDataSet {
// 				if _, ok := ss.Labels[pai.LabelIsLabeling]; ok {
// 					return nil
// 				}
// 				patch := client.RawPatch(types.MergePatchType,
// 					fmt.Appendf(nil, `{"metadata":{"labels":{"%s":"%s"}}}`, pai.LabelIsLabeling, LabelValueTrue),
// 				)
// 				return cli.Patch(ctx, ss, patch)
// 			}
// 			return nil
// 		})
// }

// func (r *JobReconciler) unlabelStorageset(ctx context.Context, job *paiv1beta1.Job) error {
// 	if job.Labels == nil || job.Labels[pai.LabelJobKind] != pai.JobsKindLabeling {
// 		return nil
// 	}
// 	return onJobStorageSet(ctx, r.Client, job,
// 		func(ctx context.Context, cli client.Client, ss *paiv1beta1.StorageSet) error {
// 			if ss.Labels != nil && ss.Labels[pai.LabelStorageSetKind] == pai.LabelStorageSetKindDataSet {
// 				if _, ok := ss.Labels[pai.LabelIsLabeling]; !ok {
// 					return nil
// 				}
// 				patch := client.RawPatch(types.MergePatchType,
// 					fmt.Appendf(nil, `{"metadata":{"labels":{"%s":null}}}`, pai.LabelIsLabeling),
// 				)
// 				return cli.Patch(ctx, ss, patch)
// 			}
// 			return nil
// 		})
// }

// func onJobStorageSet(ctx context.Context, cli client.Client, job *paiv1beta1.Job,
// 	fn func(ctx context.Context, cli client.Client, ss *paiv1beta1.StorageSet) error,
// ) error {
// 	for _, role := range job.Spec.Roles {
// 		for _, mount := range role.Mounts {
// 			ss := &paiv1beta1.StorageSet{}
// 			if err := cli.Get(ctx, client.ObjectKey{Name: mount.Name, Namespace: job.Namespace}, ss); err != nil {
// 				if errors.IsNotFound(err) {
// 					continue
// 				}
// 				return err
// 			}
// 			if err := fn(ctx, cli, ss); err != nil {
// 				return err
// 			}
// 		}
// 	}
// 	return nil
// }

func (r *JobReconciler) recordHistory(_ context.Context, job *paiv1beta1.Job,
) {
	prehistory := job.Status.State.DeepCopy()
	// avoid record empty history or pending history
	if prehistory.StartTimestamp.IsZero() || prehistory.Phase == paiv1beta1.JobPhasePending ||
		prehistory.Phase == paiv1beta1.JobPhaseFailed || prehistory.Phase == paiv1beta1.JobPhaseUnknown {
		return
	}
	// record a history of job status
	job.Status.History = append([]paiv1beta1.JobStatusState{*prehistory}, job.Status.History...)
	// remove the old history
	if job.Spec.HistoryLimit <= 0 {
		job.Spec.HistoryLimit = DefaultHistoryLimit
	}
	// remove last history
	if len(job.Status.History) > job.Spec.HistoryLimit {
		job.Status.History = job.Status.History[:job.Spec.HistoryLimit]
	}
}

func (r *JobReconciler) syncJobPullSecret(ctx context.Context, job *paiv1beta1.Job) error {
	for i, role := range job.Spec.Roles {
		if len(job.Spec.Roles[i].ImagePullSecrets) != 0 ||
			role.Annotations == nil || role.Annotations[pai.AnnotationImageAuth] == "" {
			continue
		}
		image, imageauth := role.Image, role.Annotations[pai.AnnotationImageAuth]
		if imageauth == "" || image == "" {
			continue
		}
		named, err := reference.ParseNormalizedNamed(image)
		if err != nil {
			continue
		}
		domain := reference.Domain(named)
		if domain == "" {
			domain = "docker.io"
		}
		secret := &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      job.Name + "-" + role.Name + "-image-pull-secret",
				Namespace: job.Namespace,
			},
		}
		if _, err := controllerutil.CreateOrUpdate(ctx, r.Client, secret, func() error {
			secret.Type = corev1.SecretTypeDockerConfigJson
			secret.Data = map[string][]byte{
				corev1.DockerConfigJsonKey: []byte(`{"auths":{"` + domain + `":{"auth":"` + imageauth + `"}}}`),
			}
			return ctrl.SetControllerReference(job, secret, r.Client.Scheme()) // cascade delete secret when job deleted
		}); err != nil {
			return err
		}
		job.Spec.Roles[i].ImagePullSecrets = append(
			job.Spec.Roles[i].ImagePullSecrets,
			corev1.LocalObjectReference{Name: secret.Name})
	}
	return nil
}

func (r *JobReconciler) Remove(ctx context.Context, job *paiv1beta1.Job) error {
	if err := r.JobProvider.RemoveJob(ctx, job); err != nil {
		return err
	}
	if err := r.NetworkProvider.RemoveJobNetwork(ctx, job); err != nil {
		return err
	}
	// if err := r.unlabelStorageset(ctx, job); err != nil {
	// 	return err
	// }
	return nil
}

// func (r *JobReconciler) checkSKULimit(ctx context.Context, job *paiv1beta1.Job) error {
// 	if job.Labels == nil {
// 		return nil
// 	}
// 	tenantname := job.Labels[pai.LabelTenant]
// 	if tenantname == "" {
// 		return nil
// 	}
// 	tenant := &paiv1beta1.Tenant{
// 		ObjectMeta: metav1.ObjectMeta{Name: tenantname, Namespace: job.Namespace},
// 	}
// 	if err := client.IgnoreNotFound(r.Client.Get(ctx, client.ObjectKeyFromObject(tenant), tenant)); err != nil {
// 		return err
// 	}
// 	skulimits := make(map[string]int64)
// 	for resourcename, capacity := range tenant.Spec.Resources.Limits {
// 		if !strings.HasPrefix(string(resourcename), "sku/") {
// 			continue
// 		}
// 		// zero is no limit
// 		if capacity.IsZero() {
// 			continue
// 		}
// 		skulimits[strings.TrimPrefix(string(resourcename), "sku/")] = capacity.Value()
// 	}
// 	// no sku limit for tenant
// 	if len(skulimits) == 0 {
// 		return nil
// 	}
// 	// find inuse sku
// 	joblist := &paiv1beta1.JobList{}
// 	if err := r.Client.List(ctx, joblist,
// 		client.InNamespace(job.Namespace),
// 		client.MatchingLabels{pai.LabelTenant: tenantname}); err != nil {
// 		return err
// 	}
// 	inusesku := make(map[string]int64)
// 	for _, job := range joblist.Items {
// 		if job.Status.State.Phase != paiv1beta1.JobPhaseRunning &&
// 			job.Status.State.Phase != paiv1beta1.JobPhasePending {
// 			continue
// 		}
// 		for _, role := range job.Spec.Roles {
// 			sku := role.StockKeepingUnitName
// 			if sku == "" {
// 				continue
// 			}
// 			inusesku[sku] = inusesku[sku] + role.Replicas
// 		}
// 	}
// 	for _, role := range job.Spec.Roles {
// 		skuname := role.StockKeepingUnitName
// 		if skuname == "" {
// 			continue
// 		}
// 		if limit, haslimit := skulimits[skuname]; haslimit && inusesku[skuname] >= limit {
// 			return fmt.Errorf("role [%s]: sku [%s] reach limit %d/%d", role.Name, skuname, inusesku[skuname], limit)
// 		}
// 	}
// 	return nil
// }

type StatusCache struct {
	data map[client.ObjectKey]*StatusCacheDetaisl
	mu   sync.RWMutex
}

func (c *StatusCache) Get(obj *paiv1beta1.Job) *StatusCacheDetaisl {
	c.mu.Lock()
	defer c.mu.Unlock()
	key := client.ObjectKeyFromObject(obj)
	if v, ok := c.data[key]; ok {
		return v
	}
	c.data[key] = &StatusCacheDetaisl{Paused: obj.Spec.Paused}
	return c.data[key]
}

func (c *StatusCache) Remove(obj client.Object) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.data, client.ObjectKeyFromObject(obj))
}

type StatusCacheDetaisl struct {
	Paused        bool
	CurrentJobUID string
}
