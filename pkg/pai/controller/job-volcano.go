package controller

import (
	"context"
	"encoding/base64"
	"fmt"
	"path"
	"reflect"
	"strconv"
	"strings"
	"time"

	"golang.org/x/exp/maps"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	schedulingv1 "k8s.io/api/scheduling/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	batchv1alpha1 "volcano.sh/apis/pkg/apis/batch/v1alpha1"
	schedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
	cloudv1 "xiaoshiai.cn/rune/pkg/apis/cloud/v1"
	"xiaoshiai.cn/rune/pkg/apis/pai"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

var _ JobProvider = &VolcanoJobProvider{}

type VolcanoJobProvider struct {
	Client client.Client
	Images *ImagesOptions
}

func (r *JobReconciler) volcanoPodGroupPhaseChanges() predicate.Predicate {
	return &predicate.Funcs{
		UpdateFunc: func(ue event.UpdateEvent) bool {
			newPodGroup, ok := ue.ObjectNew.(*schedulingv1beta1.PodGroup)
			if !ok {
				return false
			}
			oldPodGroup, ok := ue.ObjectOld.(*schedulingv1beta1.PodGroup)
			if !ok {
				return false
			}
			if newPodGroup.Status.Phase == oldPodGroup.Status.Phase {
				return false
			}
			return true
		},
	}
}

func (r *JobReconciler) volcanoJobChanges() predicate.Predicate {
	return &predicate.Funcs{
		UpdateFunc: func(ue event.UpdateEvent) bool {
			newJob, ok := ue.ObjectNew.(*batchv1alpha1.Job)
			if !ok {
				return false
			}
			oldJob, ok := ue.ObjectOld.(*batchv1alpha1.Job)
			if !ok {
				return false
			}
			if newJob.Status.State.Phase != oldJob.Status.State.Phase {
				return true
			}
			if !reflect.DeepEqual(newJob.Status.TaskStatusCount, oldJob.Status.TaskStatusCount) {
				return true
			}
			return false
		},
	}
}

func (r *VolcanoJobProvider) statusFrom(ctx context.Context, status *JobProviderStatus, job *batchv1alpha1.Job) error {
	if err := r.statusBasicFromJob(ctx, status, job); err != nil {
		return err
	}
	// check podgroup status
	podgroup := &schedulingv1beta1.PodGroup{}
	if err := r.Client.Get(ctx,
		client.ObjectKey{Name: job.Name + "-" + string(job.UID), Namespace: job.Namespace}, podgroup); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		// podgroup not found, check job status
		return r.statusFromJob(ctx, status, job)
	}
	switch podgroup.Status.Phase {
	case schedulingv1beta1.PodGroupPending:
		status.Phase = paiv1beta1.JobPhasePending
		status.Message = ""
		// fill reason and message
		for _, cond := range podgroup.Status.Conditions {
			if cond.Status == v1.ConditionTrue && cond.Type == schedulingv1beta1.PodGroupUnschedulableType {
				status.Message = cond.Reason + ": " + cond.Message
				break
			}
		}
	case schedulingv1beta1.PodGroupInqueue:
		status.Phase = paiv1beta1.JobPhaseQueued
		status.Message = ""
	case schedulingv1beta1.PodGroupCompleted, schedulingv1beta1.PodGroupRunning:
		// pogroup is running, check job status
		return r.statusFromJob(ctx, status, job)
	}
	return nil
}

func (r *VolcanoJobProvider) statusBasicFromJob(ctx context.Context,
	status *JobProviderStatus, job *batchv1alpha1.Job,
) error {
	if status.Roles == nil {
		status.Roles = make(map[string]JobProviderRoleStatus)
	}
	// fill job role status's taskResources
	for _, task := range job.Spec.Tasks {
		roleResources := v1.ResourceRequirements{
			Limits:   v1.ResourceList{},
			Requests: v1.ResourceList{},
		}
		for _, c := range task.Template.Spec.Containers {
			for i := 0; i < int(task.Replicas); i++ {
				AddResourceList(roleResources.Requests, c.Resources.Requests)
				AddResourceList(roleResources.Limits, c.Resources.Limits)
			}
		}
		role := status.Roles[task.Name]
		role.Resources = roleResources
		role.DesiredReplicas = task.Replicas
		status.Roles[task.Name] = role
	}
	for name, count := range job.Status.TaskStatusCount {
		allreplicas := int32(0)
		for _, replicas := range count.Phase {
			allreplicas += replicas
		}
		role := status.Roles[name]
		role.CurrentReplicas = allreplicas
		role.RunnigReplicas = count.Phase[corev1.PodRunning]
		status.Roles[name] = role
	}
	if err := r.statusRunning(ctx, status, job); err != nil {
		return err
	}
	return nil
}

func (r *VolcanoJobProvider) statusRunning(
	_ context.Context, status *JobProviderStatus, job *batchv1alpha1.Job,
) error {
	// running from condition
	for _, cond := range job.Status.Conditions {
		if cond.Status == batchv1alpha1.Running {
			status.RunningTimestamp = cond.LastTransitionTime
		}
	}
	return nil
}

func (r *VolcanoJobProvider) statusFromJob(ctx context.Context,
	status *JobProviderStatus, job *batchv1alpha1.Job,
) error {
	switch job.Status.State.Phase {
	case batchv1alpha1.Failed, batchv1alpha1.Terminated, batchv1alpha1.Aborted:
		status.Phase = paiv1beta1.JobPhaseFailed
		status.FinishTimestamp = job.Status.State.LastTransitionTime.DeepCopy()
		return r.statusFromFailedPod(ctx, job, status)
	case batchv1alpha1.Completed:
		status.FinishTimestamp = job.Status.State.LastTransitionTime.DeepCopy()
		status.Phase = paiv1beta1.JobPhaseCompleted
	case batchv1alpha1.Running:
		status.Phase = paiv1beta1.JobPhaseRunning
		status.Message = ""
	case "":
		status.Phase = paiv1beta1.JobPhasePending
		status.Message = ""
	default:
		status.Phase = paiv1beta1.JobPhase(job.Status.State.Phase)
	}
	return nil
}

func (r *VolcanoJobProvider) statusFromFailedPod(ctx context.Context,
	job *batchv1alpha1.Job, status *JobProviderStatus,
) error {
	podlist := &v1.PodList{}
	_ = r.Client.List(ctx, podlist,
		client.InNamespace(job.Namespace),
		client.MatchingLabels{batchv1alpha1.JobNameKey: job.Name})
	reasons := make(map[string]int)
	for _, pod := range podlist.Items {
		if pod.Status.Phase != v1.PodFailed {
			continue
		}
		if pod.Status.Message != "" {
			reasons[pod.Status.Message]++
			continue
		}
		for _, container := range pod.Status.ContainerStatuses {
			if terminated := container.State.Terminated; terminated != nil {
				if terminated.Reason != "" {
					reasons[terminated.Reason]++
				}
			}
		}
	}
	if len(reasons) == 0 {
		return nil
	}
	mainReason := ""
	for reason, count := range reasons {
		if count > reasons[mainReason] {
			mainReason = reason
		}
	}
	status.Message = mainReason
	return nil
}

func (r *VolcanoJobProvider) SyncJob(ctx context.Context, job *paiv1beta1.Job) error {
	// every time we edit the job, we will create a new volcano job
	volcanoJob := &batchv1alpha1.Job{}
	if err := r.Client.Get(ctx, client.ObjectKeyFromObject(job), volcanoJob); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		// not found, create a new one
		if err := r.create(ctx, job); err != nil {
			return err
		}
	} else {
		// already exists, check if we need to update
		if err := r.syncOnRuntime(ctx, job, volcanoJob); err != nil {
			return err
		}
	}
	return nil
}

func (r *VolcanoJobProvider) create(ctx context.Context, job *paiv1beta1.Job) error {
	volcanoJob := &batchv1alpha1.Job{}
	if err := r.toVolcannoJob(ctx, volcanoJob, job); err != nil {
		return err
	}
	return client.IgnoreAlreadyExists(r.Client.Create(ctx, volcanoJob))
}

func (r *VolcanoJobProvider) syncOnRuntime(ctx context.Context, job *paiv1beta1.Job, vjob *batchv1alpha1.Job) error {
	original := vjob.DeepCopy()
	//  the field we need to update is only the replicas
	for _, role := range job.Spec.Roles {
		for i, task := range vjob.Spec.Tasks {
			if task.Name == role.Name {
				vjob.Spec.Tasks[i].Replicas = int32(role.Replicas)
			}
		}
	}
	if reflect.DeepEqual(original, vjob) {
		return nil
	}
	return r.Client.Patch(ctx, vjob, client.MergeFrom(original))
}

func (r *VolcanoJobProvider) CheckJob(ctx context.Context, job *paiv1beta1.Job) (string, *JobProviderStatus, error) {
	volcanoJob := &batchv1alpha1.Job{ObjectMeta: metav1.ObjectMeta{Name: job.Name, Namespace: job.Namespace}}
	if err := r.Client.Get(ctx, client.ObjectKeyFromObject(volcanoJob), volcanoJob); err != nil {
		if !apierrors.IsNotFound(err) {
			return "", nil, err
		}
		return "", &JobProviderStatus{Phase: paiv1beta1.JobPhaseUnknown}, nil
	}
	status := &JobProviderStatus{
		// recover from job status
		StartTimestamp: job.Status.State.StartTimestamp,
	}
	if err := r.statusFrom(ctx, status, volcanoJob); err != nil {
		return string(volcanoJob.UID), nil, err
	}
	return string(volcanoJob.UID), status, nil
}

func (r *VolcanoJobProvider) RemoveJob(ctx context.Context, job *paiv1beta1.Job) error {
	vjob := &batchv1alpha1.Job{ObjectMeta: metav1.ObjectMeta{Name: job.Name, Namespace: job.Namespace}}
	if err := client.IgnoreNotFound(r.Client.Delete(ctx, vjob)); err != nil {
		return err
	}
	return wait.PollImmediateUntil(time.Second, func() (bool, error) {
		if err := r.Client.Get(ctx, client.ObjectKeyFromObject(vjob), vjob); err != nil {
			if apierrors.IsNotFound(err) {
				return true, nil
			}
			return false, err
		}
		return false, nil
	}, ctx.Done())
}

// nolint: funlen
func (r *VolcanoJobProvider) toVolcannoJob(
	ctx context.Context, vjob *batchv1alpha1.Job, job *paiv1beta1.Job,
) error {
	// set owner reference
	vjob.Name, vjob.Namespace = job.Name, job.Namespace
	if err := controllerutil.SetControllerReference(job, vjob, r.Client.Scheme()); err != nil {
		return err
	}
	// labels and annotations
	pai.MergeLabelsAnnotations(vjob, job.Labels, job.Annotations)
	// add generation label
	if vjob.Labels == nil {
		vjob.Labels = make(map[string]string)
	}
	vjob.Labels[pai.LabelGeneration] = strconv.Itoa(int(job.Generation))
	// priority class
	priorityClassName, err := r.convertPriorityClass(ctx, job.Spec.PriorityName)
	if err != nil {
		return fmt.Errorf("invalid priority: %w", err)
	}
	vjob.Spec.PriorityClassName = priorityClassName // used on pod group only
	// queue
	queuename, err := r.convertQueue(ctx, job)
	if err != nil {
		return fmt.Errorf("invalid queue: %w", err)
	}
	vjob.Spec.Queue = queuename
	// tasks
	tasks := make([]batchv1alpha1.TaskSpec, 0, len(job.Spec.Roles))
	for _, role := range job.Spec.Roles {
		task := batchv1alpha1.TaskSpec{Name: role.Name}
		if role.PodSpec != nil {
			task.Template.Spec = *role.PodSpec
		}
		// apply priority class on pod spec
		if task.Template.Spec.PriorityClassName == "" {
			task.Template.Spec.PriorityClassName = vjob.Spec.PriorityClassName
		}
		if err := r.applyBasic(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid basic: %w", role.Name, err)
		}
		if err := r.applyPorts(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid ports: %w", role.Name, err)
		}
		if err := r.applyMounts(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid mounts: %w", role.Name, err)
		}
		if err := r.applyConfigs(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid configs: %w", role.Name, err)
		}
		if err := r.applyShm(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid shm: %w", role.Name, err)
		}
		if err := r.applySku(ctx, job, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid sku: %w", role.Name, err)
		}
		// if err := r.applyDistributionInfo(ctx, job, &role, &task); err != nil {
		// 	return fmt.Errorf("job role [%s] invalid distribution info: %w", role.Name, err)
		// }
		if err := r.applyPrivileged(ctx, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid privileged: %w", role.Name, err)
		}
		if err := r.applyLifecycle(ctx, &role, &task); err != nil {
			return fmt.Errorf("job role [%s] invalid lifecycle: %w", role.Name, err)
		}
		tasks = append(tasks, task)
	}
	vjob.Spec.Tasks = tasks
	if err := r.applyTTL(ctx, vjob, job); err != nil {
		return fmt.Errorf("invalid ttl: %w", err)
	}
	// plugins
	if err := r.applyPlugins(ctx, vjob, job); err != nil {
		return fmt.Errorf("invalid plugins: %w", err)
	}
	return nil
}

func (r *VolcanoJobProvider) applyPlugins(_ context.Context, vjob *batchv1alpha1.Job, job *paiv1beta1.Job) error {
	if vjob.Spec.Plugins == nil {
		vjob.Spec.Plugins = make(map[string][]string)
	}
	// svc
	// if job.Spec.DistributionSpec != nil && job.Spec.DistributionSpec.Enabled {
	// 	vjob.Spec.Plugins["svc"] = []string{
	// 		"--disable-network-policy",
	// 		"--publish-not-ready-addresses",
	// 	}
	// } else {
	// 	vjob.Spec.Plugins["svc"] = []string{
	// 		"--disable-network-policy",
	// 	}
	// }
	total_replicas := 0
	for _, role := range job.Spec.Roles {
		total_replicas += int(role.Replicas)
	}
	// multi-replica job
	if total_replicas > 1 || len(vjob.Spec.Tasks) > 1 {
		vjob.Spec.Plugins["ssh"] = []string{
			// "--ssh-private-key=", // x509 encode private key string
			// "--ssh-public-key=",  // x509 encode public key string
		}
	}
	// multi-role job
	if len(vjob.Spec.Tasks) > 1 {
		// enable ssh and mpi plugin on multi-role job
		vjob.Spec.Plugins["mpi"] = []string{
			"--master", vjob.Spec.Tasks[0].Name,
			"--worker", vjob.Spec.Tasks[1].Name,
		}
	}
	return nil
}

// func (r *VolcanoJobProvider) getStorageSet(
// 	ctx context.Context, name string, ns string,
// ) (*paiv1beta1.StorageSet, error) {
// 	ss := &paiv1beta1.StorageSet{}
// 	if err := r.Client.Get(ctx, client.ObjectKey{Name: name, Namespace: ns}, ss); err != nil {
// 		return nil, err
// 	}
// 	return ss, nil
// }

func (r *VolcanoJobProvider) applyBasic(_ context.Context,
	job *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	rolelabels := map[string]string{}
	maps.Copy(rolelabels, job.Labels)
	maps.Copy(rolelabels, role.Labels)
	maps.Copy(rolelabels, JobRolePodLabel(job, *role))

	roleannotations := map[string]string{}
	maps.Copy(roleannotations, job.Annotations)
	maps.Copy(roleannotations, role.Annotations)

	task.Template.Labels = pai.MergeMap(task.Template.Labels, rolelabels)
	task.Template.Annotations = pai.MergeMap(task.Template.Annotations, roleannotations)

	task.Replicas = int32(role.Replicas)
	task.Template.Spec.ImagePullSecrets = append(task.Template.Spec.ImagePullSecrets, role.ImagePullSecrets...)
	if policy := role.RestartPolicy; policy != "" {
		task.Template.Spec.RestartPolicy = policy
	} else {
		task.Template.Spec.RestartPolicy = v1.RestartPolicyNever
	}
	c := getOrCreateContainer(task, role.Name)
	c.Resources = role.Resources
	if role.Image != "" {
		c.Image = role.Image
	}
	if role.ImagePullPolicy != "" {
		c.ImagePullPolicy = role.ImagePullPolicy
	}
	if role.Command != nil {
		c.Command = role.Command
	}
	if role.Args != nil {
		c.Args = role.Args
	}
	if role.Env != nil {
		c.Env = role.Env
	}
	if role.WorkingDir != "" {
		c.WorkingDir = role.WorkingDir
	}
	if role.HostNetwork {
		task.Template.Spec.HostNetwork = role.HostNetwork
	}
	return nil
}

func (r *VolcanoJobProvider) applyPorts(_ context.Context,
	_ *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	ports := make([]v1.ContainerPort, 0, len(role.Ports))
	for _, port := range role.Ports {
		ports = append(ports, v1.ContainerPort{
			Name:          port.Name,
			ContainerPort: port.Port,
		})
	}
	getOrCreateContainer(task, role.Name).Ports = ports
	return nil
}

func (r *VolcanoJobProvider) applyShm(_ context.Context, _ *paiv1beta1.Job,
	role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	if role.SHMSize.IsZero() {
		return nil
	}
	container := getOrCreateContainer(task, role.Name)
	container.VolumeMounts = append(container.VolumeMounts, v1.VolumeMount{
		Name:      "dshm",
		MountPath: "/dev/shm",
	})
	task.Template.Spec.Volumes = append(task.Template.Spec.Volumes, v1.Volume{
		Name: "dshm",
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{
				Medium:    v1.StorageMediumMemory,
				SizeLimit: &role.SHMSize,
			},
		},
	})
	return nil
}

func (r *VolcanoJobProvider) applyMounts(ctx context.Context,
	job *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	c := getOrCreateContainer(task, role.Name)
	volumes, volumeMounts := []v1.Volume{}, []v1.VolumeMount{}
	for _, vol := range role.Mounts {
		// ss, err := r.getStorageSet(ctx, vol.Name, job.Namespace)
		// if err != nil {
		// 	return err
		// }
		// volcano rasise :
		// Invalid value: "modelset-default-qwen-qwen1.5-0.5b":
		// a lowercase RFC 1123 label must consist of lower case alphanumeric characters or '-',
		// and must start and end with an alphanumeric character
		// (e.g. 'my-name', or '123-abc', regex used for validation is '[a-z0-9]([-a-z0-9]*[a-z0-9])?')
		simplestMountName := EncodeToK8sNameNoDot(vol.Name)
		pvcname := vol.Name
		// if pvcname == "" {
		// return fmt.Errorf("storage set %s has no pvc yet", ss.Name)
		// }
		// readonly from storage set or volume mount
		readonly := vol.Readonly
		volumes = append(volumes, v1.Volume{Name: simplestMountName, VolumeSource: v1.VolumeSource{
			PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{ClaimName: pvcname, ReadOnly: readonly},
		}})
		volumeMounts = append(volumeMounts, v1.VolumeMount{
			Name:      simplestMountName,
			MountPath: vol.Path,
			ReadOnly:  readonly,
			// https://juicefs.com/docs/csi/guide/pv/#automatic-mount-point-recovery
			// set mount propagation to HostToContainer to support automatic mount point recovery from host
			MountPropagation: func() *v1.MountPropagationMode {
				mode := v1.MountPropagationHostToContainer
				return &mode
			}(),
		})
	}
	// set volumes and volumeMounts
	task.Template.Spec.Volumes = append(task.Template.Spec.Volumes, volumes...)
	c.VolumeMounts = append(c.VolumeMounts, volumeMounts...)
	return nil
}

func (r *VolcanoJobProvider) applyConfigs(ctx context.Context,
	job *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	c := getOrCreateContainer(task, role.Name)
	cwd := role.WorkingDir
	if cwd == "" {
		cwd = "/config"
	}
	configMapFiles := map[string]map[string]string{}
	for _, configfile := range role.Configs {
		if configfile.Value == "" || configfile.Path == "" {
			continue
		}
		fullpath := configfile.Path
		if !path.IsAbs(configfile.Path) {
			fullpath = path.Clean(path.Join(cwd, configfile.Path))
		}
		dir, filename := path.Split(fullpath)
		dir = path.Clean(dir)
		if dir == "" || dir == "." {
			return fmt.Errorf("invalid config file mount path %s", fullpath)
		}
		if data, ok := configMapFiles[dir]; ok {
			data[filename] = configfile.Value
			configMapFiles[dir] = data
		} else {
			configMapFiles[dir] = map[string]string{filename: configfile.Value}
		}
	}

	for mountpath, data := range configMapFiles {
		mountpathb64 := strings.ToLower(base64.StdEncoding.EncodeToString([]byte(mountpath))[:4])
		cm := &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{
			Name:      EncodeToK8sName(fmt.Sprintf("%s-%s-%s", job.Name, role.Name, mountpathb64)),
			Namespace: job.Namespace,
		}}
		if _, err := controllerutil.CreateOrUpdate(ctx, r.Client, cm, func() error {
			controllerutil.SetControllerReference(job, cm, r.Client.Scheme())
			cm.Data = data
			return nil
		}); err != nil {
			return err
		}
		mountName := fmt.Sprintf("config-%s", mountpathb64)
		for filename := range data {
			c.VolumeMounts = append(c.VolumeMounts, v1.VolumeMount{
				Name:      mountName,
				MountPath: path.Join(mountpath, filename),
				SubPath:   filename,
			})
		}
		task.Template.Spec.Volumes = append(task.Template.Spec.Volumes, v1.Volume{
			Name: mountName,
			VolumeSource: v1.VolumeSource{
				ConfigMap: &v1.ConfigMapVolumeSource{
					LocalObjectReference: v1.LocalObjectReference{Name: cm.Name},
				},
			},
		})
	}
	return nil
}

// CommonPrefix returns the longest common prefix of a and b.
func CommonPrefix(a, b string) string {
	for i := 0; i < len(a) && i < len(b); i++ {
		if a[i] != b[i] {
			return a[:i]
		}
	}
	return a
}

func getOrCreateContainer(task *batchv1alpha1.TaskSpec, name string) *v1.Container {
	for i, c := range task.Template.Spec.Containers {
		if c.Name == name {
			return &task.Template.Spec.Containers[i]
		}
	}
	task.Template.Spec.Containers = append(task.Template.Spec.Containers, v1.Container{Name: name})
	return &task.Template.Spec.Containers[len(task.Template.Spec.Containers)-1]
}

func getOrCreateInitContainer(task *batchv1alpha1.TaskSpec, name string) *v1.Container {
	for i := range task.Template.Spec.InitContainers {
		if task.Template.Spec.InitContainers[i].Name == name {
			return &task.Template.Spec.InitContainers[i]
		}
	}
	task.Template.Spec.InitContainers = append(task.Template.Spec.InitContainers, v1.Container{Name: name})
	return &task.Template.Spec.InitContainers[len(task.Template.Spec.InitContainers)-1]
}

func (r *VolcanoJobProvider) convertPriorityClass(ctx context.Context, name string) (string, error) {
	if name == "" {
		return "", nil
	}
	prio := &schedulingv1.PriorityClass{ObjectMeta: metav1.ObjectMeta{Name: name}}
	if err := r.Client.Get(ctx, client.ObjectKeyFromObject(prio), prio); err != nil {
		return "", err
	}
	return prio.Name, nil
}

func (r *VolcanoJobProvider) applySku(ctx context.Context,
	job *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	if role.ResourceFlavor == "" {
		return nil
	}
	flavor := &cloudv1.ResourceFlavor{}
	if err := r.Client.Get(ctx, client.ObjectKey{Name: role.ResourceFlavor}, flavor); err != nil {
		return err
	}
	container := getOrCreateContainer(task, role.Name)
	for _, resource := range flavor.Spec.Resources {
		if container.Resources.Requests == nil {
			container.Resources.Requests = v1.ResourceList{}
		}
		if container.Resources.Limits == nil {
			container.Resources.Limits = v1.ResourceList{}
		}
		container.Resources.Limits[resource.Name] = resource.Limit
		container.Resources.Requests[resource.Name] = resource.Request

		task.Template.Spec.NodeSelector = pai.MergeMap(task.Template.Spec.NodeSelector, resource.NodeSelector)
	}
	return nil
}

const GPUResourceName = corev1.ResourceName("nvidia.com/gpu")

// func (r *VolcanoJobProvider) applyDistributionInfo(_ context.Context,
// 	job *paiv1beta1.Job, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
// ) error {
// 	if job.Spec.DistributionSpec == nil {
// 		return nil
// 	}
// 	if !job.Spec.DistributionSpec.Enabled {
// 		return nil
// 	}

// 	vl := v1.Volume{Name: "pai-distribution", VolumeSource: v1.VolumeSource{
// 		EmptyDir: &v1.EmptyDirVolumeSource{},
// 	}}
// 	pai.InjectArray[v1.Volume](&task.Template.Spec.Volumes, vl, func(v v1.Volume) string {
// 		return v.Name
// 	})

// 	vm := v1.VolumeMount{Name: "pai-distribution", MountPath: "/distribution"}
// 	container := getOrCreateContainer(task, role.Name)
// 	initContainer := getOrCreateInitContainer(task, "pai-distribution")

// 	initContainer.Image = job.Spec.DistributionSpec.Image
// 	if initContainer.Image == "" {
// 		initContainer.Image = r.Images.DistributionInitializer
// 	}
// 	roleGPUNum, exist := container.Resources.Limits[GPUResourceName]
// 	if exist {
// 		initContainer.Env = append(initContainer.Env, v1.EnvVar{
// 			Name:  "GPU_NUM",
// 			Value: roleGPUNum.String(),
// 		})
// 	}

// 	pai.MergeArray[v1.EnvVar](&initContainer.Env, &job.Spec.DistributionSpec.Env, func(v v1.EnvVar) string {
// 		return v.Name
// 	})

// 	pai.InjectArray[v1.VolumeMount](&container.VolumeMounts, vm, func(v v1.VolumeMount) string {
// 		return v.Name
// 	})

// 	pai.InjectArray[v1.VolumeMount](&initContainer.VolumeMounts, vm, func(v v1.VolumeMount) string {
// 		return v.Name
// 	})
// 	return nil
// }

func (r *VolcanoJobProvider) applyPrivileged(
	_ context.Context, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	if role.Privileged {
		for i := range task.Template.Spec.Containers {
			if task.Template.Spec.Containers[i].SecurityContext == nil {
				task.Template.Spec.Containers[i].SecurityContext = &v1.SecurityContext{}
			}
			task.Template.Spec.Containers[i].SecurityContext.Privileged = ptr.To(true)
		}
	}
	return nil
}

func (r *VolcanoJobProvider) applyLifecycle(
	_ context.Context, role *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
) error {
	if role.Lifecycle != nil {
		for i := range task.Template.Spec.Containers {
			task.Template.Spec.Containers[i].Lifecycle = role.Lifecycle
		}
	}
	return nil
}

func (r *VolcanoJobProvider) applyTTL(_ context.Context, vjob *batchv1alpha1.Job, job *paiv1beta1.Job) error {
	if job.Spec.TTL == nil || job.Spec.TTL.Duration == 0 {
		return nil
	}
	ttl := ptr.To(int64(job.Spec.TTL.Duration.Seconds()))
	for i := range vjob.Spec.Tasks {
		vjob.Spec.Tasks[i].Template.Spec.ActiveDeadlineSeconds = ttl
	}
	return nil
}

func (r *VolcanoJobProvider) convertQueue(ctx context.Context, job *paiv1beta1.Job) (string, error) {
	queuename := job.Spec.QueueName
	if queuename == "" {
		return "default", nil
	}
	queue := &schedulingv1beta1.Queue{ObjectMeta: metav1.ObjectMeta{Name: queuename, Namespace: job.Namespace}}
	if err := r.Client.Get(ctx, client.ObjectKeyFromObject(queue), queue); err != nil {
		return "", err
	}
	return queue.Name, nil
}

const GitInitCloneDir = "/workspace"

// func (r *VolcanoJobProvider) injectGitInitContainer(_ context.Context,
// 	_ *paiv1beta1.Job, _ *paiv1beta1.JobRole, task *batchv1alpha1.TaskSpec,
// 	config paiv1beta1.StorageProviderConfig, intovolname string,
// ) error {
// 	initcontainer := getOrCreateInitContainer(task, "git-"+intovolname)
// 	initcontainer.WorkingDir = GitInitCloneDir
// 	initcontainer.VolumeMounts = append(initcontainer.VolumeMounts, v1.VolumeMount{
// 		Name: intovolname, MountPath: GitInitCloneDir,
// 	})
// 	return completedGitContainer(initcontainer, config, r.Images.Git)
// }
