package controller

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	paiv1beta1 "xiaoshiai.cn/rune/pkg/apis/pai/v1"
)

// type StorageProvider interface {
// 	SyncTenant(ctx context.Context, tenant *paiv1beta1.Tenant) error
// 	RemoveTenant(ctx context.Context, tenant *paiv1beta1.Tenant) error

// 	RemoveStorageSet(ctx context.Context, ss *paiv1beta1.StorageSet) error
// 	SyncStorageSet(ctx context.Context, ss *paiv1beta1.StorageSet) error
// 	SyncS3Info(ctx context.Context, ss *paiv1beta1.StorageSet) error
// }

type NetworkProviderStatus struct {
	Message   string
	RolePorts map[string][]paiv1beta1.JobRolPortStatus
}

type NetworkProvider interface {
	SyncJobNetwork(ctx context.Context, job *paiv1beta1.Job) (*NetworkProviderStatus, error)
	RemoveJobNetwork(ctx context.Context, job *paiv1beta1.Job) error
}

type JobProviderStatus struct {
	FinishTimestamp  *metav1.Time
	RunningTimestamp *metav1.Time
	StartTimestamp   *metav1.Time
	Phase            paiv1beta1.JobPhase
	Message          string
	Roles            map[string]JobProviderRoleStatus
}

type JobProviderRoleStatus struct {
	Resources       corev1.ResourceRequirements
	DesiredReplicas int32 // desired replicas
	CurrentReplicas int32 // current replicas
	RunnigReplicas  int32 // running replicas
}

type JobProvider interface {
	// ExistsJob returns true if the job uuid if exists
	CheckJob(ctx context.Context, job *paiv1beta1.Job) (string, *JobProviderStatus, error)
	SyncJob(ctx context.Context, job *paiv1beta1.Job) error
	RemoveJob(ctx context.Context, job *paiv1beta1.Job) error
}
