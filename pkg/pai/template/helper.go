package template

import (
	"context"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/Masterminds/sprig/v3"
	"sigs.k8s.io/yaml"
)

func GoTemplate(_ context.Context, tmpl string, data any) (string, error) {
	t := template.New("gotpl")
	t.Funcs(goTemplateFuncMap())
	if _, err := t.Parse(tmpl); err != nil {
		return "", err
	}
	var buf strings.Builder
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func goTemplateFuncMap() template.FuncMap {
	f := sprig.TxtFuncMap()
	delete(f, "env")
	delete(f, "expandenv")
	f["toYaml"] = func(v any) string {
		data, err := yaml.Marshal(v)
		if err != nil {
			return err.Error()
		}
		return strings.TrimSuffix(string(data), "\n")
	}
	f["fromYaml"] = func(str string) map[string]any {
		m := map[string]any{}
		if err := yaml.Unmarshal([]byte(str), &m); err != nil {
			m["Error"] = err.Error()
		}
		return m
	}
	f["toJson"] = func(v any) string {
		data, err := json.Marshal(v)
		if err != nil {
			return err.Error()
		}
		return string(data)
	}
	f["fromJson"] = func(str string) map[string]any {
		m := map[string]any{}
		if err := json.Unmarshal([]byte(str), &m); err != nil {
			m["Error"] = err.Error()
		}
		return m
	}
	return f
}

func DeepCopyMap(m map[string]any) map[string]any {
	if m == nil {
		return nil
	}
	ret := map[string]any{}
	for k, v := range m {
		switch v := v.(type) {
		case map[string]any:
			ret[k] = DeepCopyMap(v)
		default:
			ret[k] = v
		}
	}
	return ret
}
