package template

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/openapi"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type JobTemplate struct {
	store.ObjectMeta `json:",inline"`
	Kind             string         `json:"kind"`
	Schema           openapi.Schema `json:"schema"`
	Template         string         `json:"template"`
}

func (a *API) ListJobTemplate(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		list := store.List[JobTemplate]{}
		return base.GenericList(r, a.Store, &list)
	})
}

func (a *API) GetJobTemplate(w http.ResponseWriter, r *http.Request) {
	a.onJobTemplate(w, r, func(ctx context.Context, jobtemplate string) (any, error) {
		return base.GenericGet(r, a.Store, &JobTemplate{}, jobtemplate)
	})
}

func (a *API) CreateJobTemplate(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		jobtemplate := &JobTemplate{}
		if err := api.Body(r, jobtemplate); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateJobTemplate(jobtemplate); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, a.Store, jobtemplate)
	})
}

func validateJobTemplate(jobtemplate *JobTemplate) error {
	if jobtemplate.Name == "" {
		return errors.NewBadRequest("jobtemplate name is required")
	}
	if jobtemplate.Template == "" {
		return errors.NewBadRequest("jobtemplate template is required")
	}
	if len(jobtemplate.Schema.Properties) == 0 {
		return errors.NewBadRequest("jobtemplate schema is required")
	}
	return nil
}

func (a *API) UpdateJobTemplate(w http.ResponseWriter, r *http.Request) {
	a.onJobTemplate(w, r, func(ctx context.Context, jobtemplate string) (any, error) {
		jobtemplateObj := &JobTemplate{}
		if err := api.Body(r, jobtemplateObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateJobTemplate(jobtemplateObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, a.Store, jobtemplateObj, jobtemplate)
	})
}

func (a *API) DeleteJobTemplate(w http.ResponseWriter, r *http.Request) {
	a.onJobTemplate(w, r, func(ctx context.Context, jobtemplate string) (any, error) {
		return base.GenericDelete(r, a.Store, &JobTemplate{}, jobtemplate)
	})
}

func (a *API) onJobTemplate(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, jobtemplate string) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		jobtemplate := api.Path(r, "jobtemplate", "")
		if jobtemplate == "" {
			return nil, errors.NewBadRequest("jobtemplate name is required")
		}
		return fn(ctx, jobtemplate)
	})
}

func (a *API) JobTemplateGroup() api.Group {
	return api.
		NewGroup("/jobtemplates").
		Tag("JobTemplate").
		Route(
			api.GET("").
				Operation("list job templates").
				Param(api.PageParams...).
				To(a.ListJobTemplate).
				Response(store.List[JobTemplate]{}),

			api.GET("/{jobtemplate}").
				Operation("get job template").
				To(a.GetJobTemplate).
				Response(JobTemplate{}),

			api.POST("").
				Operation("create job template").
				To(a.CreateJobTemplate).
				Param(api.BodyParam("jobtemplate", JobTemplate{})).
				Response(JobTemplate{}),

			api.PUT("/{jobtemplate}").
				Operation("update job template").
				To(a.UpdateJobTemplate).
				Param(api.BodyParam("jobtemplate", JobTemplate{})).
				Response(JobTemplate{}),

			api.DELETE("/{jobtemplate}").
				Operation("delete job template").
				To(a.DeleteJobTemplate),
		)
}
