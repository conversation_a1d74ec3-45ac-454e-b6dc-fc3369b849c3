package template

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type StorageSetTemplate struct {
	store.ObjectMeta `json:",inline"`
	//Kind             storage.StorageSetKind `json:"kind"`
	//Downloaders []storage.Downloader `json:"downloaders"`
	Config map[string]string `json:"config"`
	Public bool              `json:"public"`
}

func (a *API) ListStorageSetTemplate(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		list := store.List[StorageSetTemplate]{}
		return base.GenericList(r, a.Store, &list)
	})
}

func (a *API) GetStorageSetTemplate(w http.ResponseWriter, r *http.Request) {
	a.onStorageSetTemplate(w, r, func(ctx context.Context, storagesettemplate string) (any, error) {
		return base.GenericGet(r, a.Store, &StorageSetTemplate{}, storagesettemplate)
	})
}

func (a *API) CreateStorageSetTemplate(w http.ResponseWriter, r *http.Request) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		storagesettemplate := &StorageSetTemplate{}
		if err := api.Body(r, storagesettemplate); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageSetTemplate(storagesettemplate); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, a.Store, storagesettemplate)
	})
}

func validateStorageSetTemplate(storagesettemplate *StorageSetTemplate) error {
	if storagesettemplate.Name == "" {
		return errors.NewBadRequest("storagesettemplate name is required")
	}
	return nil
}

func (a *API) UpdateStorageSetTemplate(w http.ResponseWriter, r *http.Request) {
	a.onStorageSetTemplate(w, r, func(ctx context.Context, storagesettemplate string) (any, error) {
		storageset := &StorageSetTemplate{}
		if err := api.Body(r, storageset); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageSetTemplate(storageset); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, a.Store, storageset, storagesettemplate)
	})
}

func (a *API) DeleteStorageSetTemplate(w http.ResponseWriter, r *http.Request) {
	a.onStorageSetTemplate(w, r, func(ctx context.Context, storagesettemplate string) (any, error) {
		return base.GenericDelete(r, a.Store, &StorageSetTemplate{}, storagesettemplate)
	})
}

func (a *API) onStorageSetTemplate(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storagesettemplate string) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		storagesettemplate := api.Path(r, "storagesettemplate", "")
		if storagesettemplate == "" {
			return nil, errors.NewBadRequest("storagesettemplate name is required")
		}
		return fn(ctx, storagesettemplate)
	})
}

func (a *API) StorageSetTemplateGroup() api.Group {
	return api.
		NewGroup("/storagesettemplates").
		Tag("StorageSetTemplate").
		Route(
			api.GET("").
				Operation("list storage set templates").
				Param(api.PageParams...).
				To(a.ListStorageSetTemplate).
				Response(store.List[StorageSetTemplate]{}),

			api.GET("/{storagesettemplate}").
				Operation("get storage set template").
				To(a.GetStorageSetTemplate).
				Response(StorageSetTemplate{}),

			api.POST("").
				Operation("create storage set template").
				To(a.CreateStorageSetTemplate).
				Param(api.BodyParam("storagesettemplate", StorageSetTemplate{})).
				Response(StorageSetTemplate{}),

			api.PUT("/{storagesettemplate}").
				Operation("update storage set template").
				To(a.UpdateStorageSetTemplate).
				Param(api.BodyParam("storagesettemplate", StorageSetTemplate{})).
				Response(StorageSetTemplate{}),

			api.DELETE("/{storagesettemplate}").
				Operation("delete storage set template").
				To(a.DeleteStorageSetTemplate),
		)
}
