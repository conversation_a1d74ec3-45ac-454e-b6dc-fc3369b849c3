package pai

import (
	"context"
	"time"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/rune/pkg/cloud/client"
)

type ControllerOptions struct {
	Listen                string                                 `json:"listen" description:"Listen address for the OIDC CAS Shim service" default:":8080"`
	LeaderElection        bool                                   `json:"leaderElection,omitempty" description:"Enable leader election for the controller manager"`
	LeaderElectionTimeout time.Duration                          `json:"leaderElectionTimeout,omitempty" description:"Timeout for leader election, default is 15s"`
	EnabledControllers    []string                               `json:"enabledControllers,omitempty"`
	API                   *APIOptions                            `json:"api,omitempty" description:"API options for the server"`
	Config                *config.DynamicConfigOptions           `json:"config,omitempty" description:"Dynamic configuration options for the server"`
	Etcd                  *etcdcache.Options                     `json:"etcd,omitempty"`
	Mongodb               *mongo.MongoDBOptions                  `json:"mongodb,omitempty"`
	RequestHeader         *api.RequestHeaderAuthenticatorOptions `json:"requestHeader,omitempty"`
	Cloud                 *client.CloudServiceOptions            `json:"cloud,omitempty" description:"Cloud service options"`
}

func NewDefaultControllerOptions() *ControllerOptions {
	etcd := etcdcache.NewDefaultOptions()
	etcd.KeyPrefix = "/xpai"
	return &ControllerOptions{
		Listen:                ":8080",
		Config:                config.NewDefaultDynamicConfigOptions("xpai"),
		API:                   &APIOptions{Prefix: "/v1"},
		Etcd:                  etcd,
		LeaderElectionTimeout: 30 * time.Second,
		Mongodb:               mongo.NewDefaultMongoOptions("xpai"),
		RequestHeader:         api.NewDefaultRequestHeaderAuthenticatorOptions(),
		Cloud:                 client.NewDefaultCloudServiceOptions(),
	}
}

func RunController(ctx context.Context, options *ControllerOptions) error {
	deps, err := BuildControllerDependencies(ctx, options)
	if err != nil {
		return err
	}
	manager, err := NewControllerManager(ctx, deps)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return RunControllerAPI(ctx, deps)
	})
	eg.Go(func() error {
		return manager.Run(ctx)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type ControllerDependencies struct {
	Options     *ControllerOptions
	Storage     store.Store
	Config      config.DynamicConfig
	Authn       api.Authenticator
	CloudClient client.CloudClient
	//StorageClusterInfo storage.StorageClusterProviderHolder
}

func BuildControllerDependencies(ctx context.Context, options *ControllerOptions) (*ControllerDependencies, error) {
	etcdstore, err := etcdcache.NewEtcdCacher(options.Etcd, etcdcache.ResourceFieldsMap{})
	if err != nil {
		return nil, err
	}
	authn := api.NewRequestHeaderAuthenticator(options.RequestHeader)

	cloudclient, err := client.NewRemoteClient(ctx, options.Cloud)
	if err != nil {
		return nil, err
	}

	//providerfactory := delegate.NewDelegateProviderFactory()
	//info := storage.NewStorageClusterManager(providerfactory)

	return &ControllerDependencies{
		Options: options,
		Storage: etcdstore,
		Authn:   authn,
		//StorageClusterInfo: info,
		CloudClient: cloudclient,
	}, nil
}

func RunControllerAPI(ctx context.Context, deps *ControllerDependencies) error {
	return api.New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			api.NewGroup(deps.Options.API.Prefix).
				Filter(
					api.NewAuthenticateFilter(deps.Authn, nil),
				).
				SubGroup(),
		).
		Serve(ctx, deps.Options.Listen)
}

type ControllerManager struct {
	manager *controller.ControllerManager
	svc     *ControllerDependencies
}

func NewControllerManager(ctx context.Context, svc *ControllerDependencies) (*ControllerManager, error) {
	options, storage := svc.Options, svc.Storage
	manager := controller.NewControllerManager()
	if options.LeaderElection {
		manager.WithStoreLeaderElection(storage, "rune-pai-controller", options.LeaderElectionTimeout)
	}
	if len(options.EnabledControllers) > 0 {
		manager.EnableController(options.EnabledControllers...)
	}
	cm := &ControllerManager{
		manager: manager,
		svc:     svc,
	}
	return cm, nil
}

func (c *ControllerManager) Run(ctx context.Context) error {
	return c.manager.Run(ctx)
}
