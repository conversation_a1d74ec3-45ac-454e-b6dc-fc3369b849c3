package base

import (
	"context"
	stderrors "errors"
	"net/http"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

func OnSystemOrTenantOrTenantOrganizationRequirements(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, reqs store.Requirements) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		reqs := store.Requirements{}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			reqs = append(reqs, store.RequirementEqual("tenant", tenant))
		}
		if org := api.Path(r, "organization", ""); org != "" {
			reqs = append(reqs, store.RequirementEqual("organization", org))
		}
		return fn(ctx, reqs)
	})
}

func OnSystemOrTenantOrTenantOrganization(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, opttenant, optorg string) (any, error),
) {
	On(w, r, func(ctx context.Context) (any, error) {
		return fn(ctx, api.Path(r, "tenant", ""), api.Path(r, "organization", ""))
	})
}

func OnCurrentUser(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, user string) (any, error)) {
	On(w, r, func(ctx context.Context) (any, error) {
		userinfo := api.AuthenticateFromContext(ctx)
		if userinfo.User.Name == "" {
			return nil, errors.NewBadRequest("require login")
		}
		return fn(ctx, userinfo.User.Name)
	})
}

func OnInstance(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, workspace, instance string) (any, error)) {
	OnWorkspace(w, r, func(ctx context.Context, tenant, workspace string) (any, error) {
		instance := api.Path(r, "instance", "")
		if instance == "" {
			return nil, errors.NewBadRequest("instance name is required")
		}
		return fn(ctx, tenant, workspace, instance)
	})
}

func OnWorkspace(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, workspace string) (any, error)) {
	OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		workspace := api.Path(r, "workspace", "")
		if workspace == "" {
			return nil, errors.NewBadRequest("workspace name is required")
		}
		return fn(ctx, tenant, workspace)
	})
}

func OnTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant string) (any, error)) {
	On(w, r, func(ctx context.Context) (any, error) {
		tenant := api.Path(r, "tenant", "")
		if tenant == "" {
			return "", errors.NewBadRequest("tenant name is required")
		}
		return fn(ctx, tenant)
	})
}

func OnCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, cluster string) (any, error)) {
	On(w, r, func(ctx context.Context) (any, error) {
		cluster := api.Path(r, "cluster", "")
		if cluster == "" {
			return "", errors.NewBadRequest("cluster name is required")
		}
		return fn(ctx, cluster)
	})
}

func On(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		val, err := fn(ctx)
		err = ConvertError(err)
		return val, err
	})
}

func ConvertError(err error) error {
	if err == nil {
		return nil
	}
	// convert k8s error to common error
	if status, ok := err.(apierrors.APIStatus); ok || stderrors.As(err, &status) {
		metastatus := status.Status()
		return &errors.Status{
			Status:  metastatus.Status,
			Code:    metastatus.Code,
			Message: metastatus.Message,
			Reason:  errors.StatusReason(metastatus.Reason),
		}
	}
	return err
}

func NewCurrentGroup() api.Group {
	return api.NewGroup("/current")
}

func NewProductGroup() api.Group {
	return api.NewGroup("/products/{product}")
}

func NewClusterGroup() api.Group {
	return api.NewGroup("/clusters/{cluster}")
}

func NewTenantGroup(path string) api.Group {
	return api.NewGroup("/tenants/{tenant}" + path)
}

func NewTenantOrClusterGroup() api.Group {
	return api.NewGroup("/tenants/{tenant}/clusters/{cluster}")
}

func NewTenantOrganizationGroup(path string) api.Group {
	return api.NewGroup("/tenants/{tenant}/organizations/{organization}" + path)
}

func NewInstanceGroup() api.Group {
	return api.NewGroup("/tenants/{tenant}/workspaces/{workspace}/instances/{instance}")
}

func NewWorkspaceGroup() api.Group {
	return api.NewGroup("/tenants/{tenant}/workspaces/{workspace}")
}

func NewWorkspaceStorageVolumeGroup() api.Group {
	return api.NewGroup("/tenants/{tenant}/workspaces/{workspace}/storagevolumes/{storagevolume}")
}

func ScopeTenant(tenant string) store.Scope {
	return store.Scope{Resource: "tenants", Name: tenant}
}

func ScopeOrganization(organization string) store.Scope {
	return store.Scope{Resource: "organizations", Name: organization}
}

func ScopeCluster(cluster string) store.Scope {
	return store.Scope{Resource: "clusters", Name: cluster}
}

func ScopeInstance(instance string) store.Scope {
	return store.Scope{Resource: "instances", Name: instance}
}

func ScopeWorkspace(workspace string) store.Scope {
	return store.Scope{Resource: "workspaces", Name: workspace}
}

func TenantOrganizationFromScopes(scopes ...store.Scope) (string, string) {
	org, tenant := "", ""
	for _, scope := range scopes {
		switch scope.Resource {
		case "organizations":
			org = scope.Name
		case "tenants":
			tenant = scope.Name
		}
	}
	return tenant, org
}

func TenantFromScopes(scopes ...store.Scope) string {
	for _, scope := range scopes {
		if scope.Resource == "tenants" {
			return scope.Name
		}
	}
	return ""
}
