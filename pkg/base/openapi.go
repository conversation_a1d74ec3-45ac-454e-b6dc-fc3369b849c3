package base

import "github.com/go-openapi/spec"

func UserGroupSecurityFunc(swagger *spec.Swagger) {
	swagger.SwaggerProps.SecurityDefinitions = spec.SecurityDefinitions(map[string]*spec.SecurityScheme{
		"X-REMOTE-USER": {
			SecuritySchemeProps: spec.SecuritySchemeProps{
				Type:        "apiKey",
				In:          "header",
				Name:        "X-Remote-User",
				Description: "username for authentication",
			},
		},
		"X-REMOTE-GROUP": {
			SecuritySchemeProps: spec.SecuritySchemeProps{
				Type:        "apiKey",
				In:          "header",
				Name:        "X-Remote-Group",
				Description: "group for authentication",
			},
		},
	})
	swagger.SwaggerProps.Security = []map[string][]string{
		{"X-REMOTE-USER": {}, "X-REMOTE-GROUP": {}},
	}
}
