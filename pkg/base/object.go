package base

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/store"
)

func ObjectMetaFrom(obj client.Object) store.ObjectMeta {
	return store.ObjectMeta{
		ID:                obj.GetName(),
		Name:              GetMap(obj.GetAnnotations(), "name", obj.GetName()),
		UID:               string(obj.GetUID()),
		Description:       GetMap(obj.GetAnnotations(), "description", ""),
		CreationTimestamp: obj.GetCreationTimestamp(),
		DeletionTimestamp: obj.GetDeletionTimestamp(),
		Labels:            obj.GetLabels(),
		Annotations:       obj.GetAnnotations(),
		Finalizers:        obj.GetFinalizers(),
	}
}

func ObjectMetaTo(meta store.Object) metav1.ObjectMeta {
	annotations := meta.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations["description"] = meta.GetDescription()
	annotations["name"] = meta.GetName()
	return metav1.ObjectMeta{
		Name:              meta.GetName(),
		CreationTimestamp: meta.GetCreationTimestamp(),
		DeletionTimestamp: meta.GetDeletionTimestamp(),
		Labels:            meta.GetLabels(),
		Annotations:       annotations,
	}
}

func GetMap(kvs map[string]string, key string, def string) string {
	if kvs == nil {
		return def
	}
	if val, ok := kvs[key]; ok {
		return val
	}
	return def
}
