package base

import (
	"context"
	"fmt"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

func RandName(prefix string) string {
	return prefix + "-" + rand.RandomAlphaNumeric(8)
}

func ListOptionsToStoreListOptions(opts api.ListOptions) []store.ListOption {
	listOpts := []store.ListOption{}
	if opts.Size > 0 {
		listOpts = append(listOpts, store.WithPageSize(opts.Page, opts.Size))
	}
	if opts.Sort != "" {
		listOpts = append(listOpts, store.WithSort(opts.Sort))
	}
	if opts.Search != "" {
		listOpts = append(listOpts, store.WithSearch(opts.Search))
	}
	return listOpts
}

type ReQueueError struct {
	After time.Duration
}

func (r ReQueueError) Error() string {
	return fmt.Sprintf("retry after %s", r.After)
}

func ReQueue(after time.Duration) error {
	return ReQueueError{After: after}
}

func UnwrapReQueueError(err error) (controller.Result, error) {
	if err == nil {
		return controller.Result{}, nil
	}
	if requeue, ok := err.(ReQueueError); ok {
		return controller.Result{
			Requeue:      true,
			RequeueAfter: requeue.After,
		}, nil
	}
	return controller.Result{}, err
}

type ScopeHandler[T store.Object] struct {
	On                                            func(ctx context.Context, obj T) (controller.Result, error)
	OnTenant                                      func(ctx context.Context, tenant string, obj T) (controller.Result, error)
	OnCluster                                     func(ctx context.Context, cluster string, obj T) (controller.Result, error)
	OnTenantCluster                               func(ctx context.Context, tenant, cluster string, obj T) (controller.Result, error)
	OnTenantWorkspace                             func(ctx context.Context, tenant, workspace string, obj T) (controller.Result, error)
	OnTenantWorkspaceApplication                  func(ctx context.Context, tenant, workspace, app string, obj T) (controller.Result, error)
	OnTenantOrganization                          func(ctx context.Context, tenant, org string, obj T) (controller.Result, error)
	OnTenantOrganizationApplication               func(ctx context.Context, tenant, org, app string, obj T) (controller.Result, error)
	OnTenantOrganizationApplicationSubApplication func(ctx context.Context, tenant, org, app, subapp string, obj T) (controller.Result, error)
}

func DispatchOnScopes[T store.Object](ctx context.Context, obj T, handler ScopeHandler[T]) (controller.Result, error) {
	scopes := obj.GetScopes()
	if len(scopes) == 0 {
		if handler.On != nil {
			return handler.On(ctx, obj)
		}
		return controller.Result{}, nil
	}
	scope, scopes := scopes[0], scopes[1:]
	switch scope.Resource {
	case "tenants":
		tenantname := scope.Name
		if len(scopes) == 0 {
			if handler.OnTenant != nil {
				return handler.OnTenant(ctx, tenantname, obj)
			}
			return controller.Result{}, nil

		}
		scope, scopes = scopes[0], scopes[1:]
		switch scope.Resource {
		case "clusters":
			clusterName := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantCluster != nil {
					return handler.OnTenantCluster(ctx, tenantname, clusterName, obj)
				}
				return controller.Result{}, nil
			}
		case "workspaces":
			workspace := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantWorkspace != nil {
					return handler.OnTenantWorkspace(ctx, tenantname, workspace, obj)
				}
				return controller.Result{}, nil
			}
		case "organizations":
			orgname := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantOrganization != nil {
					return handler.OnTenantOrganization(ctx, tenantname, orgname, obj)
				}
				return controller.Result{}, nil
			}
			scope, scopes = scopes[0], scopes[1:]
			switch scope.Resource {
			case "applications":
				appname := scope.Name
				if len(scopes) == 0 {
					if handler.OnTenantOrganizationApplication != nil {
						return handler.OnTenantOrganizationApplication(ctx, tenantname, orgname, appname, obj)
					}
					return controller.Result{}, nil
				}
				scope, scopes = scopes[0], scopes[1:]
				switch scope.Resource {
				case "applications":
					subappname := scope.Name
					if len(scopes) == 0 {
						if handler.OnTenantOrganizationApplicationSubApplication != nil {
							return handler.OnTenantOrganizationApplicationSubApplication(ctx, tenantname, orgname, appname, subappname, obj)
						}
						return controller.Result{}, nil
					}
				}
			case "workspaces":
				workspace := scope.Name
				if len(scopes) == 0 {
					if handler.OnTenantWorkspace != nil {
						return handler.OnTenantWorkspace(ctx, tenantname, workspace, obj)
					}
					return controller.Result{}, nil
				}
				scope, scopes = scopes[0], scopes[1:]
				switch scope.Resource {
				case "applications":
					appname := scope.Name
					if len(scopes) == 0 {
						if handler.OnTenantWorkspaceApplication != nil {
							return handler.OnTenantWorkspaceApplication(ctx, tenantname, orgname, appname, obj)
						}
						return controller.Result{}, nil
					}
				}
			}
		}
	case "clusters":
		clusterName := scope.Name
		if handler.OnCluster != nil {
			return handler.OnCluster(ctx, clusterName, obj)
		}
		return controller.Result{}, nil
	}
	return controller.Result{}, nil
}

func InjectAttrName(ctx context.Context, name string) {
	attributes := api.AttributesFromContext(ctx)
	if len(attributes.Resources) == 0 {
		return
	}
	attributes.Resources[len(attributes.Resources)-1].Name = name
}

func Def[T comparable](val, def T) T {
	var empty T
	if val == empty {
		return def
	}
	return val
}
