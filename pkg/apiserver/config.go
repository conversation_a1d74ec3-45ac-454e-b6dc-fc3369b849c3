package apiserver

import (
	"fmt"
	"os"

	"sigs.k8s.io/yaml"
	"xiaoshiai.cn/common/log"
)

var DefaultServicesConfig = &ServicesConfig{
	BaseHost: "xiaoshiai.cn",
	Services: map[string]ServiceConfig{
		"iam": {
			Address: "http://rune-iam",
			APIPath: "/v1",
			PublicPaths: []string{
				"/login",
				"/register",
				"/forgot-password",
				"/logout",
				"/login-config",
				"/send-code",
				"/captcha",
				"/current",
			},
		},
		"cloud": {
			Address: "http://rune-cloud-api",
			APIPath: "/v1",
		},
		"xpai": {
			Address: "http://rune-xpai-api",
			APIPath: "/v1",
		},
	},
}

type ServicesConfig struct {
	BaseHost string                   `yaml:"baseHost,omitempty"` // base host for all services, used to construct service addresses
	Services map[string]ServiceConfig `yaml:"services"`
}

type ServiceConfig struct {
	Address     string   `yaml:"address"`
	APIPath     string   `yaml:"apiPath,omitempty"`     // API path for the service, defaults to "/v1"
	OpenapiPath string   `yaml:"openapiPath,omitempty"` // OpenAPI path for the service, defaults to "/docs/openapi.json"
	Timeout     string   `yaml:"timeout"`
	PublicPaths []string `yaml:"publicPaths"`
}

func LoadServicesConfig(configFile string) (*ServicesConfig, error) {
	log.Info("loading services config", "file", configFile)
	content, err := os.ReadFile(configFile)
	if err != nil {
		if os.IsNotExist(err) {
			log.Info("services config file does not exist, using default config", "file", configFile)
			return DefaultServicesConfig, nil // return default config if file does not exist
		}
		return nil, fmt.Errorf("read config file %s: %w", configFile, err)
	}
	config := &ServicesConfig{}
	if err := yaml.Unmarshal(content, &config); err != nil {
		return nil, fmt.Errorf("unmarshal config file %s: %w", configFile, err)
	}
	return config, nil
}
